!function(){var e={795:function(e,t,r){!function(e){"use strict";function t(t){return function(r,n){var i=n.line,o=r.getLine(i);function l(t){for(var l,a=n.ch,s=0;;){var u=a<=0?-1:o.lastIndexOf(t[0],a-1);if(-1!=u){if(1==s&&u<n.ch)break;if(l=r.getTokenTypeAt(e.Pos(i,u+1)),!/^(comment|string)/.test(l))return{ch:u+1,tokenType:l,pair:t};a=u-1}else{if(1==s)break;s=1,a=o.length}}}function a(t){var n,o,l=1,a=r.lastLine(),s=t.ch;e:for(var u=i;u<=a;++u)for(var c=r.getLine(u),f=u==i?s:0;;){var d=c.indexOf(t.pair[0],f),h=c.indexOf(t.pair[1],f);if(d<0&&(d=c.length),h<0&&(h=c.length),(f=Math.min(d,h))==c.length)break;if(r.getTokenTypeAt(e.Pos(u,f+1))==t.tokenType)if(f==d)++l;else if(! --l){n=u,o=f;break e}++f}return null==n||i==n?null:{from:e.Pos(i,s),to:e.Pos(n,o)}}for(var s=[],u=0;u<t.length;u++){var c=l(t[u]);c&&s.push(c)}for(s.sort((function(e,t){return e.ch-t.ch})),u=0;u<s.length;u++){var f=a(s[u]);if(f)return f}return null}}e.registerHelper("fold","brace",t([["{","}"],["[","]"]])),e.registerHelper("fold","brace-paren",t([["{","}"],["[","]"],["(",")"]])),e.registerHelper("fold","import",(function(t,r){function n(r){if(r<t.firstLine()||r>t.lastLine())return null;var n=t.getTokenAt(e.Pos(r,1));if(/\S/.test(n.string)||(n=t.getTokenAt(e.Pos(r,n.end+1))),"keyword"!=n.type||"import"!=n.string)return null;for(var i=r,o=Math.min(t.lastLine(),r+10);i<=o;++i){var l=t.getLine(i).indexOf(";");if(-1!=l)return{startCh:n.end,end:e.Pos(i,l)}}}var i,o=r.line,l=n(o);if(!l||n(o-1)||(i=n(o-2))&&i.end.line==o-1)return null;for(var a=l.end;;){var s=n(a.line+1);if(null==s)break;a=s.end}return{from:t.clipPos(e.Pos(o,l.startCh+1)),to:a}})),e.registerHelper("fold","include",(function(t,r){function n(r){if(r<t.firstLine()||r>t.lastLine())return null;var n=t.getTokenAt(e.Pos(r,1));return/\S/.test(n.string)||(n=t.getTokenAt(e.Pos(r,n.end+1))),"meta"==n.type&&"#include"==n.string.slice(0,8)?n.start+8:void 0}var i=r.line,o=n(i);if(null==o||null!=n(i-1))return null;for(var l=i;null!=n(l+1);)++l;return{from:e.Pos(i,o+1),to:t.clipPos(e.Pos(l))}}))}(r(237))},948:function(e,t,r){!function(e){"use strict";function t(t,n,o,l){if(o&&o.call){var a=o;o=null}else a=i(t,o,"rangeFinder");"number"==typeof n&&(n=e.Pos(n,0));var s=i(t,o,"minFoldSize");function u(e){var r=a(t,n);if(!r||r.to.line-r.from.line<s)return null;if("fold"===l)return r;for(var i=t.findMarksAt(r.from),o=0;o<i.length;++o)if(i[o].__isFold){if(!e)return null;r.cleared=!0,i[o].clear()}return r}var c=u(!0);if(i(t,o,"scanUp"))for(;!c&&n.line>t.firstLine();)n=e.Pos(n.line-1,0),c=u(!1);if(c&&!c.cleared&&"unfold"!==l){var f=r(t,o,c);e.on(f,"mousedown",(function(t){d.clear(),e.e_preventDefault(t)}));var d=t.markText(c.from,c.to,{replacedWith:f,clearOnEnter:i(t,o,"clearOnEnter"),__isFold:!0});d.on("clear",(function(r,n){e.signal(t,"unfold",t,r,n)})),e.signal(t,"fold",t,c.from,c.to)}}function r(e,t,r){var n=i(e,t,"widget");if("function"==typeof n&&(n=n(r.from,r.to)),"string"==typeof n){var o=document.createTextNode(n);(n=document.createElement("span")).appendChild(o),n.className="CodeMirror-foldmarker"}else n&&(n=n.cloneNode(!0));return n}e.newFoldFunction=function(e,r){return function(n,i){t(n,i,{rangeFinder:e,widget:r})}},e.defineExtension("foldCode",(function(e,r,n){t(this,e,r,n)})),e.defineExtension("isFolded",(function(e){for(var t=this.findMarksAt(e),r=0;r<t.length;++r)if(t[r].__isFold)return!0})),e.commands.toggleFold=function(e){e.foldCode(e.getCursor())},e.commands.fold=function(e){e.foldCode(e.getCursor(),null,"fold")},e.commands.unfold=function(e){e.foldCode(e.getCursor(),{scanUp:!1},"unfold")},e.commands.foldAll=function(t){t.operation((function(){for(var r=t.firstLine(),n=t.lastLine();r<=n;r++)t.foldCode(e.Pos(r,0),{scanUp:!1},"fold")}))},e.commands.unfoldAll=function(t){t.operation((function(){for(var r=t.firstLine(),n=t.lastLine();r<=n;r++)t.foldCode(e.Pos(r,0),{scanUp:!1},"unfold")}))},e.registerHelper("fold","combine",(function(){var e=Array.prototype.slice.call(arguments,0);return function(t,r){for(var n=0;n<e.length;++n){var i=e[n](t,r);if(i)return i}}})),e.registerHelper("fold","auto",(function(e,t){for(var r=e.getHelpers(t,"fold"),n=0;n<r.length;n++){var i=r[n](e,t);if(i)return i}}));var n={rangeFinder:e.fold.auto,widget:"↔",minFoldSize:0,scanUp:!1,clearOnEnter:!0};function i(e,t,r){if(t&&void 0!==t[r])return t[r];var i=e.options.foldOptions;return i&&void 0!==i[r]?i[r]:n[r]}e.defineOption("foldOptions",null),e.defineExtension("foldOption",(function(e,t){return i(this,e,t)}))}(r(237))},274:function(e,t,r){!function(e){"use strict";e.defineOption("foldGutter",!1,(function(t,i,o){o&&o!=e.Init&&(t.clearGutter(t.state.foldGutter.options.gutter),t.state.foldGutter=null,t.off("gutterClick",u),t.off("changes",f),t.off("viewportChange",d),t.off("fold",h),t.off("unfold",h),t.off("swapDoc",f),t.off("optionChange",c)),i&&(t.state.foldGutter=new r(n(i)),s(t),t.on("gutterClick",u),t.on("changes",f),t.on("viewportChange",d),t.on("fold",h),t.on("unfold",h),t.on("swapDoc",f),t.on("optionChange",c))}));var t=e.Pos;function r(e){this.options=e,this.from=this.to=0}function n(e){return!0===e&&(e={}),null==e.gutter&&(e.gutter="CodeMirror-foldgutter"),null==e.indicatorOpen&&(e.indicatorOpen="CodeMirror-foldgutter-open"),null==e.indicatorFolded&&(e.indicatorFolded="CodeMirror-foldgutter-folded"),e}function i(e,r){for(var n=e.findMarks(t(r,0),t(r+1,0)),i=0;i<n.length;++i)if(n[i].__isFold){var o=n[i].find(-1);if(o&&o.line===r)return n[i]}}function o(e){if("string"==typeof e){var t=document.createElement("div");return t.className=e+" CodeMirror-guttermarker-subtle",t}return e.cloneNode(!0)}function l(e,r,n){var l=e.state.foldGutter.options,s=r-1,u=e.foldOption(l,"minFoldSize"),c=e.foldOption(l,"rangeFinder"),f="string"==typeof l.indicatorFolded&&a(l.indicatorFolded),d="string"==typeof l.indicatorOpen&&a(l.indicatorOpen);e.eachLine(r,n,(function(r){++s;var n=null,a=r.gutterMarkers;if(a&&(a=a[l.gutter]),i(e,s)){if(f&&a&&f.test(a.className))return;n=o(l.indicatorFolded)}else{var h=t(s,0),p=c&&c(e,h);if(p&&p.to.line-p.from.line>=u){if(d&&a&&d.test(a.className))return;n=o(l.indicatorOpen)}}(n||a)&&e.setGutterMarker(r,l.gutter,n)}))}function a(e){return new RegExp("(^|\\s)"+e+"(?:$|\\s)\\s*")}function s(e){var t=e.getViewport(),r=e.state.foldGutter;r&&(e.operation((function(){l(e,t.from,t.to)})),r.from=t.from,r.to=t.to)}function u(e,r,n){var o=e.state.foldGutter;if(o){var l=o.options;if(n==l.gutter){var a=i(e,r);a?a.clear():e.foldCode(t(r,0),l)}}}function c(e,t){"mode"==t&&f(e)}function f(e){var t=e.state.foldGutter;if(t){var r=t.options;t.from=t.to=0,clearTimeout(t.changeUpdate),t.changeUpdate=setTimeout((function(){s(e)}),r.foldOnChangeTimeSpan||600)}}function d(e){var t=e.state.foldGutter;if(t){var r=t.options;clearTimeout(t.changeUpdate),t.changeUpdate=setTimeout((function(){var r=e.getViewport();t.from==t.to||r.from-t.to>20||t.from-r.to>20?s(e):e.operation((function(){r.from<t.from&&(l(e,r.from,t.from),t.from=r.from),r.to>t.to&&(l(e,t.to,r.to),t.to=r.to)}))}),r.updateViewportTimeSpan||400)}}function h(e,t){var r=e.state.foldGutter;if(r){var n=t.line;n>=r.from&&n<r.to&&l(e,n,n+1)}}}(r(237),r(948))},237:function(e){e.exports=function(){"use strict";var e=navigator.userAgent,t=navigator.platform,r=/gecko\/\d/i.test(e),n=/MSIE \d/.test(e),i=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(e),o=/Edge\/(\d+)/.exec(e),l=n||i||o,a=l&&(n?document.documentMode||6:+(o||i)[1]),s=!o&&/WebKit\//.test(e),u=s&&/Qt\/\d+\.\d+/.test(e),c=!o&&/Chrome\/(\d+)/.exec(e),f=c&&+c[1],d=/Opera\//.test(e),h=/Apple Computer/.test(navigator.vendor),p=/Mac OS X 1\d\D([8-9]|\d\d)\D/.test(e),g=/PhantomJS/.test(e),v=h&&(/Mobile\/\w+/.test(e)||navigator.maxTouchPoints>2),m=/Android/.test(e),y=v||m||/webOS|BlackBerry|Opera Mini|Opera Mobi|IEMobile/i.test(e),b=v||/Mac/.test(t),w=/\bCrOS\b/.test(e),x=/win/i.test(t),C=d&&e.match(/Version\/(\d*\.\d*)/);C&&(C=Number(C[1])),C&&C>=15&&(d=!1,s=!0);var k=b&&(u||d&&(null==C||C<12.11)),S=r||l&&a>=9;function L(e){return new RegExp("(^|\\s)"+e+"(?:$|\\s)\\s*")}var T,M=function(e,t){var r=e.className,n=L(t).exec(r);if(n){var i=r.slice(n.index+n[0].length);e.className=r.slice(0,n.index)+(i?n[1]+i:"")}};function N(e){for(var t=e.childNodes.length;t>0;--t)e.removeChild(e.firstChild);return e}function O(e,t){return N(e).appendChild(t)}function A(e,t,r,n){var i=document.createElement(e);if(r&&(i.className=r),n&&(i.style.cssText=n),"string"==typeof t)i.appendChild(document.createTextNode(t));else if(t)for(var o=0;o<t.length;++o)i.appendChild(t[o]);return i}function D(e,t,r,n){var i=A(e,t,r,n);return i.setAttribute("role","presentation"),i}function W(e,t){if(3==t.nodeType&&(t=t.parentNode),e.contains)return e.contains(t);do{if(11==t.nodeType&&(t=t.host),t==e)return!0}while(t=t.parentNode)}function H(e){var t;try{t=e.activeElement}catch(r){t=e.body||null}for(;t&&t.shadowRoot&&t.shadowRoot.activeElement;)t=t.shadowRoot.activeElement;return t}function F(e,t){var r=e.className;L(t).test(r)||(e.className+=(r?" ":"")+t)}function E(e,t){for(var r=e.split(" "),n=0;n<r.length;n++)r[n]&&!L(r[n]).test(t)&&(t+=" "+r[n]);return t}T=document.createRange?function(e,t,r,n){var i=document.createRange();return i.setEnd(n||e,r),i.setStart(e,t),i}:function(e,t,r){var n=document.body.createTextRange();try{n.moveToElementText(e.parentNode)}catch(e){return n}return n.collapse(!0),n.moveEnd("character",r),n.moveStart("character",t),n};var P=function(e){e.select()};function z(e){return e.display.wrapper.ownerDocument}function I(e){return z(e).defaultView}function R(e){var t=Array.prototype.slice.call(arguments,1);return function(){return e.apply(null,t)}}function B(e,t,r){for(var n in t||(t={}),e)!e.hasOwnProperty(n)||!1===r&&t.hasOwnProperty(n)||(t[n]=e[n]);return t}function V(e,t,r,n,i){null==t&&-1==(t=e.search(/[^\s\u00a0]/))&&(t=e.length);for(var o=n||0,l=i||0;;){var a=e.indexOf("\t",o);if(a<0||a>=t)return l+(t-o);l+=a-o,l+=r-l%r,o=a+1}}v?P=function(e){e.selectionStart=0,e.selectionEnd=e.value.length}:l&&(P=function(e){try{e.select()}catch(e){}});var G=function(){this.id=null,this.f=null,this.time=0,this.handler=R(this.onTimeout,this)};function j(e,t){for(var r=0;r<e.length;++r)if(e[r]==t)return r;return-1}G.prototype.onTimeout=function(e){e.id=0,e.time<=+new Date?e.f():setTimeout(e.handler,e.time-+new Date)},G.prototype.set=function(e,t){this.f=t;var r=+new Date+e;(!this.id||r<this.time)&&(clearTimeout(this.id),this.id=setTimeout(this.handler,e),this.time=r)};var U=50,K={toString:function(){return"CodeMirror.Pass"}},_={scroll:!1},X={origin:"*mouse"},$={origin:"+move"};function Y(e,t,r){for(var n=0,i=0;;){var o=e.indexOf("\t",n);-1==o&&(o=e.length);var l=o-n;if(o==e.length||i+l>=t)return n+Math.min(l,t-i);if(i+=o-n,n=o+1,(i+=r-i%r)>=t)return n}}var q=[""];function Z(e){for(;q.length<=e;)q.push(J(q)+" ");return q[e]}function J(e){return e[e.length-1]}function Q(e,t){for(var r=[],n=0;n<e.length;n++)r[n]=t(e[n],n);return r}function ee(e,t,r){for(var n=0,i=r(t);n<e.length&&r(e[n])<=i;)n++;e.splice(n,0,t)}function te(){}function re(e,t){var r;return Object.create?r=Object.create(e):(te.prototype=e,r=new te),t&&B(t,r),r}var ne=/[\u00df\u0587\u0590-\u05f4\u0600-\u06ff\u3040-\u309f\u30a0-\u30ff\u3400-\u4db5\u4e00-\u9fcc\uac00-\ud7af]/;function ie(e){return/\w/.test(e)||e>""&&(e.toUpperCase()!=e.toLowerCase()||ne.test(e))}function oe(e,t){return t?!!(t.source.indexOf("\\w")>-1&&ie(e))||t.test(e):ie(e)}function le(e){for(var t in e)if(e.hasOwnProperty(t)&&e[t])return!1;return!0}var ae=/[\u0300-\u036f\u0483-\u0489\u0591-\u05bd\u05bf\u05c1\u05c2\u05c4\u05c5\u05c7\u0610-\u061a\u064b-\u065e\u0670\u06d6-\u06dc\u06de-\u06e4\u06e7\u06e8\u06ea-\u06ed\u0711\u0730-\u074a\u07a6-\u07b0\u07eb-\u07f3\u0816-\u0819\u081b-\u0823\u0825-\u0827\u0829-\u082d\u0900-\u0902\u093c\u0941-\u0948\u094d\u0951-\u0955\u0962\u0963\u0981\u09bc\u09be\u09c1-\u09c4\u09cd\u09d7\u09e2\u09e3\u0a01\u0a02\u0a3c\u0a41\u0a42\u0a47\u0a48\u0a4b-\u0a4d\u0a51\u0a70\u0a71\u0a75\u0a81\u0a82\u0abc\u0ac1-\u0ac5\u0ac7\u0ac8\u0acd\u0ae2\u0ae3\u0b01\u0b3c\u0b3e\u0b3f\u0b41-\u0b44\u0b4d\u0b56\u0b57\u0b62\u0b63\u0b82\u0bbe\u0bc0\u0bcd\u0bd7\u0c3e-\u0c40\u0c46-\u0c48\u0c4a-\u0c4d\u0c55\u0c56\u0c62\u0c63\u0cbc\u0cbf\u0cc2\u0cc6\u0ccc\u0ccd\u0cd5\u0cd6\u0ce2\u0ce3\u0d3e\u0d41-\u0d44\u0d4d\u0d57\u0d62\u0d63\u0dca\u0dcf\u0dd2-\u0dd4\u0dd6\u0ddf\u0e31\u0e34-\u0e3a\u0e47-\u0e4e\u0eb1\u0eb4-\u0eb9\u0ebb\u0ebc\u0ec8-\u0ecd\u0f18\u0f19\u0f35\u0f37\u0f39\u0f71-\u0f7e\u0f80-\u0f84\u0f86\u0f87\u0f90-\u0f97\u0f99-\u0fbc\u0fc6\u102d-\u1030\u1032-\u1037\u1039\u103a\u103d\u103e\u1058\u1059\u105e-\u1060\u1071-\u1074\u1082\u1085\u1086\u108d\u109d\u135f\u1712-\u1714\u1732-\u1734\u1752\u1753\u1772\u1773\u17b7-\u17bd\u17c6\u17c9-\u17d3\u17dd\u180b-\u180d\u18a9\u1920-\u1922\u1927\u1928\u1932\u1939-\u193b\u1a17\u1a18\u1a56\u1a58-\u1a5e\u1a60\u1a62\u1a65-\u1a6c\u1a73-\u1a7c\u1a7f\u1b00-\u1b03\u1b34\u1b36-\u1b3a\u1b3c\u1b42\u1b6b-\u1b73\u1b80\u1b81\u1ba2-\u1ba5\u1ba8\u1ba9\u1c2c-\u1c33\u1c36\u1c37\u1cd0-\u1cd2\u1cd4-\u1ce0\u1ce2-\u1ce8\u1ced\u1dc0-\u1de6\u1dfd-\u1dff\u200c\u200d\u20d0-\u20f0\u2cef-\u2cf1\u2de0-\u2dff\u302a-\u302f\u3099\u309a\ua66f-\ua672\ua67c\ua67d\ua6f0\ua6f1\ua802\ua806\ua80b\ua825\ua826\ua8c4\ua8e0-\ua8f1\ua926-\ua92d\ua947-\ua951\ua980-\ua982\ua9b3\ua9b6-\ua9b9\ua9bc\uaa29-\uaa2e\uaa31\uaa32\uaa35\uaa36\uaa43\uaa4c\uaab0\uaab2-\uaab4\uaab7\uaab8\uaabe\uaabf\uaac1\uabe5\uabe8\uabed\udc00-\udfff\ufb1e\ufe00-\ufe0f\ufe20-\ufe26\uff9e\uff9f]/;function se(e){return e.charCodeAt(0)>=768&&ae.test(e)}function ue(e,t,r){for(;(r<0?t>0:t<e.length)&&se(e.charAt(t));)t+=r;return t}function ce(e,t,r){for(var n=t>r?-1:1;;){if(t==r)return t;var i=(t+r)/2,o=n<0?Math.ceil(i):Math.floor(i);if(o==t)return e(o)?t:r;e(o)?r=o:t=o+n}}function fe(e,t,r,n){if(!e)return n(t,r,"ltr",0);for(var i=!1,o=0;o<e.length;++o){var l=e[o];(l.from<r&&l.to>t||t==r&&l.to==t)&&(n(Math.max(l.from,t),Math.min(l.to,r),1==l.level?"rtl":"ltr",o),i=!0)}i||n(t,r,"ltr")}var de=null;function he(e,t,r){var n;de=null;for(var i=0;i<e.length;++i){var o=e[i];if(o.from<t&&o.to>t)return i;o.to==t&&(o.from!=o.to&&"before"==r?n=i:de=i),o.from==t&&(o.from!=o.to&&"before"!=r?n=i:de=i)}return null!=n?n:de}var pe=function(){var e="bbbbbbbbbtstwsbbbbbbbbbbbbbbssstwNN%%%NNNNNN,N,N1111111111NNNNNNNLLLLLLLLLLLLLLLLLLLLLLLLLLNNNNNNLLLLLLLLLLLLLLLLLLLLLLLLLLNNNNbbbbbbsbbbbbbbbbbbbbbbbbbbbbbbbbb,N%%%%NNNNLNNNNN%%11NLNNN1LNNNNNLLLLLLLLLLLLLLLLLLLLLLLNLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLN",t="nnnnnnNNr%%r,rNNmmmmmmmmmmmrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrmmmmmmmmmmmmmmmmmmmmmnnnnnnnnnn%nnrrrmrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrmmmmmmmnNmmmmmmrrmmNmmmmrr1111111111";function r(r){return r<=247?e.charAt(r):1424<=r&&r<=1524?"R":1536<=r&&r<=1785?t.charAt(r-1536):1774<=r&&r<=2220?"r":8192<=r&&r<=8203?"w":8204==r?"b":"L"}var n=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac]/,i=/[stwN]/,o=/[LRr]/,l=/[Lb1n]/,a=/[1n]/;function s(e,t,r){this.level=e,this.from=t,this.to=r}return function(e,t){var u="ltr"==t?"L":"R";if(0==e.length||"ltr"==t&&!n.test(e))return!1;for(var c=e.length,f=[],d=0;d<c;++d)f.push(r(e.charCodeAt(d)));for(var h=0,p=u;h<c;++h){var g=f[h];"m"==g?f[h]=p:p=g}for(var v=0,m=u;v<c;++v){var y=f[v];"1"==y&&"r"==m?f[v]="n":o.test(y)&&(m=y,"r"==y&&(f[v]="R"))}for(var b=1,w=f[0];b<c-1;++b){var x=f[b];"+"==x&&"1"==w&&"1"==f[b+1]?f[b]="1":","!=x||w!=f[b+1]||"1"!=w&&"n"!=w||(f[b]=w),w=x}for(var C=0;C<c;++C){var k=f[C];if(","==k)f[C]="N";else if("%"==k){var S=void 0;for(S=C+1;S<c&&"%"==f[S];++S);for(var L=C&&"!"==f[C-1]||S<c&&"1"==f[S]?"1":"N",T=C;T<S;++T)f[T]=L;C=S-1}}for(var M=0,N=u;M<c;++M){var O=f[M];"L"==N&&"1"==O?f[M]="L":o.test(O)&&(N=O)}for(var A=0;A<c;++A)if(i.test(f[A])){var D=void 0;for(D=A+1;D<c&&i.test(f[D]);++D);for(var W="L"==(A?f[A-1]:u),H=W==("L"==(D<c?f[D]:u))?W?"L":"R":u,F=A;F<D;++F)f[F]=H;A=D-1}for(var E,P=[],z=0;z<c;)if(l.test(f[z])){var I=z;for(++z;z<c&&l.test(f[z]);++z);P.push(new s(0,I,z))}else{var R=z,B=P.length,V="rtl"==t?1:0;for(++z;z<c&&"L"!=f[z];++z);for(var G=R;G<z;)if(a.test(f[G])){R<G&&(P.splice(B,0,new s(1,R,G)),B+=V);var j=G;for(++G;G<z&&a.test(f[G]);++G);P.splice(B,0,new s(2,j,G)),B+=V,R=G}else++G;R<z&&P.splice(B,0,new s(1,R,z))}return"ltr"==t&&(1==P[0].level&&(E=e.match(/^\s+/))&&(P[0].from=E[0].length,P.unshift(new s(0,0,E[0].length))),1==J(P).level&&(E=e.match(/\s+$/))&&(J(P).to-=E[0].length,P.push(new s(0,c-E[0].length,c)))),"rtl"==t?P.reverse():P}}();function ge(e,t){var r=e.order;return null==r&&(r=e.order=pe(e.text,t)),r}var ve=[],me=function(e,t,r){if(e.addEventListener)e.addEventListener(t,r,!1);else if(e.attachEvent)e.attachEvent("on"+t,r);else{var n=e._handlers||(e._handlers={});n[t]=(n[t]||ve).concat(r)}};function ye(e,t){return e._handlers&&e._handlers[t]||ve}function be(e,t,r){if(e.removeEventListener)e.removeEventListener(t,r,!1);else if(e.detachEvent)e.detachEvent("on"+t,r);else{var n=e._handlers,i=n&&n[t];if(i){var o=j(i,r);o>-1&&(n[t]=i.slice(0,o).concat(i.slice(o+1)))}}}function we(e,t){var r=ye(e,t);if(r.length)for(var n=Array.prototype.slice.call(arguments,2),i=0;i<r.length;++i)r[i].apply(null,n)}function xe(e,t,r){return"string"==typeof t&&(t={type:t,preventDefault:function(){this.defaultPrevented=!0}}),we(e,r||t.type,e,t),Me(t)||t.codemirrorIgnore}function Ce(e){var t=e._handlers&&e._handlers.cursorActivity;if(t)for(var r=e.curOp.cursorActivityHandlers||(e.curOp.cursorActivityHandlers=[]),n=0;n<t.length;++n)-1==j(r,t[n])&&r.push(t[n])}function ke(e,t){return ye(e,t).length>0}function Se(e){e.prototype.on=function(e,t){me(this,e,t)},e.prototype.off=function(e,t){be(this,e,t)}}function Le(e){e.preventDefault?e.preventDefault():e.returnValue=!1}function Te(e){e.stopPropagation?e.stopPropagation():e.cancelBubble=!0}function Me(e){return null!=e.defaultPrevented?e.defaultPrevented:0==e.returnValue}function Ne(e){Le(e),Te(e)}function Oe(e){return e.target||e.srcElement}function Ae(e){var t=e.which;return null==t&&(1&e.button?t=1:2&e.button?t=3:4&e.button&&(t=2)),b&&e.ctrlKey&&1==t&&(t=3),t}var De,We,He=function(){if(l&&a<9)return!1;var e=A("div");return"draggable"in e||"dragDrop"in e}();function Fe(e){if(null==De){var t=A("span","​");O(e,A("span",[t,document.createTextNode("x")])),0!=e.firstChild.offsetHeight&&(De=t.offsetWidth<=1&&t.offsetHeight>2&&!(l&&a<8))}var r=De?A("span","​"):A("span"," ",null,"display: inline-block; width: 1px; margin-right: -1px");return r.setAttribute("cm-text",""),r}function Ee(e){if(null!=We)return We;var t=O(e,document.createTextNode("AخA")),r=T(t,0,1).getBoundingClientRect(),n=T(t,1,2).getBoundingClientRect();return N(e),!(!r||r.left==r.right)&&(We=n.right-r.right<3)}var Pe,ze=3!="\n\nb".split(/\n/).length?function(e){for(var t=0,r=[],n=e.length;t<=n;){var i=e.indexOf("\n",t);-1==i&&(i=e.length);var o=e.slice(t,"\r"==e.charAt(i-1)?i-1:i),l=o.indexOf("\r");-1!=l?(r.push(o.slice(0,l)),t+=l+1):(r.push(o),t=i+1)}return r}:function(e){return e.split(/\r\n?|\n/)},Ie=window.getSelection?function(e){try{return e.selectionStart!=e.selectionEnd}catch(e){return!1}}:function(e){var t;try{t=e.ownerDocument.selection.createRange()}catch(e){}return!(!t||t.parentElement()!=e)&&0!=t.compareEndPoints("StartToEnd",t)},Re="oncopy"in(Pe=A("div"))||(Pe.setAttribute("oncopy","return;"),"function"==typeof Pe.oncopy),Be=null;function Ve(e){if(null!=Be)return Be;var t=O(e,A("span","x")),r=t.getBoundingClientRect(),n=T(t,0,1).getBoundingClientRect();return Be=Math.abs(r.left-n.left)>1}var Ge={},je={};function Ue(e,t){arguments.length>2&&(t.dependencies=Array.prototype.slice.call(arguments,2)),Ge[e]=t}function Ke(e,t){je[e]=t}function _e(e){if("string"==typeof e&&je.hasOwnProperty(e))e=je[e];else if(e&&"string"==typeof e.name&&je.hasOwnProperty(e.name)){var t=je[e.name];"string"==typeof t&&(t={name:t}),(e=re(t,e)).name=t.name}else{if("string"==typeof e&&/^[\w\-]+\/[\w\-]+\+xml$/.test(e))return _e("application/xml");if("string"==typeof e&&/^[\w\-]+\/[\w\-]+\+json$/.test(e))return _e("application/json")}return"string"==typeof e?{name:e}:e||{name:"null"}}function Xe(e,t){t=_e(t);var r=Ge[t.name];if(!r)return Xe(e,"text/plain");var n=r(e,t);if($e.hasOwnProperty(t.name)){var i=$e[t.name];for(var o in i)i.hasOwnProperty(o)&&(n.hasOwnProperty(o)&&(n["_"+o]=n[o]),n[o]=i[o])}if(n.name=t.name,t.helperType&&(n.helperType=t.helperType),t.modeProps)for(var l in t.modeProps)n[l]=t.modeProps[l];return n}var $e={};function Ye(e,t){B(t,$e.hasOwnProperty(e)?$e[e]:$e[e]={})}function qe(e,t){if(!0===t)return t;if(e.copyState)return e.copyState(t);var r={};for(var n in t){var i=t[n];i instanceof Array&&(i=i.concat([])),r[n]=i}return r}function Ze(e,t){for(var r;e.innerMode&&(r=e.innerMode(t))&&r.mode!=e;)t=r.state,e=r.mode;return r||{mode:e,state:t}}function Je(e,t,r){return!e.startState||e.startState(t,r)}var Qe=function(e,t,r){this.pos=this.start=0,this.string=e,this.tabSize=t||8,this.lastColumnPos=this.lastColumnValue=0,this.lineStart=0,this.lineOracle=r};function et(e,t){if((t-=e.first)<0||t>=e.size)throw new Error("There is no line "+(t+e.first)+" in the document.");for(var r=e;!r.lines;)for(var n=0;;++n){var i=r.children[n],o=i.chunkSize();if(t<o){r=i;break}t-=o}return r.lines[t]}function tt(e,t,r){var n=[],i=t.line;return e.iter(t.line,r.line+1,(function(e){var o=e.text;i==r.line&&(o=o.slice(0,r.ch)),i==t.line&&(o=o.slice(t.ch)),n.push(o),++i})),n}function rt(e,t,r){var n=[];return e.iter(t,r,(function(e){n.push(e.text)})),n}function nt(e,t){var r=t-e.height;if(r)for(var n=e;n;n=n.parent)n.height+=r}function it(e){if(null==e.parent)return null;for(var t=e.parent,r=j(t.lines,e),n=t.parent;n;t=n,n=n.parent)for(var i=0;n.children[i]!=t;++i)r+=n.children[i].chunkSize();return r+t.first}function ot(e,t){var r=e.first;e:do{for(var n=0;n<e.children.length;++n){var i=e.children[n],o=i.height;if(t<o){e=i;continue e}t-=o,r+=i.chunkSize()}return r}while(!e.lines);for(var l=0;l<e.lines.length;++l){var a=e.lines[l].height;if(t<a)break;t-=a}return r+l}function lt(e,t){return t>=e.first&&t<e.first+e.size}function at(e,t){return String(e.lineNumberFormatter(t+e.firstLineNumber))}function st(e,t,r){if(void 0===r&&(r=null),!(this instanceof st))return new st(e,t,r);this.line=e,this.ch=t,this.sticky=r}function ut(e,t){return e.line-t.line||e.ch-t.ch}function ct(e,t){return e.sticky==t.sticky&&0==ut(e,t)}function ft(e){return st(e.line,e.ch)}function dt(e,t){return ut(e,t)<0?t:e}function ht(e,t){return ut(e,t)<0?e:t}function pt(e,t){return Math.max(e.first,Math.min(t,e.first+e.size-1))}function gt(e,t){if(t.line<e.first)return st(e.first,0);var r=e.first+e.size-1;return t.line>r?st(r,et(e,r).text.length):vt(t,et(e,t.line).text.length)}function vt(e,t){var r=e.ch;return null==r||r>t?st(e.line,t):r<0?st(e.line,0):e}function mt(e,t){for(var r=[],n=0;n<t.length;n++)r[n]=gt(e,t[n]);return r}Qe.prototype.eol=function(){return this.pos>=this.string.length},Qe.prototype.sol=function(){return this.pos==this.lineStart},Qe.prototype.peek=function(){return this.string.charAt(this.pos)||void 0},Qe.prototype.next=function(){if(this.pos<this.string.length)return this.string.charAt(this.pos++)},Qe.prototype.eat=function(e){var t=this.string.charAt(this.pos);if("string"==typeof e?t==e:t&&(e.test?e.test(t):e(t)))return++this.pos,t},Qe.prototype.eatWhile=function(e){for(var t=this.pos;this.eat(e););return this.pos>t},Qe.prototype.eatSpace=function(){for(var e=this.pos;/[\s\u00a0]/.test(this.string.charAt(this.pos));)++this.pos;return this.pos>e},Qe.prototype.skipToEnd=function(){this.pos=this.string.length},Qe.prototype.skipTo=function(e){var t=this.string.indexOf(e,this.pos);if(t>-1)return this.pos=t,!0},Qe.prototype.backUp=function(e){this.pos-=e},Qe.prototype.column=function(){return this.lastColumnPos<this.start&&(this.lastColumnValue=V(this.string,this.start,this.tabSize,this.lastColumnPos,this.lastColumnValue),this.lastColumnPos=this.start),this.lastColumnValue-(this.lineStart?V(this.string,this.lineStart,this.tabSize):0)},Qe.prototype.indentation=function(){return V(this.string,null,this.tabSize)-(this.lineStart?V(this.string,this.lineStart,this.tabSize):0)},Qe.prototype.match=function(e,t,r){if("string"!=typeof e){var n=this.string.slice(this.pos).match(e);return n&&n.index>0?null:(n&&!1!==t&&(this.pos+=n[0].length),n)}var i=function(e){return r?e.toLowerCase():e};if(i(this.string.substr(this.pos,e.length))==i(e))return!1!==t&&(this.pos+=e.length),!0},Qe.prototype.current=function(){return this.string.slice(this.start,this.pos)},Qe.prototype.hideFirstChars=function(e,t){this.lineStart+=e;try{return t()}finally{this.lineStart-=e}},Qe.prototype.lookAhead=function(e){var t=this.lineOracle;return t&&t.lookAhead(e)},Qe.prototype.baseToken=function(){var e=this.lineOracle;return e&&e.baseToken(this.pos)};var yt=function(e,t){this.state=e,this.lookAhead=t},bt=function(e,t,r,n){this.state=t,this.doc=e,this.line=r,this.maxLookAhead=n||0,this.baseTokens=null,this.baseTokenPos=1};function wt(e,t,r,n){var i=[e.state.modeGen],o={};Ot(e,t.text,e.doc.mode,r,(function(e,t){return i.push(e,t)}),o,n);for(var l=r.state,a=function(n){r.baseTokens=i;var a=e.state.overlays[n],s=1,u=0;r.state=!0,Ot(e,t.text,a.mode,r,(function(e,t){for(var r=s;u<e;){var n=i[s];n>e&&i.splice(s,1,e,i[s+1],n),s+=2,u=Math.min(e,n)}if(t)if(a.opaque)i.splice(r,s-r,e,"overlay "+t),s=r+2;else for(;r<s;r+=2){var o=i[r+1];i[r+1]=(o?o+" ":"")+"overlay "+t}}),o),r.state=l,r.baseTokens=null,r.baseTokenPos=1},s=0;s<e.state.overlays.length;++s)a(s);return{styles:i,classes:o.bgClass||o.textClass?o:null}}function xt(e,t,r){if(!t.styles||t.styles[0]!=e.state.modeGen){var n=Ct(e,it(t)),i=t.text.length>e.options.maxHighlightLength&&qe(e.doc.mode,n.state),o=wt(e,t,n);i&&(n.state=i),t.stateAfter=n.save(!i),t.styles=o.styles,o.classes?t.styleClasses=o.classes:t.styleClasses&&(t.styleClasses=null),r===e.doc.highlightFrontier&&(e.doc.modeFrontier=Math.max(e.doc.modeFrontier,++e.doc.highlightFrontier))}return t.styles}function Ct(e,t,r){var n=e.doc,i=e.display;if(!n.mode.startState)return new bt(n,!0,t);var o=At(e,t,r),l=o>n.first&&et(n,o-1).stateAfter,a=l?bt.fromSaved(n,l,o):new bt(n,Je(n.mode),o);return n.iter(o,t,(function(r){kt(e,r.text,a);var n=a.line;r.stateAfter=n==t-1||n%5==0||n>=i.viewFrom&&n<i.viewTo?a.save():null,a.nextLine()})),r&&(n.modeFrontier=a.line),a}function kt(e,t,r,n){var i=e.doc.mode,o=new Qe(t,e.options.tabSize,r);for(o.start=o.pos=n||0,""==t&&St(i,r.state);!o.eol();)Lt(i,o,r.state),o.start=o.pos}function St(e,t){if(e.blankLine)return e.blankLine(t);if(e.innerMode){var r=Ze(e,t);return r.mode.blankLine?r.mode.blankLine(r.state):void 0}}function Lt(e,t,r,n){for(var i=0;i<10;i++){n&&(n[0]=Ze(e,r).mode);var o=e.token(t,r);if(t.pos>t.start)return o}throw new Error("Mode "+e.name+" failed to advance stream.")}bt.prototype.lookAhead=function(e){var t=this.doc.getLine(this.line+e);return null!=t&&e>this.maxLookAhead&&(this.maxLookAhead=e),t},bt.prototype.baseToken=function(e){if(!this.baseTokens)return null;for(;this.baseTokens[this.baseTokenPos]<=e;)this.baseTokenPos+=2;var t=this.baseTokens[this.baseTokenPos+1];return{type:t&&t.replace(/( |^)overlay .*/,""),size:this.baseTokens[this.baseTokenPos]-e}},bt.prototype.nextLine=function(){this.line++,this.maxLookAhead>0&&this.maxLookAhead--},bt.fromSaved=function(e,t,r){return t instanceof yt?new bt(e,qe(e.mode,t.state),r,t.lookAhead):new bt(e,qe(e.mode,t),r)},bt.prototype.save=function(e){var t=!1!==e?qe(this.doc.mode,this.state):this.state;return this.maxLookAhead>0?new yt(t,this.maxLookAhead):t};var Tt=function(e,t,r){this.start=e.start,this.end=e.pos,this.string=e.current(),this.type=t||null,this.state=r};function Mt(e,t,r,n){var i,o,l=e.doc,a=l.mode,s=et(l,(t=gt(l,t)).line),u=Ct(e,t.line,r),c=new Qe(s.text,e.options.tabSize,u);for(n&&(o=[]);(n||c.pos<t.ch)&&!c.eol();)c.start=c.pos,i=Lt(a,c,u.state),n&&o.push(new Tt(c,i,qe(l.mode,u.state)));return n?o:new Tt(c,i,u.state)}function Nt(e,t){if(e)for(;;){var r=e.match(/(?:^|\s+)line-(background-)?(\S+)/);if(!r)break;e=e.slice(0,r.index)+e.slice(r.index+r[0].length);var n=r[1]?"bgClass":"textClass";null==t[n]?t[n]=r[2]:new RegExp("(?:^|\\s)"+r[2]+"(?:$|\\s)").test(t[n])||(t[n]+=" "+r[2])}return e}function Ot(e,t,r,n,i,o,l){var a=r.flattenSpans;null==a&&(a=e.options.flattenSpans);var s,u=0,c=null,f=new Qe(t,e.options.tabSize,n),d=e.options.addModeClass&&[null];for(""==t&&Nt(St(r,n.state),o);!f.eol();){if(f.pos>e.options.maxHighlightLength?(a=!1,l&&kt(e,t,n,f.pos),f.pos=t.length,s=null):s=Nt(Lt(r,f,n.state,d),o),d){var h=d[0].name;h&&(s="m-"+(s?h+" "+s:h))}if(!a||c!=s){for(;u<f.start;)i(u=Math.min(f.start,u+5e3),c);c=s}f.start=f.pos}for(;u<f.pos;){var p=Math.min(f.pos,u+5e3);i(p,c),u=p}}function At(e,t,r){for(var n,i,o=e.doc,l=r?-1:t-(e.doc.mode.innerMode?1e3:100),a=t;a>l;--a){if(a<=o.first)return o.first;var s=et(o,a-1),u=s.stateAfter;if(u&&(!r||a+(u instanceof yt?u.lookAhead:0)<=o.modeFrontier))return a;var c=V(s.text,null,e.options.tabSize);(null==i||n>c)&&(i=a-1,n=c)}return i}function Dt(e,t){if(e.modeFrontier=Math.min(e.modeFrontier,t),!(e.highlightFrontier<t-10)){for(var r=e.first,n=t-1;n>r;n--){var i=et(e,n).stateAfter;if(i&&(!(i instanceof yt)||n+i.lookAhead<t)){r=n+1;break}}e.highlightFrontier=Math.min(e.highlightFrontier,r)}}var Wt=!1,Ht=!1;function Ft(){Wt=!0}function Et(){Ht=!0}function Pt(e,t,r){this.marker=e,this.from=t,this.to=r}function zt(e,t){if(e)for(var r=0;r<e.length;++r){var n=e[r];if(n.marker==t)return n}}function It(e,t){for(var r,n=0;n<e.length;++n)e[n]!=t&&(r||(r=[])).push(e[n]);return r}function Rt(e,t,r){var n=r&&window.WeakSet&&(r.markedSpans||(r.markedSpans=new WeakSet));n&&e.markedSpans&&n.has(e.markedSpans)?e.markedSpans.push(t):(e.markedSpans=e.markedSpans?e.markedSpans.concat([t]):[t],n&&n.add(e.markedSpans)),t.marker.attachLine(e)}function Bt(e,t,r){var n;if(e)for(var i=0;i<e.length;++i){var o=e[i],l=o.marker;if(null==o.from||(l.inclusiveLeft?o.from<=t:o.from<t)||o.from==t&&"bookmark"==l.type&&(!r||!o.marker.insertLeft)){var a=null==o.to||(l.inclusiveRight?o.to>=t:o.to>t);(n||(n=[])).push(new Pt(l,o.from,a?null:o.to))}}return n}function Vt(e,t,r){var n;if(e)for(var i=0;i<e.length;++i){var o=e[i],l=o.marker;if(null==o.to||(l.inclusiveRight?o.to>=t:o.to>t)||o.from==t&&"bookmark"==l.type&&(!r||o.marker.insertLeft)){var a=null==o.from||(l.inclusiveLeft?o.from<=t:o.from<t);(n||(n=[])).push(new Pt(l,a?null:o.from-t,null==o.to?null:o.to-t))}}return n}function Gt(e,t){if(t.full)return null;var r=lt(e,t.from.line)&&et(e,t.from.line).markedSpans,n=lt(e,t.to.line)&&et(e,t.to.line).markedSpans;if(!r&&!n)return null;var i=t.from.ch,o=t.to.ch,l=0==ut(t.from,t.to),a=Bt(r,i,l),s=Vt(n,o,l),u=1==t.text.length,c=J(t.text).length+(u?i:0);if(a)for(var f=0;f<a.length;++f){var d=a[f];if(null==d.to){var h=zt(s,d.marker);h?u&&(d.to=null==h.to?null:h.to+c):d.to=i}}if(s)for(var p=0;p<s.length;++p){var g=s[p];null!=g.to&&(g.to+=c),null==g.from?zt(a,g.marker)||(g.from=c,u&&(a||(a=[])).push(g)):(g.from+=c,u&&(a||(a=[])).push(g))}a&&(a=jt(a)),s&&s!=a&&(s=jt(s));var v=[a];if(!u){var m,y=t.text.length-2;if(y>0&&a)for(var b=0;b<a.length;++b)null==a[b].to&&(m||(m=[])).push(new Pt(a[b].marker,null,null));for(var w=0;w<y;++w)v.push(m);v.push(s)}return v}function jt(e){for(var t=0;t<e.length;++t){var r=e[t];null!=r.from&&r.from==r.to&&!1!==r.marker.clearWhenEmpty&&e.splice(t--,1)}return e.length?e:null}function Ut(e,t,r){var n=null;if(e.iter(t.line,r.line+1,(function(e){if(e.markedSpans)for(var t=0;t<e.markedSpans.length;++t){var r=e.markedSpans[t].marker;!r.readOnly||n&&-1!=j(n,r)||(n||(n=[])).push(r)}})),!n)return null;for(var i=[{from:t,to:r}],o=0;o<n.length;++o)for(var l=n[o],a=l.find(0),s=0;s<i.length;++s){var u=i[s];if(!(ut(u.to,a.from)<0||ut(u.from,a.to)>0)){var c=[s,1],f=ut(u.from,a.from),d=ut(u.to,a.to);(f<0||!l.inclusiveLeft&&!f)&&c.push({from:u.from,to:a.from}),(d>0||!l.inclusiveRight&&!d)&&c.push({from:a.to,to:u.to}),i.splice.apply(i,c),s+=c.length-3}}return i}function Kt(e){var t=e.markedSpans;if(t){for(var r=0;r<t.length;++r)t[r].marker.detachLine(e);e.markedSpans=null}}function _t(e,t){if(t){for(var r=0;r<t.length;++r)t[r].marker.attachLine(e);e.markedSpans=t}}function Xt(e){return e.inclusiveLeft?-1:0}function $t(e){return e.inclusiveRight?1:0}function Yt(e,t){var r=e.lines.length-t.lines.length;if(0!=r)return r;var n=e.find(),i=t.find(),o=ut(n.from,i.from)||Xt(e)-Xt(t);if(o)return-o;var l=ut(n.to,i.to)||$t(e)-$t(t);return l||t.id-e.id}function qt(e,t){var r,n=Ht&&e.markedSpans;if(n)for(var i=void 0,o=0;o<n.length;++o)(i=n[o]).marker.collapsed&&null==(t?i.from:i.to)&&(!r||Yt(r,i.marker)<0)&&(r=i.marker);return r}function Zt(e){return qt(e,!0)}function Jt(e){return qt(e,!1)}function Qt(e,t){var r,n=Ht&&e.markedSpans;if(n)for(var i=0;i<n.length;++i){var o=n[i];o.marker.collapsed&&(null==o.from||o.from<t)&&(null==o.to||o.to>t)&&(!r||Yt(r,o.marker)<0)&&(r=o.marker)}return r}function er(e,t,r,n,i){var o=et(e,t),l=Ht&&o.markedSpans;if(l)for(var a=0;a<l.length;++a){var s=l[a];if(s.marker.collapsed){var u=s.marker.find(0),c=ut(u.from,r)||Xt(s.marker)-Xt(i),f=ut(u.to,n)||$t(s.marker)-$t(i);if(!(c>=0&&f<=0||c<=0&&f>=0)&&(c<=0&&(s.marker.inclusiveRight&&i.inclusiveLeft?ut(u.to,r)>=0:ut(u.to,r)>0)||c>=0&&(s.marker.inclusiveRight&&i.inclusiveLeft?ut(u.from,n)<=0:ut(u.from,n)<0)))return!0}}}function tr(e){for(var t;t=Zt(e);)e=t.find(-1,!0).line;return e}function rr(e){for(var t;t=Jt(e);)e=t.find(1,!0).line;return e}function nr(e){for(var t,r;t=Jt(e);)e=t.find(1,!0).line,(r||(r=[])).push(e);return r}function ir(e,t){var r=et(e,t),n=tr(r);return r==n?t:it(n)}function or(e,t){if(t>e.lastLine())return t;var r,n=et(e,t);if(!lr(e,n))return t;for(;r=Jt(n);)n=r.find(1,!0).line;return it(n)+1}function lr(e,t){var r=Ht&&t.markedSpans;if(r)for(var n=void 0,i=0;i<r.length;++i)if((n=r[i]).marker.collapsed){if(null==n.from)return!0;if(!n.marker.widgetNode&&0==n.from&&n.marker.inclusiveLeft&&ar(e,t,n))return!0}}function ar(e,t,r){if(null==r.to){var n=r.marker.find(1,!0);return ar(e,n.line,zt(n.line.markedSpans,r.marker))}if(r.marker.inclusiveRight&&r.to==t.text.length)return!0;for(var i=void 0,o=0;o<t.markedSpans.length;++o)if((i=t.markedSpans[o]).marker.collapsed&&!i.marker.widgetNode&&i.from==r.to&&(null==i.to||i.to!=r.from)&&(i.marker.inclusiveLeft||r.marker.inclusiveRight)&&ar(e,t,i))return!0}function sr(e){for(var t=0,r=(e=tr(e)).parent,n=0;n<r.lines.length;++n){var i=r.lines[n];if(i==e)break;t+=i.height}for(var o=r.parent;o;o=(r=o).parent)for(var l=0;l<o.children.length;++l){var a=o.children[l];if(a==r)break;t+=a.height}return t}function ur(e){if(0==e.height)return 0;for(var t,r=e.text.length,n=e;t=Zt(n);){var i=t.find(0,!0);n=i.from.line,r+=i.from.ch-i.to.ch}for(n=e;t=Jt(n);){var o=t.find(0,!0);r-=n.text.length-o.from.ch,r+=(n=o.to.line).text.length-o.to.ch}return r}function cr(e){var t=e.display,r=e.doc;t.maxLine=et(r,r.first),t.maxLineLength=ur(t.maxLine),t.maxLineChanged=!0,r.iter((function(e){var r=ur(e);r>t.maxLineLength&&(t.maxLineLength=r,t.maxLine=e)}))}var fr=function(e,t,r){this.text=e,_t(this,t),this.height=r?r(this):1};function dr(e,t,r,n){e.text=t,e.stateAfter&&(e.stateAfter=null),e.styles&&(e.styles=null),null!=e.order&&(e.order=null),Kt(e),_t(e,r);var i=n?n(e):1;i!=e.height&&nt(e,i)}function hr(e){e.parent=null,Kt(e)}fr.prototype.lineNo=function(){return it(this)},Se(fr);var pr={},gr={};function vr(e,t){if(!e||/^\s*$/.test(e))return null;var r=t.addModeClass?gr:pr;return r[e]||(r[e]=e.replace(/\S+/g,"cm-$&"))}function mr(e,t){var r=D("span",null,null,s?"padding-right: .1px":null),n={pre:D("pre",[r],"CodeMirror-line"),content:r,col:0,pos:0,cm:e,trailingSpace:!1,splitSpaces:e.getOption("lineWrapping")};t.measure={};for(var i=0;i<=(t.rest?t.rest.length:0);i++){var o=i?t.rest[i-1]:t.line,l=void 0;n.pos=0,n.addToken=br,Ee(e.display.measure)&&(l=ge(o,e.doc.direction))&&(n.addToken=xr(n.addToken,l)),n.map=[],kr(o,n,xt(e,o,t!=e.display.externalMeasured&&it(o))),o.styleClasses&&(o.styleClasses.bgClass&&(n.bgClass=E(o.styleClasses.bgClass,n.bgClass||"")),o.styleClasses.textClass&&(n.textClass=E(o.styleClasses.textClass,n.textClass||""))),0==n.map.length&&n.map.push(0,0,n.content.appendChild(Fe(e.display.measure))),0==i?(t.measure.map=n.map,t.measure.cache={}):((t.measure.maps||(t.measure.maps=[])).push(n.map),(t.measure.caches||(t.measure.caches=[])).push({}))}if(s){var a=n.content.lastChild;(/\bcm-tab\b/.test(a.className)||a.querySelector&&a.querySelector(".cm-tab"))&&(n.content.className="cm-tab-wrap-hack")}return we(e,"renderLine",e,t.line,n.pre),n.pre.className&&(n.textClass=E(n.pre.className,n.textClass||"")),n}function yr(e){var t=A("span","•","cm-invalidchar");return t.title="\\u"+e.charCodeAt(0).toString(16),t.setAttribute("aria-label",t.title),t}function br(e,t,r,n,i,o,s){if(t){var u,c=e.splitSpaces?wr(t,e.trailingSpace):t,f=e.cm.state.specialChars,d=!1;if(f.test(t)){u=document.createDocumentFragment();for(var h=0;;){f.lastIndex=h;var p=f.exec(t),g=p?p.index-h:t.length-h;if(g){var v=document.createTextNode(c.slice(h,h+g));l&&a<9?u.appendChild(A("span",[v])):u.appendChild(v),e.map.push(e.pos,e.pos+g,v),e.col+=g,e.pos+=g}if(!p)break;h+=g+1;var m=void 0;if("\t"==p[0]){var y=e.cm.options.tabSize,b=y-e.col%y;(m=u.appendChild(A("span",Z(b),"cm-tab"))).setAttribute("role","presentation"),m.setAttribute("cm-text","\t"),e.col+=b}else"\r"==p[0]||"\n"==p[0]?((m=u.appendChild(A("span","\r"==p[0]?"␍":"␤","cm-invalidchar"))).setAttribute("cm-text",p[0]),e.col+=1):((m=e.cm.options.specialCharPlaceholder(p[0])).setAttribute("cm-text",p[0]),l&&a<9?u.appendChild(A("span",[m])):u.appendChild(m),e.col+=1);e.map.push(e.pos,e.pos+1,m),e.pos++}}else e.col+=t.length,u=document.createTextNode(c),e.map.push(e.pos,e.pos+t.length,u),l&&a<9&&(d=!0),e.pos+=t.length;if(e.trailingSpace=32==c.charCodeAt(t.length-1),r||n||i||d||o||s){var w=r||"";n&&(w+=n),i&&(w+=i);var x=A("span",[u],w,o);if(s)for(var C in s)s.hasOwnProperty(C)&&"style"!=C&&"class"!=C&&x.setAttribute(C,s[C]);return e.content.appendChild(x)}e.content.appendChild(u)}}function wr(e,t){if(e.length>1&&!/  /.test(e))return e;for(var r=t,n="",i=0;i<e.length;i++){var o=e.charAt(i);" "!=o||!r||i!=e.length-1&&32!=e.charCodeAt(i+1)||(o=" "),n+=o,r=" "==o}return n}function xr(e,t){return function(r,n,i,o,l,a,s){i=i?i+" cm-force-border":"cm-force-border";for(var u=r.pos,c=u+n.length;;){for(var f=void 0,d=0;d<t.length&&!((f=t[d]).to>u&&f.from<=u);d++);if(f.to>=c)return e(r,n,i,o,l,a,s);e(r,n.slice(0,f.to-u),i,o,null,a,s),o=null,n=n.slice(f.to-u),u=f.to}}}function Cr(e,t,r,n){var i=!n&&r.widgetNode;i&&e.map.push(e.pos,e.pos+t,i),!n&&e.cm.display.input.needsContentAttribute&&(i||(i=e.content.appendChild(document.createElement("span"))),i.setAttribute("cm-marker",r.id)),i&&(e.cm.display.input.setUneditable(i),e.content.appendChild(i)),e.pos+=t,e.trailingSpace=!1}function kr(e,t,r){var n=e.markedSpans,i=e.text,o=0;if(n)for(var l,a,s,u,c,f,d,h=i.length,p=0,g=1,v="",m=0;;){if(m==p){s=u=c=a="",d=null,f=null,m=1/0;for(var y=[],b=void 0,w=0;w<n.length;++w){var x=n[w],C=x.marker;if("bookmark"==C.type&&x.from==p&&C.widgetNode)y.push(C);else if(x.from<=p&&(null==x.to||x.to>p||C.collapsed&&x.to==p&&x.from==p)){if(null!=x.to&&x.to!=p&&m>x.to&&(m=x.to,u=""),C.className&&(s+=" "+C.className),C.css&&(a=(a?a+";":"")+C.css),C.startStyle&&x.from==p&&(c+=" "+C.startStyle),C.endStyle&&x.to==m&&(b||(b=[])).push(C.endStyle,x.to),C.title&&((d||(d={})).title=C.title),C.attributes)for(var k in C.attributes)(d||(d={}))[k]=C.attributes[k];C.collapsed&&(!f||Yt(f.marker,C)<0)&&(f=x)}else x.from>p&&m>x.from&&(m=x.from)}if(b)for(var S=0;S<b.length;S+=2)b[S+1]==m&&(u+=" "+b[S]);if(!f||f.from==p)for(var L=0;L<y.length;++L)Cr(t,0,y[L]);if(f&&(f.from||0)==p){if(Cr(t,(null==f.to?h+1:f.to)-p,f.marker,null==f.from),null==f.to)return;f.to==p&&(f=!1)}}if(p>=h)break;for(var T=Math.min(h,m);;){if(v){var M=p+v.length;if(!f){var N=M>T?v.slice(0,T-p):v;t.addToken(t,N,l?l+s:s,c,p+N.length==m?u:"",a,d)}if(M>=T){v=v.slice(T-p),p=T;break}p=M,c=""}v=i.slice(o,o=r[g++]),l=vr(r[g++],t.cm.options)}}else for(var O=1;O<r.length;O+=2)t.addToken(t,i.slice(o,o=r[O]),vr(r[O+1],t.cm.options))}function Sr(e,t,r){this.line=t,this.rest=nr(t),this.size=this.rest?it(J(this.rest))-r+1:1,this.node=this.text=null,this.hidden=lr(e,t)}function Lr(e,t,r){for(var n,i=[],o=t;o<r;o=n){var l=new Sr(e.doc,et(e.doc,o),o);n=o+l.size,i.push(l)}return i}var Tr=null;function Mr(e){Tr?Tr.ops.push(e):e.ownsGroup=Tr={ops:[e],delayedCallbacks:[]}}function Nr(e){var t=e.delayedCallbacks,r=0;do{for(;r<t.length;r++)t[r].call(null);for(var n=0;n<e.ops.length;n++){var i=e.ops[n];if(i.cursorActivityHandlers)for(;i.cursorActivityCalled<i.cursorActivityHandlers.length;)i.cursorActivityHandlers[i.cursorActivityCalled++].call(null,i.cm)}}while(r<t.length)}function Or(e,t){var r=e.ownsGroup;if(r)try{Nr(r)}finally{Tr=null,t(r)}}var Ar=null;function Dr(e,t){var r=ye(e,t);if(r.length){var n,i=Array.prototype.slice.call(arguments,2);Tr?n=Tr.delayedCallbacks:Ar?n=Ar:(n=Ar=[],setTimeout(Wr,0));for(var o=function(e){n.push((function(){return r[e].apply(null,i)}))},l=0;l<r.length;++l)o(l)}}function Wr(){var e=Ar;Ar=null;for(var t=0;t<e.length;++t)e[t]()}function Hr(e,t,r,n){for(var i=0;i<t.changes.length;i++){var o=t.changes[i];"text"==o?zr(e,t):"gutter"==o?Rr(e,t,r,n):"class"==o?Ir(e,t):"widget"==o&&Br(e,t,n)}t.changes=null}function Fr(e){return e.node==e.text&&(e.node=A("div",null,null,"position: relative"),e.text.parentNode&&e.text.parentNode.replaceChild(e.node,e.text),e.node.appendChild(e.text),l&&a<8&&(e.node.style.zIndex=2)),e.node}function Er(e,t){var r=t.bgClass?t.bgClass+" "+(t.line.bgClass||""):t.line.bgClass;if(r&&(r+=" CodeMirror-linebackground"),t.background)r?t.background.className=r:(t.background.parentNode.removeChild(t.background),t.background=null);else if(r){var n=Fr(t);t.background=n.insertBefore(A("div",null,r),n.firstChild),e.display.input.setUneditable(t.background)}}function Pr(e,t){var r=e.display.externalMeasured;return r&&r.line==t.line?(e.display.externalMeasured=null,t.measure=r.measure,r.built):mr(e,t)}function zr(e,t){var r=t.text.className,n=Pr(e,t);t.text==t.node&&(t.node=n.pre),t.text.parentNode.replaceChild(n.pre,t.text),t.text=n.pre,n.bgClass!=t.bgClass||n.textClass!=t.textClass?(t.bgClass=n.bgClass,t.textClass=n.textClass,Ir(e,t)):r&&(t.text.className=r)}function Ir(e,t){Er(e,t),t.line.wrapClass?Fr(t).className=t.line.wrapClass:t.node!=t.text&&(t.node.className="");var r=t.textClass?t.textClass+" "+(t.line.textClass||""):t.line.textClass;t.text.className=r||""}function Rr(e,t,r,n){if(t.gutter&&(t.node.removeChild(t.gutter),t.gutter=null),t.gutterBackground&&(t.node.removeChild(t.gutterBackground),t.gutterBackground=null),t.line.gutterClass){var i=Fr(t);t.gutterBackground=A("div",null,"CodeMirror-gutter-background "+t.line.gutterClass,"left: "+(e.options.fixedGutter?n.fixedPos:-n.gutterTotalWidth)+"px; width: "+n.gutterTotalWidth+"px"),e.display.input.setUneditable(t.gutterBackground),i.insertBefore(t.gutterBackground,t.text)}var o=t.line.gutterMarkers;if(e.options.lineNumbers||o){var l=Fr(t),a=t.gutter=A("div",null,"CodeMirror-gutter-wrapper","left: "+(e.options.fixedGutter?n.fixedPos:-n.gutterTotalWidth)+"px");if(a.setAttribute("aria-hidden","true"),e.display.input.setUneditable(a),l.insertBefore(a,t.text),t.line.gutterClass&&(a.className+=" "+t.line.gutterClass),!e.options.lineNumbers||o&&o["CodeMirror-linenumbers"]||(t.lineNumber=a.appendChild(A("div",at(e.options,r),"CodeMirror-linenumber CodeMirror-gutter-elt","left: "+n.gutterLeft["CodeMirror-linenumbers"]+"px; width: "+e.display.lineNumInnerWidth+"px"))),o)for(var s=0;s<e.display.gutterSpecs.length;++s){var u=e.display.gutterSpecs[s].className,c=o.hasOwnProperty(u)&&o[u];c&&a.appendChild(A("div",[c],"CodeMirror-gutter-elt","left: "+n.gutterLeft[u]+"px; width: "+n.gutterWidth[u]+"px"))}}}function Br(e,t,r){t.alignable&&(t.alignable=null);for(var n=L("CodeMirror-linewidget"),i=t.node.firstChild,o=void 0;i;i=o)o=i.nextSibling,n.test(i.className)&&t.node.removeChild(i);Gr(e,t,r)}function Vr(e,t,r,n){var i=Pr(e,t);return t.text=t.node=i.pre,i.bgClass&&(t.bgClass=i.bgClass),i.textClass&&(t.textClass=i.textClass),Ir(e,t),Rr(e,t,r,n),Gr(e,t,n),t.node}function Gr(e,t,r){if(jr(e,t.line,t,r,!0),t.rest)for(var n=0;n<t.rest.length;n++)jr(e,t.rest[n],t,r,!1)}function jr(e,t,r,n,i){if(t.widgets)for(var o=Fr(r),l=0,a=t.widgets;l<a.length;++l){var s=a[l],u=A("div",[s.node],"CodeMirror-linewidget"+(s.className?" "+s.className:""));s.handleMouseEvents||u.setAttribute("cm-ignore-events","true"),Ur(s,u,r,n),e.display.input.setUneditable(u),i&&s.above?o.insertBefore(u,r.gutter||r.text):o.appendChild(u),Dr(s,"redraw")}}function Ur(e,t,r,n){if(e.noHScroll){(r.alignable||(r.alignable=[])).push(t);var i=n.wrapperWidth;t.style.left=n.fixedPos+"px",e.coverGutter||(i-=n.gutterTotalWidth,t.style.paddingLeft=n.gutterTotalWidth+"px"),t.style.width=i+"px"}e.coverGutter&&(t.style.zIndex=5,t.style.position="relative",e.noHScroll||(t.style.marginLeft=-n.gutterTotalWidth+"px"))}function Kr(e){if(null!=e.height)return e.height;var t=e.doc.cm;if(!t)return 0;if(!W(document.body,e.node)){var r="position: relative;";e.coverGutter&&(r+="margin-left: -"+t.display.gutters.offsetWidth+"px;"),e.noHScroll&&(r+="width: "+t.display.wrapper.clientWidth+"px;"),O(t.display.measure,A("div",[e.node],null,r))}return e.height=e.node.parentNode.offsetHeight}function _r(e,t){for(var r=Oe(t);r!=e.wrapper;r=r.parentNode)if(!r||1==r.nodeType&&"true"==r.getAttribute("cm-ignore-events")||r.parentNode==e.sizer&&r!=e.mover)return!0}function Xr(e){return e.lineSpace.offsetTop}function $r(e){return e.mover.offsetHeight-e.lineSpace.offsetHeight}function Yr(e){if(e.cachedPaddingH)return e.cachedPaddingH;var t=O(e.measure,A("pre","x","CodeMirror-line-like")),r=window.getComputedStyle?window.getComputedStyle(t):t.currentStyle,n={left:parseInt(r.paddingLeft),right:parseInt(r.paddingRight)};return isNaN(n.left)||isNaN(n.right)||(e.cachedPaddingH=n),n}function qr(e){return U-e.display.nativeBarWidth}function Zr(e){return e.display.scroller.clientWidth-qr(e)-e.display.barWidth}function Jr(e){return e.display.scroller.clientHeight-qr(e)-e.display.barHeight}function Qr(e,t,r){var n=e.options.lineWrapping,i=n&&Zr(e);if(!t.measure.heights||n&&t.measure.width!=i){var o=t.measure.heights=[];if(n){t.measure.width=i;for(var l=t.text.firstChild.getClientRects(),a=0;a<l.length-1;a++){var s=l[a],u=l[a+1];Math.abs(s.bottom-u.bottom)>2&&o.push((s.bottom+u.top)/2-r.top)}}o.push(r.bottom-r.top)}}function en(e,t,r){if(e.line==t)return{map:e.measure.map,cache:e.measure.cache};if(e.rest){for(var n=0;n<e.rest.length;n++)if(e.rest[n]==t)return{map:e.measure.maps[n],cache:e.measure.caches[n]};for(var i=0;i<e.rest.length;i++)if(it(e.rest[i])>r)return{map:e.measure.maps[i],cache:e.measure.caches[i],before:!0}}}function tn(e,t){var r=it(t=tr(t)),n=e.display.externalMeasured=new Sr(e.doc,t,r);n.lineN=r;var i=n.built=mr(e,n);return n.text=i.pre,O(e.display.lineMeasure,i.pre),n}function rn(e,t,r,n){return ln(e,on(e,t),r,n)}function nn(e,t){if(t>=e.display.viewFrom&&t<e.display.viewTo)return e.display.view[Rn(e,t)];var r=e.display.externalMeasured;return r&&t>=r.lineN&&t<r.lineN+r.size?r:void 0}function on(e,t){var r=it(t),n=nn(e,r);n&&!n.text?n=null:n&&n.changes&&(Hr(e,n,r,Fn(e)),e.curOp.forceUpdate=!0),n||(n=tn(e,t));var i=en(n,t,r);return{line:t,view:n,rect:null,map:i.map,cache:i.cache,before:i.before,hasHeights:!1}}function ln(e,t,r,n,i){t.before&&(r=-1);var o,l=r+(n||"");return t.cache.hasOwnProperty(l)?o=t.cache[l]:(t.rect||(t.rect=t.view.text.getBoundingClientRect()),t.hasHeights||(Qr(e,t.view,t.rect),t.hasHeights=!0),(o=fn(e,t,r,n)).bogus||(t.cache[l]=o)),{left:o.left,right:o.right,top:i?o.rtop:o.top,bottom:i?o.rbottom:o.bottom}}var an,sn={left:0,right:0,top:0,bottom:0};function un(e,t,r){for(var n,i,o,l,a,s,u=0;u<e.length;u+=3)if(a=e[u],s=e[u+1],t<a?(i=0,o=1,l="left"):t<s?o=1+(i=t-a):(u==e.length-3||t==s&&e[u+3]>t)&&(i=(o=s-a)-1,t>=s&&(l="right")),null!=i){if(n=e[u+2],a==s&&r==(n.insertLeft?"left":"right")&&(l=r),"left"==r&&0==i)for(;u&&e[u-2]==e[u-3]&&e[u-1].insertLeft;)n=e[2+(u-=3)],l="left";if("right"==r&&i==s-a)for(;u<e.length-3&&e[u+3]==e[u+4]&&!e[u+5].insertLeft;)n=e[(u+=3)+2],l="right";break}return{node:n,start:i,end:o,collapse:l,coverStart:a,coverEnd:s}}function cn(e,t){var r=sn;if("left"==t)for(var n=0;n<e.length&&(r=e[n]).left==r.right;n++);else for(var i=e.length-1;i>=0&&(r=e[i]).left==r.right;i--);return r}function fn(e,t,r,n){var i,o=un(t.map,r,n),s=o.node,u=o.start,c=o.end,f=o.collapse;if(3==s.nodeType){for(var d=0;d<4;d++){for(;u&&se(t.line.text.charAt(o.coverStart+u));)--u;for(;o.coverStart+c<o.coverEnd&&se(t.line.text.charAt(o.coverStart+c));)++c;if((i=l&&a<9&&0==u&&c==o.coverEnd-o.coverStart?s.parentNode.getBoundingClientRect():cn(T(s,u,c).getClientRects(),n)).left||i.right||0==u)break;c=u,u-=1,f="right"}l&&a<11&&(i=dn(e.display.measure,i))}else{var h;u>0&&(f=n="right"),i=e.options.lineWrapping&&(h=s.getClientRects()).length>1?h["right"==n?h.length-1:0]:s.getBoundingClientRect()}if(l&&a<9&&!u&&(!i||!i.left&&!i.right)){var p=s.parentNode.getClientRects()[0];i=p?{left:p.left,right:p.left+Hn(e.display),top:p.top,bottom:p.bottom}:sn}for(var g=i.top-t.rect.top,v=i.bottom-t.rect.top,m=(g+v)/2,y=t.view.measure.heights,b=0;b<y.length-1&&!(m<y[b]);b++);var w=b?y[b-1]:0,x=y[b],C={left:("right"==f?i.right:i.left)-t.rect.left,right:("left"==f?i.left:i.right)-t.rect.left,top:w,bottom:x};return i.left||i.right||(C.bogus=!0),e.options.singleCursorHeightPerLine||(C.rtop=g,C.rbottom=v),C}function dn(e,t){if(!window.screen||null==screen.logicalXDPI||screen.logicalXDPI==screen.deviceXDPI||!Ve(e))return t;var r=screen.logicalXDPI/screen.deviceXDPI,n=screen.logicalYDPI/screen.deviceYDPI;return{left:t.left*r,right:t.right*r,top:t.top*n,bottom:t.bottom*n}}function hn(e){if(e.measure&&(e.measure.cache={},e.measure.heights=null,e.rest))for(var t=0;t<e.rest.length;t++)e.measure.caches[t]={}}function pn(e){e.display.externalMeasure=null,N(e.display.lineMeasure);for(var t=0;t<e.display.view.length;t++)hn(e.display.view[t])}function gn(e){pn(e),e.display.cachedCharWidth=e.display.cachedTextHeight=e.display.cachedPaddingH=null,e.options.lineWrapping||(e.display.maxLineChanged=!0),e.display.lineNumChars=null}function vn(e){return c&&m?-(e.body.getBoundingClientRect().left-parseInt(getComputedStyle(e.body).marginLeft)):e.defaultView.pageXOffset||(e.documentElement||e.body).scrollLeft}function mn(e){return c&&m?-(e.body.getBoundingClientRect().top-parseInt(getComputedStyle(e.body).marginTop)):e.defaultView.pageYOffset||(e.documentElement||e.body).scrollTop}function yn(e){var t=tr(e).widgets,r=0;if(t)for(var n=0;n<t.length;++n)t[n].above&&(r+=Kr(t[n]));return r}function bn(e,t,r,n,i){if(!i){var o=yn(t);r.top+=o,r.bottom+=o}if("line"==n)return r;n||(n="local");var l=sr(t);if("local"==n?l+=Xr(e.display):l-=e.display.viewOffset,"page"==n||"window"==n){var a=e.display.lineSpace.getBoundingClientRect();l+=a.top+("window"==n?0:mn(z(e)));var s=a.left+("window"==n?0:vn(z(e)));r.left+=s,r.right+=s}return r.top+=l,r.bottom+=l,r}function wn(e,t,r){if("div"==r)return t;var n=t.left,i=t.top;if("page"==r)n-=vn(z(e)),i-=mn(z(e));else if("local"==r||!r){var o=e.display.sizer.getBoundingClientRect();n+=o.left,i+=o.top}var l=e.display.lineSpace.getBoundingClientRect();return{left:n-l.left,top:i-l.top}}function xn(e,t,r,n,i){return n||(n=et(e.doc,t.line)),bn(e,n,rn(e,n,t.ch,i),r)}function Cn(e,t,r,n,i,o){function l(t,l){var a=ln(e,i,t,l?"right":"left",o);return l?a.left=a.right:a.right=a.left,bn(e,n,a,r)}n=n||et(e.doc,t.line),i||(i=on(e,n));var a=ge(n,e.doc.direction),s=t.ch,u=t.sticky;if(s>=n.text.length?(s=n.text.length,u="before"):s<=0&&(s=0,u="after"),!a)return l("before"==u?s-1:s,"before"==u);function c(e,t,r){return l(r?e-1:e,1==a[t].level!=r)}var f=he(a,s,u),d=de,h=c(s,f,"before"==u);return null!=d&&(h.other=c(s,d,"before"!=u)),h}function kn(e,t){var r=0;t=gt(e.doc,t),e.options.lineWrapping||(r=Hn(e.display)*t.ch);var n=et(e.doc,t.line),i=sr(n)+Xr(e.display);return{left:r,right:r,top:i,bottom:i+n.height}}function Sn(e,t,r,n,i){var o=st(e,t,r);return o.xRel=i,n&&(o.outside=n),o}function Ln(e,t,r){var n=e.doc;if((r+=e.display.viewOffset)<0)return Sn(n.first,0,null,-1,-1);var i=ot(n,r),o=n.first+n.size-1;if(i>o)return Sn(n.first+n.size-1,et(n,o).text.length,null,1,1);t<0&&(t=0);for(var l=et(n,i);;){var a=On(e,l,i,t,r),s=Qt(l,a.ch+(a.xRel>0||a.outside>0?1:0));if(!s)return a;var u=s.find(1);if(u.line==i)return u;l=et(n,i=u.line)}}function Tn(e,t,r,n){n-=yn(t);var i=t.text.length,o=ce((function(t){return ln(e,r,t-1).bottom<=n}),i,0);return{begin:o,end:i=ce((function(t){return ln(e,r,t).top>n}),o,i)}}function Mn(e,t,r,n){return r||(r=on(e,t)),Tn(e,t,r,bn(e,t,ln(e,r,n),"line").top)}function Nn(e,t,r,n){return!(e.bottom<=r)&&(e.top>r||(n?e.left:e.right)>t)}function On(e,t,r,n,i){i-=sr(t);var o=on(e,t),l=yn(t),a=0,s=t.text.length,u=!0,c=ge(t,e.doc.direction);if(c){var f=(e.options.lineWrapping?Dn:An)(e,t,r,o,c,n,i);a=(u=1!=f.level)?f.from:f.to-1,s=u?f.to:f.from-1}var d,h,p=null,g=null,v=ce((function(t){var r=ln(e,o,t);return r.top+=l,r.bottom+=l,!!Nn(r,n,i,!1)&&(r.top<=i&&r.left<=n&&(p=t,g=r),!0)}),a,s),m=!1;if(g){var y=n-g.left<g.right-n,b=y==u;v=p+(b?0:1),h=b?"after":"before",d=y?g.left:g.right}else{u||v!=s&&v!=a||v++,h=0==v?"after":v==t.text.length?"before":ln(e,o,v-(u?1:0)).bottom+l<=i==u?"after":"before";var w=Cn(e,st(r,v,h),"line",t,o);d=w.left,m=i<w.top?-1:i>=w.bottom?1:0}return Sn(r,v=ue(t.text,v,1),h,m,n-d)}function An(e,t,r,n,i,o,l){var a=ce((function(a){var s=i[a],u=1!=s.level;return Nn(Cn(e,st(r,u?s.to:s.from,u?"before":"after"),"line",t,n),o,l,!0)}),0,i.length-1),s=i[a];if(a>0){var u=1!=s.level,c=Cn(e,st(r,u?s.from:s.to,u?"after":"before"),"line",t,n);Nn(c,o,l,!0)&&c.top>l&&(s=i[a-1])}return s}function Dn(e,t,r,n,i,o,l){var a=Tn(e,t,n,l),s=a.begin,u=a.end;/\s/.test(t.text.charAt(u-1))&&u--;for(var c=null,f=null,d=0;d<i.length;d++){var h=i[d];if(!(h.from>=u||h.to<=s)){var p=ln(e,n,1!=h.level?Math.min(u,h.to)-1:Math.max(s,h.from)).right,g=p<o?o-p+1e9:p-o;(!c||f>g)&&(c=h,f=g)}}return c||(c=i[i.length-1]),c.from<s&&(c={from:s,to:c.to,level:c.level}),c.to>u&&(c={from:c.from,to:u,level:c.level}),c}function Wn(e){if(null!=e.cachedTextHeight)return e.cachedTextHeight;if(null==an){an=A("pre",null,"CodeMirror-line-like");for(var t=0;t<49;++t)an.appendChild(document.createTextNode("x")),an.appendChild(A("br"));an.appendChild(document.createTextNode("x"))}O(e.measure,an);var r=an.offsetHeight/50;return r>3&&(e.cachedTextHeight=r),N(e.measure),r||1}function Hn(e){if(null!=e.cachedCharWidth)return e.cachedCharWidth;var t=A("span","xxxxxxxxxx"),r=A("pre",[t],"CodeMirror-line-like");O(e.measure,r);var n=t.getBoundingClientRect(),i=(n.right-n.left)/10;return i>2&&(e.cachedCharWidth=i),i||10}function Fn(e){for(var t=e.display,r={},n={},i=t.gutters.clientLeft,o=t.gutters.firstChild,l=0;o;o=o.nextSibling,++l){var a=e.display.gutterSpecs[l].className;r[a]=o.offsetLeft+o.clientLeft+i,n[a]=o.clientWidth}return{fixedPos:En(t),gutterTotalWidth:t.gutters.offsetWidth,gutterLeft:r,gutterWidth:n,wrapperWidth:t.wrapper.clientWidth}}function En(e){return e.scroller.getBoundingClientRect().left-e.sizer.getBoundingClientRect().left}function Pn(e){var t=Wn(e.display),r=e.options.lineWrapping,n=r&&Math.max(5,e.display.scroller.clientWidth/Hn(e.display)-3);return function(i){if(lr(e.doc,i))return 0;var o=0;if(i.widgets)for(var l=0;l<i.widgets.length;l++)i.widgets[l].height&&(o+=i.widgets[l].height);return r?o+(Math.ceil(i.text.length/n)||1)*t:o+t}}function zn(e){var t=e.doc,r=Pn(e);t.iter((function(e){var t=r(e);t!=e.height&&nt(e,t)}))}function In(e,t,r,n){var i=e.display;if(!r&&"true"==Oe(t).getAttribute("cm-not-content"))return null;var o,l,a=i.lineSpace.getBoundingClientRect();try{o=t.clientX-a.left,l=t.clientY-a.top}catch(e){return null}var s,u=Ln(e,o,l);if(n&&u.xRel>0&&(s=et(e.doc,u.line).text).length==u.ch){var c=V(s,s.length,e.options.tabSize)-s.length;u=st(u.line,Math.max(0,Math.round((o-Yr(e.display).left)/Hn(e.display))-c))}return u}function Rn(e,t){if(t>=e.display.viewTo)return null;if((t-=e.display.viewFrom)<0)return null;for(var r=e.display.view,n=0;n<r.length;n++)if((t-=r[n].size)<0)return n}function Bn(e,t,r,n){null==t&&(t=e.doc.first),null==r&&(r=e.doc.first+e.doc.size),n||(n=0);var i=e.display;if(n&&r<i.viewTo&&(null==i.updateLineNumbers||i.updateLineNumbers>t)&&(i.updateLineNumbers=t),e.curOp.viewChanged=!0,t>=i.viewTo)Ht&&ir(e.doc,t)<i.viewTo&&Gn(e);else if(r<=i.viewFrom)Ht&&or(e.doc,r+n)>i.viewFrom?Gn(e):(i.viewFrom+=n,i.viewTo+=n);else if(t<=i.viewFrom&&r>=i.viewTo)Gn(e);else if(t<=i.viewFrom){var o=jn(e,r,r+n,1);o?(i.view=i.view.slice(o.index),i.viewFrom=o.lineN,i.viewTo+=n):Gn(e)}else if(r>=i.viewTo){var l=jn(e,t,t,-1);l?(i.view=i.view.slice(0,l.index),i.viewTo=l.lineN):Gn(e)}else{var a=jn(e,t,t,-1),s=jn(e,r,r+n,1);a&&s?(i.view=i.view.slice(0,a.index).concat(Lr(e,a.lineN,s.lineN)).concat(i.view.slice(s.index)),i.viewTo+=n):Gn(e)}var u=i.externalMeasured;u&&(r<u.lineN?u.lineN+=n:t<u.lineN+u.size&&(i.externalMeasured=null))}function Vn(e,t,r){e.curOp.viewChanged=!0;var n=e.display,i=e.display.externalMeasured;if(i&&t>=i.lineN&&t<i.lineN+i.size&&(n.externalMeasured=null),!(t<n.viewFrom||t>=n.viewTo)){var o=n.view[Rn(e,t)];if(null!=o.node){var l=o.changes||(o.changes=[]);-1==j(l,r)&&l.push(r)}}}function Gn(e){e.display.viewFrom=e.display.viewTo=e.doc.first,e.display.view=[],e.display.viewOffset=0}function jn(e,t,r,n){var i,o=Rn(e,t),l=e.display.view;if(!Ht||r==e.doc.first+e.doc.size)return{index:o,lineN:r};for(var a=e.display.viewFrom,s=0;s<o;s++)a+=l[s].size;if(a!=t){if(n>0){if(o==l.length-1)return null;i=a+l[o].size-t,o++}else i=a-t;t+=i,r+=i}for(;ir(e.doc,r)!=r;){if(o==(n<0?0:l.length-1))return null;r+=n*l[o-(n<0?1:0)].size,o+=n}return{index:o,lineN:r}}function Un(e,t,r){var n=e.display;0==n.view.length||t>=n.viewTo||r<=n.viewFrom?(n.view=Lr(e,t,r),n.viewFrom=t):(n.viewFrom>t?n.view=Lr(e,t,n.viewFrom).concat(n.view):n.viewFrom<t&&(n.view=n.view.slice(Rn(e,t))),n.viewFrom=t,n.viewTo<r?n.view=n.view.concat(Lr(e,n.viewTo,r)):n.viewTo>r&&(n.view=n.view.slice(0,Rn(e,r)))),n.viewTo=r}function Kn(e){for(var t=e.display.view,r=0,n=0;n<t.length;n++){var i=t[n];i.hidden||i.node&&!i.changes||++r}return r}function _n(e){e.display.input.showSelection(e.display.input.prepareSelection())}function Xn(e,t){void 0===t&&(t=!0);var r=e.doc,n={},i=n.cursors=document.createDocumentFragment(),o=n.selection=document.createDocumentFragment(),l=e.options.$customCursor;l&&(t=!0);for(var a=0;a<r.sel.ranges.length;a++)if(t||a!=r.sel.primIndex){var s=r.sel.ranges[a];if(!(s.from().line>=e.display.viewTo||s.to().line<e.display.viewFrom)){var u=s.empty();if(l){var c=l(e,s);c&&$n(e,c,i)}else(u||e.options.showCursorWhenSelecting)&&$n(e,s.head,i);u||qn(e,s,o)}}return n}function $n(e,t,r){var n=Cn(e,t,"div",null,null,!e.options.singleCursorHeightPerLine),i=r.appendChild(A("div"," ","CodeMirror-cursor"));if(i.style.left=n.left+"px",i.style.top=n.top+"px",i.style.height=Math.max(0,n.bottom-n.top)*e.options.cursorHeight+"px",/\bcm-fat-cursor\b/.test(e.getWrapperElement().className)){var o=xn(e,t,"div",null,null),l=o.right-o.left;i.style.width=(l>0?l:e.defaultCharWidth())+"px"}if(n.other){var a=r.appendChild(A("div"," ","CodeMirror-cursor CodeMirror-secondarycursor"));a.style.display="",a.style.left=n.other.left+"px",a.style.top=n.other.top+"px",a.style.height=.85*(n.other.bottom-n.other.top)+"px"}}function Yn(e,t){return e.top-t.top||e.left-t.left}function qn(e,t,r){var n=e.display,i=e.doc,o=document.createDocumentFragment(),l=Yr(e.display),a=l.left,s=Math.max(n.sizerWidth,Zr(e)-n.sizer.offsetLeft)-l.right,u="ltr"==i.direction;function c(e,t,r,n){t<0&&(t=0),t=Math.round(t),n=Math.round(n),o.appendChild(A("div",null,"CodeMirror-selected","position: absolute; left: "+e+"px;\n                             top: "+t+"px; width: "+(null==r?s-e:r)+"px;\n                             height: "+(n-t)+"px"))}function f(t,r,n){var o,l,f=et(i,t),d=f.text.length;function h(r,n){return xn(e,st(t,r),"div",f,n)}function p(t,r,n){var i=Mn(e,f,null,t),o="ltr"==r==("after"==n)?"left":"right";return h("after"==n?i.begin:i.end-(/\s/.test(f.text.charAt(i.end-1))?2:1),o)[o]}var g=ge(f,i.direction);return fe(g,r||0,null==n?d:n,(function(e,t,i,f){var v="ltr"==i,m=h(e,v?"left":"right"),y=h(t-1,v?"right":"left"),b=null==r&&0==e,w=null==n&&t==d,x=0==f,C=!g||f==g.length-1;if(y.top-m.top<=3){var k=(u?w:b)&&C,S=(u?b:w)&&x?a:(v?m:y).left,L=k?s:(v?y:m).right;c(S,m.top,L-S,m.bottom)}else{var T,M,N,O;v?(T=u&&b&&x?a:m.left,M=u?s:p(e,i,"before"),N=u?a:p(t,i,"after"),O=u&&w&&C?s:y.right):(T=u?p(e,i,"before"):a,M=!u&&b&&x?s:m.right,N=!u&&w&&C?a:y.left,O=u?p(t,i,"after"):s),c(T,m.top,M-T,m.bottom),m.bottom<y.top&&c(a,m.bottom,null,y.top),c(N,y.top,O-N,y.bottom)}(!o||Yn(m,o)<0)&&(o=m),Yn(y,o)<0&&(o=y),(!l||Yn(m,l)<0)&&(l=m),Yn(y,l)<0&&(l=y)})),{start:o,end:l}}var d=t.from(),h=t.to();if(d.line==h.line)f(d.line,d.ch,h.ch);else{var p=et(i,d.line),g=et(i,h.line),v=tr(p)==tr(g),m=f(d.line,d.ch,v?p.text.length+1:null).end,y=f(h.line,v?0:null,h.ch).start;v&&(m.top<y.top-2?(c(m.right,m.top,null,m.bottom),c(a,y.top,y.left,y.bottom)):c(m.right,m.top,y.left-m.right,m.bottom)),m.bottom<y.top&&c(a,m.bottom,null,y.top)}r.appendChild(o)}function Zn(e){if(e.state.focused){var t=e.display;clearInterval(t.blinker);var r=!0;t.cursorDiv.style.visibility="",e.options.cursorBlinkRate>0?t.blinker=setInterval((function(){e.hasFocus()||ti(e),t.cursorDiv.style.visibility=(r=!r)?"":"hidden"}),e.options.cursorBlinkRate):e.options.cursorBlinkRate<0&&(t.cursorDiv.style.visibility="hidden")}}function Jn(e){e.hasFocus()||(e.display.input.focus(),e.state.focused||ei(e))}function Qn(e){e.state.delayingBlurEvent=!0,setTimeout((function(){e.state.delayingBlurEvent&&(e.state.delayingBlurEvent=!1,e.state.focused&&ti(e))}),100)}function ei(e,t){e.state.delayingBlurEvent&&!e.state.draggingText&&(e.state.delayingBlurEvent=!1),"nocursor"!=e.options.readOnly&&(e.state.focused||(we(e,"focus",e,t),e.state.focused=!0,F(e.display.wrapper,"CodeMirror-focused"),e.curOp||e.display.selForContextMenu==e.doc.sel||(e.display.input.reset(),s&&setTimeout((function(){return e.display.input.reset(!0)}),20)),e.display.input.receivedFocus()),Zn(e))}function ti(e,t){e.state.delayingBlurEvent||(e.state.focused&&(we(e,"blur",e,t),e.state.focused=!1,M(e.display.wrapper,"CodeMirror-focused")),clearInterval(e.display.blinker),setTimeout((function(){e.state.focused||(e.display.shift=!1)}),150))}function ri(e){for(var t=e.display,r=t.lineDiv.offsetTop,n=Math.max(0,t.scroller.getBoundingClientRect().top),i=t.lineDiv.getBoundingClientRect().top,o=0,s=0;s<t.view.length;s++){var u=t.view[s],c=e.options.lineWrapping,f=void 0,d=0;if(!u.hidden){if(i+=u.line.height,l&&a<8){var h=u.node.offsetTop+u.node.offsetHeight;f=h-r,r=h}else{var p=u.node.getBoundingClientRect();f=p.bottom-p.top,!c&&u.text.firstChild&&(d=u.text.firstChild.getBoundingClientRect().right-p.left-1)}var g=u.line.height-f;if((g>.005||g<-.005)&&(i<n&&(o-=g),nt(u.line,f),ni(u.line),u.rest))for(var v=0;v<u.rest.length;v++)ni(u.rest[v]);if(d>e.display.sizerWidth){var m=Math.ceil(d/Hn(e.display));m>e.display.maxLineLength&&(e.display.maxLineLength=m,e.display.maxLine=u.line,e.display.maxLineChanged=!0)}}}Math.abs(o)>2&&(t.scroller.scrollTop+=o)}function ni(e){if(e.widgets)for(var t=0;t<e.widgets.length;++t){var r=e.widgets[t],n=r.node.parentNode;n&&(r.height=n.offsetHeight)}}function ii(e,t,r){var n=r&&null!=r.top?Math.max(0,r.top):e.scroller.scrollTop;n=Math.floor(n-Xr(e));var i=r&&null!=r.bottom?r.bottom:n+e.wrapper.clientHeight,o=ot(t,n),l=ot(t,i);if(r&&r.ensure){var a=r.ensure.from.line,s=r.ensure.to.line;a<o?(o=a,l=ot(t,sr(et(t,a))+e.wrapper.clientHeight)):Math.min(s,t.lastLine())>=l&&(o=ot(t,sr(et(t,s))-e.wrapper.clientHeight),l=s)}return{from:o,to:Math.max(l,o+1)}}function oi(e,t){if(!xe(e,"scrollCursorIntoView")){var r=e.display,n=r.sizer.getBoundingClientRect(),i=null,o=r.wrapper.ownerDocument;if(t.top+n.top<0?i=!0:t.bottom+n.top>(o.defaultView.innerHeight||o.documentElement.clientHeight)&&(i=!1),null!=i&&!g){var l=A("div","​",null,"position: absolute;\n                         top: "+(t.top-r.viewOffset-Xr(e.display))+"px;\n                         height: "+(t.bottom-t.top+qr(e)+r.barHeight)+"px;\n                         left: "+t.left+"px; width: "+Math.max(2,t.right-t.left)+"px;");e.display.lineSpace.appendChild(l),l.scrollIntoView(i),e.display.lineSpace.removeChild(l)}}}function li(e,t,r,n){var i;null==n&&(n=0),e.options.lineWrapping||t!=r||(r="before"==t.sticky?st(t.line,t.ch+1,"before"):t,t=t.ch?st(t.line,"before"==t.sticky?t.ch-1:t.ch,"after"):t);for(var o=0;o<5;o++){var l=!1,a=Cn(e,t),s=r&&r!=t?Cn(e,r):a,u=si(e,i={left:Math.min(a.left,s.left),top:Math.min(a.top,s.top)-n,right:Math.max(a.left,s.left),bottom:Math.max(a.bottom,s.bottom)+n}),c=e.doc.scrollTop,f=e.doc.scrollLeft;if(null!=u.scrollTop&&(gi(e,u.scrollTop),Math.abs(e.doc.scrollTop-c)>1&&(l=!0)),null!=u.scrollLeft&&(mi(e,u.scrollLeft),Math.abs(e.doc.scrollLeft-f)>1&&(l=!0)),!l)break}return i}function ai(e,t){var r=si(e,t);null!=r.scrollTop&&gi(e,r.scrollTop),null!=r.scrollLeft&&mi(e,r.scrollLeft)}function si(e,t){var r=e.display,n=Wn(e.display);t.top<0&&(t.top=0);var i=e.curOp&&null!=e.curOp.scrollTop?e.curOp.scrollTop:r.scroller.scrollTop,o=Jr(e),l={};t.bottom-t.top>o&&(t.bottom=t.top+o);var a=e.doc.height+$r(r),s=t.top<n,u=t.bottom>a-n;if(t.top<i)l.scrollTop=s?0:t.top;else if(t.bottom>i+o){var c=Math.min(t.top,(u?a:t.bottom)-o);c!=i&&(l.scrollTop=c)}var f=e.options.fixedGutter?0:r.gutters.offsetWidth,d=e.curOp&&null!=e.curOp.scrollLeft?e.curOp.scrollLeft:r.scroller.scrollLeft-f,h=Zr(e)-r.gutters.offsetWidth,p=t.right-t.left>h;return p&&(t.right=t.left+h),t.left<10?l.scrollLeft=0:t.left<d?l.scrollLeft=Math.max(0,t.left+f-(p?0:10)):t.right>h+d-3&&(l.scrollLeft=t.right+(p?0:10)-h),l}function ui(e,t){null!=t&&(hi(e),e.curOp.scrollTop=(null==e.curOp.scrollTop?e.doc.scrollTop:e.curOp.scrollTop)+t)}function ci(e){hi(e);var t=e.getCursor();e.curOp.scrollToPos={from:t,to:t,margin:e.options.cursorScrollMargin}}function fi(e,t,r){null==t&&null==r||hi(e),null!=t&&(e.curOp.scrollLeft=t),null!=r&&(e.curOp.scrollTop=r)}function di(e,t){hi(e),e.curOp.scrollToPos=t}function hi(e){var t=e.curOp.scrollToPos;t&&(e.curOp.scrollToPos=null,pi(e,kn(e,t.from),kn(e,t.to),t.margin))}function pi(e,t,r,n){var i=si(e,{left:Math.min(t.left,r.left),top:Math.min(t.top,r.top)-n,right:Math.max(t.right,r.right),bottom:Math.max(t.bottom,r.bottom)+n});fi(e,i.scrollLeft,i.scrollTop)}function gi(e,t){Math.abs(e.doc.scrollTop-t)<2||(r||_i(e,{top:t}),vi(e,t,!0),r&&_i(e),Ii(e,100))}function vi(e,t,r){t=Math.max(0,Math.min(e.display.scroller.scrollHeight-e.display.scroller.clientHeight,t)),(e.display.scroller.scrollTop!=t||r)&&(e.doc.scrollTop=t,e.display.scrollbars.setScrollTop(t),e.display.scroller.scrollTop!=t&&(e.display.scroller.scrollTop=t))}function mi(e,t,r,n){t=Math.max(0,Math.min(t,e.display.scroller.scrollWidth-e.display.scroller.clientWidth)),(r?t==e.doc.scrollLeft:Math.abs(e.doc.scrollLeft-t)<2)&&!n||(e.doc.scrollLeft=t,qi(e),e.display.scroller.scrollLeft!=t&&(e.display.scroller.scrollLeft=t),e.display.scrollbars.setScrollLeft(t))}function yi(e){var t=e.display,r=t.gutters.offsetWidth,n=Math.round(e.doc.height+$r(e.display));return{clientHeight:t.scroller.clientHeight,viewHeight:t.wrapper.clientHeight,scrollWidth:t.scroller.scrollWidth,clientWidth:t.scroller.clientWidth,viewWidth:t.wrapper.clientWidth,barLeft:e.options.fixedGutter?r:0,docHeight:n,scrollHeight:n+qr(e)+t.barHeight,nativeBarWidth:t.nativeBarWidth,gutterWidth:r}}var bi=function(e,t,r){this.cm=r;var n=this.vert=A("div",[A("div",null,null,"min-width: 1px")],"CodeMirror-vscrollbar"),i=this.horiz=A("div",[A("div",null,null,"height: 100%; min-height: 1px")],"CodeMirror-hscrollbar");n.tabIndex=i.tabIndex=-1,e(n),e(i),me(n,"scroll",(function(){n.clientHeight&&t(n.scrollTop,"vertical")})),me(i,"scroll",(function(){i.clientWidth&&t(i.scrollLeft,"horizontal")})),this.checkedZeroWidth=!1,l&&a<8&&(this.horiz.style.minHeight=this.vert.style.minWidth="18px")};bi.prototype.update=function(e){var t=e.scrollWidth>e.clientWidth+1,r=e.scrollHeight>e.clientHeight+1,n=e.nativeBarWidth;if(r){this.vert.style.display="block",this.vert.style.bottom=t?n+"px":"0";var i=e.viewHeight-(t?n:0);this.vert.firstChild.style.height=Math.max(0,e.scrollHeight-e.clientHeight+i)+"px"}else this.vert.scrollTop=0,this.vert.style.display="",this.vert.firstChild.style.height="0";if(t){this.horiz.style.display="block",this.horiz.style.right=r?n+"px":"0",this.horiz.style.left=e.barLeft+"px";var o=e.viewWidth-e.barLeft-(r?n:0);this.horiz.firstChild.style.width=Math.max(0,e.scrollWidth-e.clientWidth+o)+"px"}else this.horiz.style.display="",this.horiz.firstChild.style.width="0";return!this.checkedZeroWidth&&e.clientHeight>0&&(0==n&&this.zeroWidthHack(),this.checkedZeroWidth=!0),{right:r?n:0,bottom:t?n:0}},bi.prototype.setScrollLeft=function(e){this.horiz.scrollLeft!=e&&(this.horiz.scrollLeft=e),this.disableHoriz&&this.enableZeroWidthBar(this.horiz,this.disableHoriz,"horiz")},bi.prototype.setScrollTop=function(e){this.vert.scrollTop!=e&&(this.vert.scrollTop=e),this.disableVert&&this.enableZeroWidthBar(this.vert,this.disableVert,"vert")},bi.prototype.zeroWidthHack=function(){var e=b&&!p?"12px":"18px";this.horiz.style.height=this.vert.style.width=e,this.horiz.style.visibility=this.vert.style.visibility="hidden",this.disableHoriz=new G,this.disableVert=new G},bi.prototype.enableZeroWidthBar=function(e,t,r){function n(){var i=e.getBoundingClientRect();("vert"==r?document.elementFromPoint(i.right-1,(i.top+i.bottom)/2):document.elementFromPoint((i.right+i.left)/2,i.bottom-1))!=e?e.style.visibility="hidden":t.set(1e3,n)}e.style.visibility="",t.set(1e3,n)},bi.prototype.clear=function(){var e=this.horiz.parentNode;e.removeChild(this.horiz),e.removeChild(this.vert)};var wi=function(){};function xi(e,t){t||(t=yi(e));var r=e.display.barWidth,n=e.display.barHeight;Ci(e,t);for(var i=0;i<4&&r!=e.display.barWidth||n!=e.display.barHeight;i++)r!=e.display.barWidth&&e.options.lineWrapping&&ri(e),Ci(e,yi(e)),r=e.display.barWidth,n=e.display.barHeight}function Ci(e,t){var r=e.display,n=r.scrollbars.update(t);r.sizer.style.paddingRight=(r.barWidth=n.right)+"px",r.sizer.style.paddingBottom=(r.barHeight=n.bottom)+"px",r.heightForcer.style.borderBottom=n.bottom+"px solid transparent",n.right&&n.bottom?(r.scrollbarFiller.style.display="block",r.scrollbarFiller.style.height=n.bottom+"px",r.scrollbarFiller.style.width=n.right+"px"):r.scrollbarFiller.style.display="",n.bottom&&e.options.coverGutterNextToScrollbar&&e.options.fixedGutter?(r.gutterFiller.style.display="block",r.gutterFiller.style.height=n.bottom+"px",r.gutterFiller.style.width=t.gutterWidth+"px"):r.gutterFiller.style.display=""}wi.prototype.update=function(){return{bottom:0,right:0}},wi.prototype.setScrollLeft=function(){},wi.prototype.setScrollTop=function(){},wi.prototype.clear=function(){};var ki={native:bi,null:wi};function Si(e){e.display.scrollbars&&(e.display.scrollbars.clear(),e.display.scrollbars.addClass&&M(e.display.wrapper,e.display.scrollbars.addClass)),e.display.scrollbars=new ki[e.options.scrollbarStyle]((function(t){e.display.wrapper.insertBefore(t,e.display.scrollbarFiller),me(t,"mousedown",(function(){e.state.focused&&setTimeout((function(){return e.display.input.focus()}),0)})),t.setAttribute("cm-not-content","true")}),(function(t,r){"horizontal"==r?mi(e,t):gi(e,t)}),e),e.display.scrollbars.addClass&&F(e.display.wrapper,e.display.scrollbars.addClass)}var Li=0;function Ti(e){e.curOp={cm:e,viewChanged:!1,startHeight:e.doc.height,forceUpdate:!1,updateInput:0,typing:!1,changeObjs:null,cursorActivityHandlers:null,cursorActivityCalled:0,selectionChanged:!1,updateMaxLine:!1,scrollLeft:null,scrollTop:null,scrollToPos:null,focus:!1,id:++Li,markArrays:null},Mr(e.curOp)}function Mi(e){var t=e.curOp;t&&Or(t,(function(e){for(var t=0;t<e.ops.length;t++)e.ops[t].cm.curOp=null;Ni(e)}))}function Ni(e){for(var t=e.ops,r=0;r<t.length;r++)Oi(t[r]);for(var n=0;n<t.length;n++)Ai(t[n]);for(var i=0;i<t.length;i++)Di(t[i]);for(var o=0;o<t.length;o++)Wi(t[o]);for(var l=0;l<t.length;l++)Hi(t[l])}function Oi(e){var t=e.cm,r=t.display;Vi(t),e.updateMaxLine&&cr(t),e.mustUpdate=e.viewChanged||e.forceUpdate||null!=e.scrollTop||e.scrollToPos&&(e.scrollToPos.from.line<r.viewFrom||e.scrollToPos.to.line>=r.viewTo)||r.maxLineChanged&&t.options.lineWrapping,e.update=e.mustUpdate&&new Bi(t,e.mustUpdate&&{top:e.scrollTop,ensure:e.scrollToPos},e.forceUpdate)}function Ai(e){e.updatedDisplay=e.mustUpdate&&Ui(e.cm,e.update)}function Di(e){var t=e.cm,r=t.display;e.updatedDisplay&&ri(t),e.barMeasure=yi(t),r.maxLineChanged&&!t.options.lineWrapping&&(e.adjustWidthTo=rn(t,r.maxLine,r.maxLine.text.length).left+3,t.display.sizerWidth=e.adjustWidthTo,e.barMeasure.scrollWidth=Math.max(r.scroller.clientWidth,r.sizer.offsetLeft+e.adjustWidthTo+qr(t)+t.display.barWidth),e.maxScrollLeft=Math.max(0,r.sizer.offsetLeft+e.adjustWidthTo-Zr(t))),(e.updatedDisplay||e.selectionChanged)&&(e.preparedSelection=r.input.prepareSelection())}function Wi(e){var t=e.cm;null!=e.adjustWidthTo&&(t.display.sizer.style.minWidth=e.adjustWidthTo+"px",e.maxScrollLeft<t.doc.scrollLeft&&mi(t,Math.min(t.display.scroller.scrollLeft,e.maxScrollLeft),!0),t.display.maxLineChanged=!1);var r=e.focus&&e.focus==H(z(t));e.preparedSelection&&t.display.input.showSelection(e.preparedSelection,r),(e.updatedDisplay||e.startHeight!=t.doc.height)&&xi(t,e.barMeasure),e.updatedDisplay&&Yi(t,e.barMeasure),e.selectionChanged&&Zn(t),t.state.focused&&e.updateInput&&t.display.input.reset(e.typing),r&&Jn(e.cm)}function Hi(e){var t=e.cm,r=t.display,n=t.doc;e.updatedDisplay&&Ki(t,e.update),null==r.wheelStartX||null==e.scrollTop&&null==e.scrollLeft&&!e.scrollToPos||(r.wheelStartX=r.wheelStartY=null),null!=e.scrollTop&&vi(t,e.scrollTop,e.forceScroll),null!=e.scrollLeft&&mi(t,e.scrollLeft,!0,!0),e.scrollToPos&&oi(t,li(t,gt(n,e.scrollToPos.from),gt(n,e.scrollToPos.to),e.scrollToPos.margin));var i=e.maybeHiddenMarkers,o=e.maybeUnhiddenMarkers;if(i)for(var l=0;l<i.length;++l)i[l].lines.length||we(i[l],"hide");if(o)for(var a=0;a<o.length;++a)o[a].lines.length&&we(o[a],"unhide");r.wrapper.offsetHeight&&(n.scrollTop=t.display.scroller.scrollTop),e.changeObjs&&we(t,"changes",t,e.changeObjs),e.update&&e.update.finish()}function Fi(e,t){if(e.curOp)return t();Ti(e);try{return t()}finally{Mi(e)}}function Ei(e,t){return function(){if(e.curOp)return t.apply(e,arguments);Ti(e);try{return t.apply(e,arguments)}finally{Mi(e)}}}function Pi(e){return function(){if(this.curOp)return e.apply(this,arguments);Ti(this);try{return e.apply(this,arguments)}finally{Mi(this)}}}function zi(e){return function(){var t=this.cm;if(!t||t.curOp)return e.apply(this,arguments);Ti(t);try{return e.apply(this,arguments)}finally{Mi(t)}}}function Ii(e,t){e.doc.highlightFrontier<e.display.viewTo&&e.state.highlight.set(t,R(Ri,e))}function Ri(e){var t=e.doc;if(!(t.highlightFrontier>=e.display.viewTo)){var r=+new Date+e.options.workTime,n=Ct(e,t.highlightFrontier),i=[];t.iter(n.line,Math.min(t.first+t.size,e.display.viewTo+500),(function(o){if(n.line>=e.display.viewFrom){var l=o.styles,a=o.text.length>e.options.maxHighlightLength?qe(t.mode,n.state):null,s=wt(e,o,n,!0);a&&(n.state=a),o.styles=s.styles;var u=o.styleClasses,c=s.classes;c?o.styleClasses=c:u&&(o.styleClasses=null);for(var f=!l||l.length!=o.styles.length||u!=c&&(!u||!c||u.bgClass!=c.bgClass||u.textClass!=c.textClass),d=0;!f&&d<l.length;++d)f=l[d]!=o.styles[d];f&&i.push(n.line),o.stateAfter=n.save(),n.nextLine()}else o.text.length<=e.options.maxHighlightLength&&kt(e,o.text,n),o.stateAfter=n.line%5==0?n.save():null,n.nextLine();if(+new Date>r)return Ii(e,e.options.workDelay),!0})),t.highlightFrontier=n.line,t.modeFrontier=Math.max(t.modeFrontier,n.line),i.length&&Fi(e,(function(){for(var t=0;t<i.length;t++)Vn(e,i[t],"text")}))}}var Bi=function(e,t,r){var n=e.display;this.viewport=t,this.visible=ii(n,e.doc,t),this.editorIsHidden=!n.wrapper.offsetWidth,this.wrapperHeight=n.wrapper.clientHeight,this.wrapperWidth=n.wrapper.clientWidth,this.oldDisplayWidth=Zr(e),this.force=r,this.dims=Fn(e),this.events=[]};function Vi(e){var t=e.display;!t.scrollbarsClipped&&t.scroller.offsetWidth&&(t.nativeBarWidth=t.scroller.offsetWidth-t.scroller.clientWidth,t.heightForcer.style.height=qr(e)+"px",t.sizer.style.marginBottom=-t.nativeBarWidth+"px",t.sizer.style.borderRightWidth=qr(e)+"px",t.scrollbarsClipped=!0)}function Gi(e){if(e.hasFocus())return null;var t=H(z(e));if(!t||!W(e.display.lineDiv,t))return null;var r={activeElt:t};if(window.getSelection){var n=I(e).getSelection();n.anchorNode&&n.extend&&W(e.display.lineDiv,n.anchorNode)&&(r.anchorNode=n.anchorNode,r.anchorOffset=n.anchorOffset,r.focusNode=n.focusNode,r.focusOffset=n.focusOffset)}return r}function ji(e){if(e&&e.activeElt&&e.activeElt!=H(e.activeElt.ownerDocument)&&(e.activeElt.focus(),!/^(INPUT|TEXTAREA)$/.test(e.activeElt.nodeName)&&e.anchorNode&&W(document.body,e.anchorNode)&&W(document.body,e.focusNode))){var t=e.activeElt.ownerDocument,r=t.defaultView.getSelection(),n=t.createRange();n.setEnd(e.anchorNode,e.anchorOffset),n.collapse(!1),r.removeAllRanges(),r.addRange(n),r.extend(e.focusNode,e.focusOffset)}}function Ui(e,t){var r=e.display,n=e.doc;if(t.editorIsHidden)return Gn(e),!1;if(!t.force&&t.visible.from>=r.viewFrom&&t.visible.to<=r.viewTo&&(null==r.updateLineNumbers||r.updateLineNumbers>=r.viewTo)&&r.renderedView==r.view&&0==Kn(e))return!1;Zi(e)&&(Gn(e),t.dims=Fn(e));var i=n.first+n.size,o=Math.max(t.visible.from-e.options.viewportMargin,n.first),l=Math.min(i,t.visible.to+e.options.viewportMargin);r.viewFrom<o&&o-r.viewFrom<20&&(o=Math.max(n.first,r.viewFrom)),r.viewTo>l&&r.viewTo-l<20&&(l=Math.min(i,r.viewTo)),Ht&&(o=ir(e.doc,o),l=or(e.doc,l));var a=o!=r.viewFrom||l!=r.viewTo||r.lastWrapHeight!=t.wrapperHeight||r.lastWrapWidth!=t.wrapperWidth;Un(e,o,l),r.viewOffset=sr(et(e.doc,r.viewFrom)),e.display.mover.style.top=r.viewOffset+"px";var s=Kn(e);if(!a&&0==s&&!t.force&&r.renderedView==r.view&&(null==r.updateLineNumbers||r.updateLineNumbers>=r.viewTo))return!1;var u=Gi(e);return s>4&&(r.lineDiv.style.display="none"),Xi(e,r.updateLineNumbers,t.dims),s>4&&(r.lineDiv.style.display=""),r.renderedView=r.view,ji(u),N(r.cursorDiv),N(r.selectionDiv),r.gutters.style.height=r.sizer.style.minHeight=0,a&&(r.lastWrapHeight=t.wrapperHeight,r.lastWrapWidth=t.wrapperWidth,Ii(e,400)),r.updateLineNumbers=null,!0}function Ki(e,t){for(var r=t.viewport,n=!0;;n=!1){if(n&&e.options.lineWrapping&&t.oldDisplayWidth!=Zr(e))n&&(t.visible=ii(e.display,e.doc,r));else if(r&&null!=r.top&&(r={top:Math.min(e.doc.height+$r(e.display)-Jr(e),r.top)}),t.visible=ii(e.display,e.doc,r),t.visible.from>=e.display.viewFrom&&t.visible.to<=e.display.viewTo)break;if(!Ui(e,t))break;ri(e);var i=yi(e);_n(e),xi(e,i),Yi(e,i),t.force=!1}t.signal(e,"update",e),e.display.viewFrom==e.display.reportedViewFrom&&e.display.viewTo==e.display.reportedViewTo||(t.signal(e,"viewportChange",e,e.display.viewFrom,e.display.viewTo),e.display.reportedViewFrom=e.display.viewFrom,e.display.reportedViewTo=e.display.viewTo)}function _i(e,t){var r=new Bi(e,t);if(Ui(e,r)){ri(e),Ki(e,r);var n=yi(e);_n(e),xi(e,n),Yi(e,n),r.finish()}}function Xi(e,t,r){var n=e.display,i=e.options.lineNumbers,o=n.lineDiv,l=o.firstChild;function a(t){var r=t.nextSibling;return s&&b&&e.display.currentWheelTarget==t?t.style.display="none":t.parentNode.removeChild(t),r}for(var u=n.view,c=n.viewFrom,f=0;f<u.length;f++){var d=u[f];if(d.hidden);else if(d.node&&d.node.parentNode==o){for(;l!=d.node;)l=a(l);var h=i&&null!=t&&t<=c&&d.lineNumber;d.changes&&(j(d.changes,"gutter")>-1&&(h=!1),Hr(e,d,c,r)),h&&(N(d.lineNumber),d.lineNumber.appendChild(document.createTextNode(at(e.options,c)))),l=d.node.nextSibling}else{var p=Vr(e,d,c,r);o.insertBefore(p,l)}c+=d.size}for(;l;)l=a(l)}function $i(e){var t=e.gutters.offsetWidth;e.sizer.style.marginLeft=t+"px",Dr(e,"gutterChanged",e)}function Yi(e,t){e.display.sizer.style.minHeight=t.docHeight+"px",e.display.heightForcer.style.top=t.docHeight+"px",e.display.gutters.style.height=t.docHeight+e.display.barHeight+qr(e)+"px"}function qi(e){var t=e.display,r=t.view;if(t.alignWidgets||t.gutters.firstChild&&e.options.fixedGutter){for(var n=En(t)-t.scroller.scrollLeft+e.doc.scrollLeft,i=t.gutters.offsetWidth,o=n+"px",l=0;l<r.length;l++)if(!r[l].hidden){e.options.fixedGutter&&(r[l].gutter&&(r[l].gutter.style.left=o),r[l].gutterBackground&&(r[l].gutterBackground.style.left=o));var a=r[l].alignable;if(a)for(var s=0;s<a.length;s++)a[s].style.left=o}e.options.fixedGutter&&(t.gutters.style.left=n+i+"px")}}function Zi(e){if(!e.options.lineNumbers)return!1;var t=e.doc,r=at(e.options,t.first+t.size-1),n=e.display;if(r.length!=n.lineNumChars){var i=n.measure.appendChild(A("div",[A("div",r)],"CodeMirror-linenumber CodeMirror-gutter-elt")),o=i.firstChild.offsetWidth,l=i.offsetWidth-o;return n.lineGutter.style.width="",n.lineNumInnerWidth=Math.max(o,n.lineGutter.offsetWidth-l)+1,n.lineNumWidth=n.lineNumInnerWidth+l,n.lineNumChars=n.lineNumInnerWidth?r.length:-1,n.lineGutter.style.width=n.lineNumWidth+"px",$i(e.display),!0}return!1}function Ji(e,t){for(var r=[],n=!1,i=0;i<e.length;i++){var o=e[i],l=null;if("string"!=typeof o&&(l=o.style,o=o.className),"CodeMirror-linenumbers"==o){if(!t)continue;n=!0}r.push({className:o,style:l})}return t&&!n&&r.push({className:"CodeMirror-linenumbers",style:null}),r}function Qi(e){var t=e.gutters,r=e.gutterSpecs;N(t),e.lineGutter=null;for(var n=0;n<r.length;++n){var i=r[n],o=i.className,l=i.style,a=t.appendChild(A("div",null,"CodeMirror-gutter "+o));l&&(a.style.cssText=l),"CodeMirror-linenumbers"==o&&(e.lineGutter=a,a.style.width=(e.lineNumWidth||1)+"px")}t.style.display=r.length?"":"none",$i(e)}function eo(e){Qi(e.display),Bn(e),qi(e)}function to(e,t,n,i){var o=this;this.input=n,o.scrollbarFiller=A("div",null,"CodeMirror-scrollbar-filler"),o.scrollbarFiller.setAttribute("cm-not-content","true"),o.gutterFiller=A("div",null,"CodeMirror-gutter-filler"),o.gutterFiller.setAttribute("cm-not-content","true"),o.lineDiv=D("div",null,"CodeMirror-code"),o.selectionDiv=A("div",null,null,"position: relative; z-index: 1"),o.cursorDiv=A("div",null,"CodeMirror-cursors"),o.measure=A("div",null,"CodeMirror-measure"),o.lineMeasure=A("div",null,"CodeMirror-measure"),o.lineSpace=D("div",[o.measure,o.lineMeasure,o.selectionDiv,o.cursorDiv,o.lineDiv],null,"position: relative; outline: none");var u=D("div",[o.lineSpace],"CodeMirror-lines");o.mover=A("div",[u],null,"position: relative"),o.sizer=A("div",[o.mover],"CodeMirror-sizer"),o.sizerWidth=null,o.heightForcer=A("div",null,null,"position: absolute; height: "+U+"px; width: 1px;"),o.gutters=A("div",null,"CodeMirror-gutters"),o.lineGutter=null,o.scroller=A("div",[o.sizer,o.heightForcer,o.gutters],"CodeMirror-scroll"),o.scroller.setAttribute("tabIndex","-1"),o.wrapper=A("div",[o.scrollbarFiller,o.gutterFiller,o.scroller],"CodeMirror"),c&&f>=105&&(o.wrapper.style.clipPath="inset(0px)"),o.wrapper.setAttribute("translate","no"),l&&a<8&&(o.gutters.style.zIndex=-1,o.scroller.style.paddingRight=0),s||r&&y||(o.scroller.draggable=!0),e&&(e.appendChild?e.appendChild(o.wrapper):e(o.wrapper)),o.viewFrom=o.viewTo=t.first,o.reportedViewFrom=o.reportedViewTo=t.first,o.view=[],o.renderedView=null,o.externalMeasured=null,o.viewOffset=0,o.lastWrapHeight=o.lastWrapWidth=0,o.updateLineNumbers=null,o.nativeBarWidth=o.barHeight=o.barWidth=0,o.scrollbarsClipped=!1,o.lineNumWidth=o.lineNumInnerWidth=o.lineNumChars=null,o.alignWidgets=!1,o.cachedCharWidth=o.cachedTextHeight=o.cachedPaddingH=null,o.maxLine=null,o.maxLineLength=0,o.maxLineChanged=!1,o.wheelDX=o.wheelDY=o.wheelStartX=o.wheelStartY=null,o.shift=!1,o.selForContextMenu=null,o.activeTouch=null,o.gutterSpecs=Ji(i.gutters,i.lineNumbers),Qi(o),n.init(o)}Bi.prototype.signal=function(e,t){ke(e,t)&&this.events.push(arguments)},Bi.prototype.finish=function(){for(var e=0;e<this.events.length;e++)we.apply(null,this.events[e])};var ro=0,no=null;function io(e){var t=e.wheelDeltaX,r=e.wheelDeltaY;return null==t&&e.detail&&e.axis==e.HORIZONTAL_AXIS&&(t=e.detail),null==r&&e.detail&&e.axis==e.VERTICAL_AXIS?r=e.detail:null==r&&(r=e.wheelDelta),{x:t,y:r}}function oo(e){var t=io(e);return t.x*=no,t.y*=no,t}function lo(e,t){c&&102==f&&(null==e.display.chromeScrollHack?e.display.sizer.style.pointerEvents="none":clearTimeout(e.display.chromeScrollHack),e.display.chromeScrollHack=setTimeout((function(){e.display.chromeScrollHack=null,e.display.sizer.style.pointerEvents=""}),100));var n=io(t),i=n.x,o=n.y,l=no;0===t.deltaMode&&(i=t.deltaX,o=t.deltaY,l=1);var a=e.display,u=a.scroller,h=u.scrollWidth>u.clientWidth,p=u.scrollHeight>u.clientHeight;if(i&&h||o&&p){if(o&&b&&s)e:for(var g=t.target,v=a.view;g!=u;g=g.parentNode)for(var m=0;m<v.length;m++)if(v[m].node==g){e.display.currentWheelTarget=g;break e}if(i&&!r&&!d&&null!=l)return o&&p&&gi(e,Math.max(0,u.scrollTop+o*l)),mi(e,Math.max(0,u.scrollLeft+i*l)),(!o||o&&p)&&Le(t),void(a.wheelStartX=null);if(o&&null!=l){var y=o*l,w=e.doc.scrollTop,x=w+a.wrapper.clientHeight;y<0?w=Math.max(0,w+y-50):x=Math.min(e.doc.height,x+y+50),_i(e,{top:w,bottom:x})}ro<20&&0!==t.deltaMode&&(null==a.wheelStartX?(a.wheelStartX=u.scrollLeft,a.wheelStartY=u.scrollTop,a.wheelDX=i,a.wheelDY=o,setTimeout((function(){if(null!=a.wheelStartX){var e=u.scrollLeft-a.wheelStartX,t=u.scrollTop-a.wheelStartY,r=t&&a.wheelDY&&t/a.wheelDY||e&&a.wheelDX&&e/a.wheelDX;a.wheelStartX=a.wheelStartY=null,r&&(no=(no*ro+r)/(ro+1),++ro)}}),200)):(a.wheelDX+=i,a.wheelDY+=o))}}l?no=-.53:r?no=15:c?no=-.7:h&&(no=-1/3);var ao=function(e,t){this.ranges=e,this.primIndex=t};ao.prototype.primary=function(){return this.ranges[this.primIndex]},ao.prototype.equals=function(e){if(e==this)return!0;if(e.primIndex!=this.primIndex||e.ranges.length!=this.ranges.length)return!1;for(var t=0;t<this.ranges.length;t++){var r=this.ranges[t],n=e.ranges[t];if(!ct(r.anchor,n.anchor)||!ct(r.head,n.head))return!1}return!0},ao.prototype.deepCopy=function(){for(var e=[],t=0;t<this.ranges.length;t++)e[t]=new so(ft(this.ranges[t].anchor),ft(this.ranges[t].head));return new ao(e,this.primIndex)},ao.prototype.somethingSelected=function(){for(var e=0;e<this.ranges.length;e++)if(!this.ranges[e].empty())return!0;return!1},ao.prototype.contains=function(e,t){t||(t=e);for(var r=0;r<this.ranges.length;r++){var n=this.ranges[r];if(ut(t,n.from())>=0&&ut(e,n.to())<=0)return r}return-1};var so=function(e,t){this.anchor=e,this.head=t};function uo(e,t,r){var n=e&&e.options.selectionsMayTouch,i=t[r];t.sort((function(e,t){return ut(e.from(),t.from())})),r=j(t,i);for(var o=1;o<t.length;o++){var l=t[o],a=t[o-1],s=ut(a.to(),l.from());if(n&&!l.empty()?s>0:s>=0){var u=ht(a.from(),l.from()),c=dt(a.to(),l.to()),f=a.empty()?l.from()==l.head:a.from()==a.head;o<=r&&--r,t.splice(--o,2,new so(f?c:u,f?u:c))}}return new ao(t,r)}function co(e,t){return new ao([new so(e,t||e)],0)}function fo(e){return e.text?st(e.from.line+e.text.length-1,J(e.text).length+(1==e.text.length?e.from.ch:0)):e.to}function ho(e,t){if(ut(e,t.from)<0)return e;if(ut(e,t.to)<=0)return fo(t);var r=e.line+t.text.length-(t.to.line-t.from.line)-1,n=e.ch;return e.line==t.to.line&&(n+=fo(t).ch-t.to.ch),st(r,n)}function po(e,t){for(var r=[],n=0;n<e.sel.ranges.length;n++){var i=e.sel.ranges[n];r.push(new so(ho(i.anchor,t),ho(i.head,t)))}return uo(e.cm,r,e.sel.primIndex)}function go(e,t,r){return e.line==t.line?st(r.line,e.ch-t.ch+r.ch):st(r.line+(e.line-t.line),e.ch)}function vo(e,t,r){for(var n=[],i=st(e.first,0),o=i,l=0;l<t.length;l++){var a=t[l],s=go(a.from,i,o),u=go(fo(a),i,o);if(i=a.to,o=u,"around"==r){var c=e.sel.ranges[l],f=ut(c.head,c.anchor)<0;n[l]=new so(f?u:s,f?s:u)}else n[l]=new so(s,s)}return new ao(n,e.sel.primIndex)}function mo(e){e.doc.mode=Xe(e.options,e.doc.modeOption),yo(e)}function yo(e){e.doc.iter((function(e){e.stateAfter&&(e.stateAfter=null),e.styles&&(e.styles=null)})),e.doc.modeFrontier=e.doc.highlightFrontier=e.doc.first,Ii(e,100),e.state.modeGen++,e.curOp&&Bn(e)}function bo(e,t){return 0==t.from.ch&&0==t.to.ch&&""==J(t.text)&&(!e.cm||e.cm.options.wholeLineUpdateBefore)}function wo(e,t,r,n){function i(e){return r?r[e]:null}function o(e,r,i){dr(e,r,i,n),Dr(e,"change",e,t)}function l(e,t){for(var r=[],o=e;o<t;++o)r.push(new fr(u[o],i(o),n));return r}var a=t.from,s=t.to,u=t.text,c=et(e,a.line),f=et(e,s.line),d=J(u),h=i(u.length-1),p=s.line-a.line;if(t.full)e.insert(0,l(0,u.length)),e.remove(u.length,e.size-u.length);else if(bo(e,t)){var g=l(0,u.length-1);o(f,f.text,h),p&&e.remove(a.line,p),g.length&&e.insert(a.line,g)}else if(c==f)if(1==u.length)o(c,c.text.slice(0,a.ch)+d+c.text.slice(s.ch),h);else{var v=l(1,u.length-1);v.push(new fr(d+c.text.slice(s.ch),h,n)),o(c,c.text.slice(0,a.ch)+u[0],i(0)),e.insert(a.line+1,v)}else if(1==u.length)o(c,c.text.slice(0,a.ch)+u[0]+f.text.slice(s.ch),i(0)),e.remove(a.line+1,p);else{o(c,c.text.slice(0,a.ch)+u[0],i(0)),o(f,d+f.text.slice(s.ch),h);var m=l(1,u.length-1);p>1&&e.remove(a.line+1,p-1),e.insert(a.line+1,m)}Dr(e,"change",e,t)}function xo(e,t,r){function n(e,i,o){if(e.linked)for(var l=0;l<e.linked.length;++l){var a=e.linked[l];if(a.doc!=i){var s=o&&a.sharedHist;r&&!s||(t(a.doc,s),n(a.doc,e,s))}}}n(e,null,!0)}function Co(e,t){if(t.cm)throw new Error("This document is already in use.");e.doc=t,t.cm=e,zn(e),mo(e),ko(e),e.options.direction=t.direction,e.options.lineWrapping||cr(e),e.options.mode=t.modeOption,Bn(e)}function ko(e){("rtl"==e.doc.direction?F:M)(e.display.lineDiv,"CodeMirror-rtl")}function So(e){Fi(e,(function(){ko(e),Bn(e)}))}function Lo(e){this.done=[],this.undone=[],this.undoDepth=e?e.undoDepth:1/0,this.lastModTime=this.lastSelTime=0,this.lastOp=this.lastSelOp=null,this.lastOrigin=this.lastSelOrigin=null,this.generation=this.maxGeneration=e?e.maxGeneration:1}function To(e,t){var r={from:ft(t.from),to:fo(t),text:tt(e,t.from,t.to)};return Ho(e,r,t.from.line,t.to.line+1),xo(e,(function(e){return Ho(e,r,t.from.line,t.to.line+1)}),!0),r}function Mo(e){for(;e.length&&J(e).ranges;)e.pop()}function No(e,t){return t?(Mo(e.done),J(e.done)):e.done.length&&!J(e.done).ranges?J(e.done):e.done.length>1&&!e.done[e.done.length-2].ranges?(e.done.pop(),J(e.done)):void 0}function Oo(e,t,r,n){var i=e.history;i.undone.length=0;var o,l,a=+new Date;if((i.lastOp==n||i.lastOrigin==t.origin&&t.origin&&("+"==t.origin.charAt(0)&&i.lastModTime>a-(e.cm?e.cm.options.historyEventDelay:500)||"*"==t.origin.charAt(0)))&&(o=No(i,i.lastOp==n)))l=J(o.changes),0==ut(t.from,t.to)&&0==ut(t.from,l.to)?l.to=fo(t):o.changes.push(To(e,t));else{var s=J(i.done);for(s&&s.ranges||Wo(e.sel,i.done),o={changes:[To(e,t)],generation:i.generation},i.done.push(o);i.done.length>i.undoDepth;)i.done.shift(),i.done[0].ranges||i.done.shift()}i.done.push(r),i.generation=++i.maxGeneration,i.lastModTime=i.lastSelTime=a,i.lastOp=i.lastSelOp=n,i.lastOrigin=i.lastSelOrigin=t.origin,l||we(e,"historyAdded")}function Ao(e,t,r,n){var i=t.charAt(0);return"*"==i||"+"==i&&r.ranges.length==n.ranges.length&&r.somethingSelected()==n.somethingSelected()&&new Date-e.history.lastSelTime<=(e.cm?e.cm.options.historyEventDelay:500)}function Do(e,t,r,n){var i=e.history,o=n&&n.origin;r==i.lastSelOp||o&&i.lastSelOrigin==o&&(i.lastModTime==i.lastSelTime&&i.lastOrigin==o||Ao(e,o,J(i.done),t))?i.done[i.done.length-1]=t:Wo(t,i.done),i.lastSelTime=+new Date,i.lastSelOrigin=o,i.lastSelOp=r,n&&!1!==n.clearRedo&&Mo(i.undone)}function Wo(e,t){var r=J(t);r&&r.ranges&&r.equals(e)||t.push(e)}function Ho(e,t,r,n){var i=t["spans_"+e.id],o=0;e.iter(Math.max(e.first,r),Math.min(e.first+e.size,n),(function(r){r.markedSpans&&((i||(i=t["spans_"+e.id]={}))[o]=r.markedSpans),++o}))}function Fo(e){if(!e)return null;for(var t,r=0;r<e.length;++r)e[r].marker.explicitlyCleared?t||(t=e.slice(0,r)):t&&t.push(e[r]);return t?t.length?t:null:e}function Eo(e,t){var r=t["spans_"+e.id];if(!r)return null;for(var n=[],i=0;i<t.text.length;++i)n.push(Fo(r[i]));return n}function Po(e,t){var r=Eo(e,t),n=Gt(e,t);if(!r)return n;if(!n)return r;for(var i=0;i<r.length;++i){var o=r[i],l=n[i];if(o&&l)e:for(var a=0;a<l.length;++a){for(var s=l[a],u=0;u<o.length;++u)if(o[u].marker==s.marker)continue e;o.push(s)}else l&&(r[i]=l)}return r}function zo(e,t,r){for(var n=[],i=0;i<e.length;++i){var o=e[i];if(o.ranges)n.push(r?ao.prototype.deepCopy.call(o):o);else{var l=o.changes,a=[];n.push({changes:a});for(var s=0;s<l.length;++s){var u=l[s],c=void 0;if(a.push({from:u.from,to:u.to,text:u.text}),t)for(var f in u)(c=f.match(/^spans_(\d+)$/))&&j(t,Number(c[1]))>-1&&(J(a)[f]=u[f],delete u[f])}}}return n}function Io(e,t,r,n){if(n){var i=e.anchor;if(r){var o=ut(t,i)<0;o!=ut(r,i)<0?(i=t,t=r):o!=ut(t,r)<0&&(t=r)}return new so(i,t)}return new so(r||t,t)}function Ro(e,t,r,n,i){null==i&&(i=e.cm&&(e.cm.display.shift||e.extend)),Ko(e,new ao([Io(e.sel.primary(),t,r,i)],0),n)}function Bo(e,t,r){for(var n=[],i=e.cm&&(e.cm.display.shift||e.extend),o=0;o<e.sel.ranges.length;o++)n[o]=Io(e.sel.ranges[o],t[o],null,i);Ko(e,uo(e.cm,n,e.sel.primIndex),r)}function Vo(e,t,r,n){var i=e.sel.ranges.slice(0);i[t]=r,Ko(e,uo(e.cm,i,e.sel.primIndex),n)}function Go(e,t,r,n){Ko(e,co(t,r),n)}function jo(e,t,r){var n={ranges:t.ranges,update:function(t){this.ranges=[];for(var r=0;r<t.length;r++)this.ranges[r]=new so(gt(e,t[r].anchor),gt(e,t[r].head))},origin:r&&r.origin};return we(e,"beforeSelectionChange",e,n),e.cm&&we(e.cm,"beforeSelectionChange",e.cm,n),n.ranges!=t.ranges?uo(e.cm,n.ranges,n.ranges.length-1):t}function Uo(e,t,r){var n=e.history.done,i=J(n);i&&i.ranges?(n[n.length-1]=t,_o(e,t,r)):Ko(e,t,r)}function Ko(e,t,r){_o(e,t,r),Do(e,e.sel,e.cm?e.cm.curOp.id:NaN,r)}function _o(e,t,r){(ke(e,"beforeSelectionChange")||e.cm&&ke(e.cm,"beforeSelectionChange"))&&(t=jo(e,t,r));var n=r&&r.bias||(ut(t.primary().head,e.sel.primary().head)<0?-1:1);Xo(e,Yo(e,t,n,!0)),r&&!1===r.scroll||!e.cm||"nocursor"==e.cm.getOption("readOnly")||ci(e.cm)}function Xo(e,t){t.equals(e.sel)||(e.sel=t,e.cm&&(e.cm.curOp.updateInput=1,e.cm.curOp.selectionChanged=!0,Ce(e.cm)),Dr(e,"cursorActivity",e))}function $o(e){Xo(e,Yo(e,e.sel,null,!1))}function Yo(e,t,r,n){for(var i,o=0;o<t.ranges.length;o++){var l=t.ranges[o],a=t.ranges.length==e.sel.ranges.length&&e.sel.ranges[o],s=Zo(e,l.anchor,a&&a.anchor,r,n),u=l.head==l.anchor?s:Zo(e,l.head,a&&a.head,r,n);(i||s!=l.anchor||u!=l.head)&&(i||(i=t.ranges.slice(0,o)),i[o]=new so(s,u))}return i?uo(e.cm,i,t.primIndex):t}function qo(e,t,r,n,i){var o=et(e,t.line);if(o.markedSpans)for(var l=0;l<o.markedSpans.length;++l){var a=o.markedSpans[l],s=a.marker,u="selectLeft"in s?!s.selectLeft:s.inclusiveLeft,c="selectRight"in s?!s.selectRight:s.inclusiveRight;if((null==a.from||(u?a.from<=t.ch:a.from<t.ch))&&(null==a.to||(c?a.to>=t.ch:a.to>t.ch))){if(i&&(we(s,"beforeCursorEnter"),s.explicitlyCleared)){if(o.markedSpans){--l;continue}break}if(!s.atomic)continue;if(r){var f=s.find(n<0?1:-1),d=void 0;if((n<0?c:u)&&(f=Jo(e,f,-n,f&&f.line==t.line?o:null)),f&&f.line==t.line&&(d=ut(f,r))&&(n<0?d<0:d>0))return qo(e,f,t,n,i)}var h=s.find(n<0?-1:1);return(n<0?u:c)&&(h=Jo(e,h,n,h.line==t.line?o:null)),h?qo(e,h,t,n,i):null}}return t}function Zo(e,t,r,n,i){var o=n||1,l=qo(e,t,r,o,i)||!i&&qo(e,t,r,o,!0)||qo(e,t,r,-o,i)||!i&&qo(e,t,r,-o,!0);return l||(e.cantEdit=!0,st(e.first,0))}function Jo(e,t,r,n){return r<0&&0==t.ch?t.line>e.first?gt(e,st(t.line-1)):null:r>0&&t.ch==(n||et(e,t.line)).text.length?t.line<e.first+e.size-1?st(t.line+1,0):null:new st(t.line,t.ch+r)}function Qo(e){e.setSelection(st(e.firstLine(),0),st(e.lastLine()),_)}function el(e,t,r){var n={canceled:!1,from:t.from,to:t.to,text:t.text,origin:t.origin,cancel:function(){return n.canceled=!0}};return r&&(n.update=function(t,r,i,o){t&&(n.from=gt(e,t)),r&&(n.to=gt(e,r)),i&&(n.text=i),void 0!==o&&(n.origin=o)}),we(e,"beforeChange",e,n),e.cm&&we(e.cm,"beforeChange",e.cm,n),n.canceled?(e.cm&&(e.cm.curOp.updateInput=2),null):{from:n.from,to:n.to,text:n.text,origin:n.origin}}function tl(e,t,r){if(e.cm){if(!e.cm.curOp)return Ei(e.cm,tl)(e,t,r);if(e.cm.state.suppressEdits)return}if(!(ke(e,"beforeChange")||e.cm&&ke(e.cm,"beforeChange"))||(t=el(e,t,!0))){var n=Wt&&!r&&Ut(e,t.from,t.to);if(n)for(var i=n.length-1;i>=0;--i)rl(e,{from:n[i].from,to:n[i].to,text:i?[""]:t.text,origin:t.origin});else rl(e,t)}}function rl(e,t){if(1!=t.text.length||""!=t.text[0]||0!=ut(t.from,t.to)){var r=po(e,t);Oo(e,t,r,e.cm?e.cm.curOp.id:NaN),ol(e,t,r,Gt(e,t));var n=[];xo(e,(function(e,r){r||-1!=j(n,e.history)||(cl(e.history,t),n.push(e.history)),ol(e,t,null,Gt(e,t))}))}}function nl(e,t,r){var n=e.cm&&e.cm.state.suppressEdits;if(!n||r){for(var i,o=e.history,l=e.sel,a="undo"==t?o.done:o.undone,s="undo"==t?o.undone:o.done,u=0;u<a.length&&(i=a[u],r?!i.ranges||i.equals(e.sel):i.ranges);u++);if(u!=a.length){for(o.lastOrigin=o.lastSelOrigin=null;;){if(!(i=a.pop()).ranges){if(n)return void a.push(i);break}if(Wo(i,s),r&&!i.equals(e.sel))return void Ko(e,i,{clearRedo:!1});l=i}var c=[];Wo(l,s),s.push({changes:c,generation:o.generation}),o.generation=i.generation||++o.maxGeneration;for(var f=ke(e,"beforeChange")||e.cm&&ke(e.cm,"beforeChange"),d=function(r){var n=i.changes[r];if(n.origin=t,f&&!el(e,n,!1))return a.length=0,{};c.push(To(e,n));var o=r?po(e,n):J(a);ol(e,n,o,Po(e,n)),!r&&e.cm&&e.cm.scrollIntoView({from:n.from,to:fo(n)});var l=[];xo(e,(function(e,t){t||-1!=j(l,e.history)||(cl(e.history,n),l.push(e.history)),ol(e,n,null,Po(e,n))}))},h=i.changes.length-1;h>=0;--h){var p=d(h);if(p)return p.v}}}}function il(e,t){if(0!=t&&(e.first+=t,e.sel=new ao(Q(e.sel.ranges,(function(e){return new so(st(e.anchor.line+t,e.anchor.ch),st(e.head.line+t,e.head.ch))})),e.sel.primIndex),e.cm)){Bn(e.cm,e.first,e.first-t,t);for(var r=e.cm.display,n=r.viewFrom;n<r.viewTo;n++)Vn(e.cm,n,"gutter")}}function ol(e,t,r,n){if(e.cm&&!e.cm.curOp)return Ei(e.cm,ol)(e,t,r,n);if(t.to.line<e.first)il(e,t.text.length-1-(t.to.line-t.from.line));else if(!(t.from.line>e.lastLine())){if(t.from.line<e.first){var i=t.text.length-1-(e.first-t.from.line);il(e,i),t={from:st(e.first,0),to:st(t.to.line+i,t.to.ch),text:[J(t.text)],origin:t.origin}}var o=e.lastLine();t.to.line>o&&(t={from:t.from,to:st(o,et(e,o).text.length),text:[t.text[0]],origin:t.origin}),t.removed=tt(e,t.from,t.to),r||(r=po(e,t)),e.cm?ll(e.cm,t,n):wo(e,t,n),_o(e,r,_),e.cantEdit&&Zo(e,st(e.firstLine(),0))&&(e.cantEdit=!1)}}function ll(e,t,r){var n=e.doc,i=e.display,o=t.from,l=t.to,a=!1,s=o.line;e.options.lineWrapping||(s=it(tr(et(n,o.line))),n.iter(s,l.line+1,(function(e){if(e==i.maxLine)return a=!0,!0}))),n.sel.contains(t.from,t.to)>-1&&Ce(e),wo(n,t,r,Pn(e)),e.options.lineWrapping||(n.iter(s,o.line+t.text.length,(function(e){var t=ur(e);t>i.maxLineLength&&(i.maxLine=e,i.maxLineLength=t,i.maxLineChanged=!0,a=!1)})),a&&(e.curOp.updateMaxLine=!0)),Dt(n,o.line),Ii(e,400);var u=t.text.length-(l.line-o.line)-1;t.full?Bn(e):o.line!=l.line||1!=t.text.length||bo(e.doc,t)?Bn(e,o.line,l.line+1,u):Vn(e,o.line,"text");var c=ke(e,"changes"),f=ke(e,"change");if(f||c){var d={from:o,to:l,text:t.text,removed:t.removed,origin:t.origin};f&&Dr(e,"change",e,d),c&&(e.curOp.changeObjs||(e.curOp.changeObjs=[])).push(d)}e.display.selForContextMenu=null}function al(e,t,r,n,i){var o;n||(n=r),ut(n,r)<0&&(r=(o=[n,r])[0],n=o[1]),"string"==typeof t&&(t=e.splitLines(t)),tl(e,{from:r,to:n,text:t,origin:i})}function sl(e,t,r,n){r<e.line?e.line+=n:t<e.line&&(e.line=t,e.ch=0)}function ul(e,t,r,n){for(var i=0;i<e.length;++i){var o=e[i],l=!0;if(o.ranges){o.copied||((o=e[i]=o.deepCopy()).copied=!0);for(var a=0;a<o.ranges.length;a++)sl(o.ranges[a].anchor,t,r,n),sl(o.ranges[a].head,t,r,n)}else{for(var s=0;s<o.changes.length;++s){var u=o.changes[s];if(r<u.from.line)u.from=st(u.from.line+n,u.from.ch),u.to=st(u.to.line+n,u.to.ch);else if(t<=u.to.line){l=!1;break}}l||(e.splice(0,i+1),i=0)}}}function cl(e,t){var r=t.from.line,n=t.to.line,i=t.text.length-(n-r)-1;ul(e.done,r,n,i),ul(e.undone,r,n,i)}function fl(e,t,r,n){var i=t,o=t;return"number"==typeof t?o=et(e,pt(e,t)):i=it(t),null==i?null:(n(o,i)&&e.cm&&Vn(e.cm,i,r),o)}function dl(e){this.lines=e,this.parent=null;for(var t=0,r=0;r<e.length;++r)e[r].parent=this,t+=e[r].height;this.height=t}function hl(e){this.children=e;for(var t=0,r=0,n=0;n<e.length;++n){var i=e[n];t+=i.chunkSize(),r+=i.height,i.parent=this}this.size=t,this.height=r,this.parent=null}so.prototype.from=function(){return ht(this.anchor,this.head)},so.prototype.to=function(){return dt(this.anchor,this.head)},so.prototype.empty=function(){return this.head.line==this.anchor.line&&this.head.ch==this.anchor.ch},dl.prototype={chunkSize:function(){return this.lines.length},removeInner:function(e,t){for(var r=e,n=e+t;r<n;++r){var i=this.lines[r];this.height-=i.height,hr(i),Dr(i,"delete")}this.lines.splice(e,t)},collapse:function(e){e.push.apply(e,this.lines)},insertInner:function(e,t,r){this.height+=r,this.lines=this.lines.slice(0,e).concat(t).concat(this.lines.slice(e));for(var n=0;n<t.length;++n)t[n].parent=this},iterN:function(e,t,r){for(var n=e+t;e<n;++e)if(r(this.lines[e]))return!0}},hl.prototype={chunkSize:function(){return this.size},removeInner:function(e,t){this.size-=t;for(var r=0;r<this.children.length;++r){var n=this.children[r],i=n.chunkSize();if(e<i){var o=Math.min(t,i-e),l=n.height;if(n.removeInner(e,o),this.height-=l-n.height,i==o&&(this.children.splice(r--,1),n.parent=null),0==(t-=o))break;e=0}else e-=i}if(this.size-t<25&&(this.children.length>1||!(this.children[0]instanceof dl))){var a=[];this.collapse(a),this.children=[new dl(a)],this.children[0].parent=this}},collapse:function(e){for(var t=0;t<this.children.length;++t)this.children[t].collapse(e)},insertInner:function(e,t,r){this.size+=t.length,this.height+=r;for(var n=0;n<this.children.length;++n){var i=this.children[n],o=i.chunkSize();if(e<=o){if(i.insertInner(e,t,r),i.lines&&i.lines.length>50){for(var l=i.lines.length%25+25,a=l;a<i.lines.length;){var s=new dl(i.lines.slice(a,a+=25));i.height-=s.height,this.children.splice(++n,0,s),s.parent=this}i.lines=i.lines.slice(0,l),this.maybeSpill()}break}e-=o}},maybeSpill:function(){if(!(this.children.length<=10)){var e=this;do{var t=new hl(e.children.splice(e.children.length-5,5));if(e.parent){e.size-=t.size,e.height-=t.height;var r=j(e.parent.children,e);e.parent.children.splice(r+1,0,t)}else{var n=new hl(e.children);n.parent=e,e.children=[n,t],e=n}t.parent=e.parent}while(e.children.length>10);e.parent.maybeSpill()}},iterN:function(e,t,r){for(var n=0;n<this.children.length;++n){var i=this.children[n],o=i.chunkSize();if(e<o){var l=Math.min(t,o-e);if(i.iterN(e,l,r))return!0;if(0==(t-=l))break;e=0}else e-=o}}};var pl=function(e,t,r){if(r)for(var n in r)r.hasOwnProperty(n)&&(this[n]=r[n]);this.doc=e,this.node=t};function gl(e,t,r){sr(t)<(e.curOp&&e.curOp.scrollTop||e.doc.scrollTop)&&ui(e,r)}function vl(e,t,r,n){var i=new pl(e,r,n),o=e.cm;return o&&i.noHScroll&&(o.display.alignWidgets=!0),fl(e,t,"widget",(function(t){var r=t.widgets||(t.widgets=[]);if(null==i.insertAt?r.push(i):r.splice(Math.min(r.length,Math.max(0,i.insertAt)),0,i),i.line=t,o&&!lr(e,t)){var n=sr(t)<e.scrollTop;nt(t,t.height+Kr(i)),n&&ui(o,i.height),o.curOp.forceUpdate=!0}return!0})),o&&Dr(o,"lineWidgetAdded",o,i,"number"==typeof t?t:it(t)),i}pl.prototype.clear=function(){var e=this.doc.cm,t=this.line.widgets,r=this.line,n=it(r);if(null!=n&&t){for(var i=0;i<t.length;++i)t[i]==this&&t.splice(i--,1);t.length||(r.widgets=null);var o=Kr(this);nt(r,Math.max(0,r.height-o)),e&&(Fi(e,(function(){gl(e,r,-o),Vn(e,n,"widget")})),Dr(e,"lineWidgetCleared",e,this,n))}},pl.prototype.changed=function(){var e=this,t=this.height,r=this.doc.cm,n=this.line;this.height=null;var i=Kr(this)-t;i&&(lr(this.doc,n)||nt(n,n.height+i),r&&Fi(r,(function(){r.curOp.forceUpdate=!0,gl(r,n,i),Dr(r,"lineWidgetChanged",r,e,it(n))})))},Se(pl);var ml=0,yl=function(e,t){this.lines=[],this.type=t,this.doc=e,this.id=++ml};function bl(e,t,r,n,i){if(n&&n.shared)return xl(e,t,r,n,i);if(e.cm&&!e.cm.curOp)return Ei(e.cm,bl)(e,t,r,n,i);var o=new yl(e,i),l=ut(t,r);if(n&&B(n,o,!1),l>0||0==l&&!1!==o.clearWhenEmpty)return o;if(o.replacedWith&&(o.collapsed=!0,o.widgetNode=D("span",[o.replacedWith],"CodeMirror-widget"),n.handleMouseEvents||o.widgetNode.setAttribute("cm-ignore-events","true"),n.insertLeft&&(o.widgetNode.insertLeft=!0)),o.collapsed){if(er(e,t.line,t,r,o)||t.line!=r.line&&er(e,r.line,t,r,o))throw new Error("Inserting collapsed marker partially overlapping an existing one");Et()}o.addToHistory&&Oo(e,{from:t,to:r,origin:"markText"},e.sel,NaN);var a,s=t.line,u=e.cm;if(e.iter(s,r.line+1,(function(n){u&&o.collapsed&&!u.options.lineWrapping&&tr(n)==u.display.maxLine&&(a=!0),o.collapsed&&s!=t.line&&nt(n,0),Rt(n,new Pt(o,s==t.line?t.ch:null,s==r.line?r.ch:null),e.cm&&e.cm.curOp),++s})),o.collapsed&&e.iter(t.line,r.line+1,(function(t){lr(e,t)&&nt(t,0)})),o.clearOnEnter&&me(o,"beforeCursorEnter",(function(){return o.clear()})),o.readOnly&&(Ft(),(e.history.done.length||e.history.undone.length)&&e.clearHistory()),o.collapsed&&(o.id=++ml,o.atomic=!0),u){if(a&&(u.curOp.updateMaxLine=!0),o.collapsed)Bn(u,t.line,r.line+1);else if(o.className||o.startStyle||o.endStyle||o.css||o.attributes||o.title)for(var c=t.line;c<=r.line;c++)Vn(u,c,"text");o.atomic&&$o(u.doc),Dr(u,"markerAdded",u,o)}return o}yl.prototype.clear=function(){if(!this.explicitlyCleared){var e=this.doc.cm,t=e&&!e.curOp;if(t&&Ti(e),ke(this,"clear")){var r=this.find();r&&Dr(this,"clear",r.from,r.to)}for(var n=null,i=null,o=0;o<this.lines.length;++o){var l=this.lines[o],a=zt(l.markedSpans,this);e&&!this.collapsed?Vn(e,it(l),"text"):e&&(null!=a.to&&(i=it(l)),null!=a.from&&(n=it(l))),l.markedSpans=It(l.markedSpans,a),null==a.from&&this.collapsed&&!lr(this.doc,l)&&e&&nt(l,Wn(e.display))}if(e&&this.collapsed&&!e.options.lineWrapping)for(var s=0;s<this.lines.length;++s){var u=tr(this.lines[s]),c=ur(u);c>e.display.maxLineLength&&(e.display.maxLine=u,e.display.maxLineLength=c,e.display.maxLineChanged=!0)}null!=n&&e&&this.collapsed&&Bn(e,n,i+1),this.lines.length=0,this.explicitlyCleared=!0,this.atomic&&this.doc.cantEdit&&(this.doc.cantEdit=!1,e&&$o(e.doc)),e&&Dr(e,"markerCleared",e,this,n,i),t&&Mi(e),this.parent&&this.parent.clear()}},yl.prototype.find=function(e,t){var r,n;null==e&&"bookmark"==this.type&&(e=1);for(var i=0;i<this.lines.length;++i){var o=this.lines[i],l=zt(o.markedSpans,this);if(null!=l.from&&(r=st(t?o:it(o),l.from),-1==e))return r;if(null!=l.to&&(n=st(t?o:it(o),l.to),1==e))return n}return r&&{from:r,to:n}},yl.prototype.changed=function(){var e=this,t=this.find(-1,!0),r=this,n=this.doc.cm;t&&n&&Fi(n,(function(){var i=t.line,o=it(t.line),l=nn(n,o);if(l&&(hn(l),n.curOp.selectionChanged=n.curOp.forceUpdate=!0),n.curOp.updateMaxLine=!0,!lr(r.doc,i)&&null!=r.height){var a=r.height;r.height=null;var s=Kr(r)-a;s&&nt(i,i.height+s)}Dr(n,"markerChanged",n,e)}))},yl.prototype.attachLine=function(e){if(!this.lines.length&&this.doc.cm){var t=this.doc.cm.curOp;t.maybeHiddenMarkers&&-1!=j(t.maybeHiddenMarkers,this)||(t.maybeUnhiddenMarkers||(t.maybeUnhiddenMarkers=[])).push(this)}this.lines.push(e)},yl.prototype.detachLine=function(e){if(this.lines.splice(j(this.lines,e),1),!this.lines.length&&this.doc.cm){var t=this.doc.cm.curOp;(t.maybeHiddenMarkers||(t.maybeHiddenMarkers=[])).push(this)}},Se(yl);var wl=function(e,t){this.markers=e,this.primary=t;for(var r=0;r<e.length;++r)e[r].parent=this};function xl(e,t,r,n,i){(n=B(n)).shared=!1;var o=[bl(e,t,r,n,i)],l=o[0],a=n.widgetNode;return xo(e,(function(e){a&&(n.widgetNode=a.cloneNode(!0)),o.push(bl(e,gt(e,t),gt(e,r),n,i));for(var s=0;s<e.linked.length;++s)if(e.linked[s].isParent)return;l=J(o)})),new wl(o,l)}function Cl(e){return e.findMarks(st(e.first,0),e.clipPos(st(e.lastLine())),(function(e){return e.parent}))}function kl(e,t){for(var r=0;r<t.length;r++){var n=t[r],i=n.find(),o=e.clipPos(i.from),l=e.clipPos(i.to);if(ut(o,l)){var a=bl(e,o,l,n.primary,n.primary.type);n.markers.push(a),a.parent=n}}}function Sl(e){for(var t=function(t){var r=e[t],n=[r.primary.doc];xo(r.primary.doc,(function(e){return n.push(e)}));for(var i=0;i<r.markers.length;i++){var o=r.markers[i];-1==j(n,o.doc)&&(o.parent=null,r.markers.splice(i--,1))}},r=0;r<e.length;r++)t(r)}wl.prototype.clear=function(){if(!this.explicitlyCleared){this.explicitlyCleared=!0;for(var e=0;e<this.markers.length;++e)this.markers[e].clear();Dr(this,"clear")}},wl.prototype.find=function(e,t){return this.primary.find(e,t)},Se(wl);var Ll=0,Tl=function(e,t,r,n,i){if(!(this instanceof Tl))return new Tl(e,t,r,n,i);null==r&&(r=0),hl.call(this,[new dl([new fr("",null)])]),this.first=r,this.scrollTop=this.scrollLeft=0,this.cantEdit=!1,this.cleanGeneration=1,this.modeFrontier=this.highlightFrontier=r;var o=st(r,0);this.sel=co(o),this.history=new Lo(null),this.id=++Ll,this.modeOption=t,this.lineSep=n,this.direction="rtl"==i?"rtl":"ltr",this.extend=!1,"string"==typeof e&&(e=this.splitLines(e)),wo(this,{from:o,to:o,text:e}),Ko(this,co(o),_)};Tl.prototype=re(hl.prototype,{constructor:Tl,iter:function(e,t,r){r?this.iterN(e-this.first,t-e,r):this.iterN(this.first,this.first+this.size,e)},insert:function(e,t){for(var r=0,n=0;n<t.length;++n)r+=t[n].height;this.insertInner(e-this.first,t,r)},remove:function(e,t){this.removeInner(e-this.first,t)},getValue:function(e){var t=rt(this,this.first,this.first+this.size);return!1===e?t:t.join(e||this.lineSeparator())},setValue:zi((function(e){var t=st(this.first,0),r=this.first+this.size-1;tl(this,{from:t,to:st(r,et(this,r).text.length),text:this.splitLines(e),origin:"setValue",full:!0},!0),this.cm&&fi(this.cm,0,0),Ko(this,co(t),_)})),replaceRange:function(e,t,r,n){al(this,e,t=gt(this,t),r=r?gt(this,r):t,n)},getRange:function(e,t,r){var n=tt(this,gt(this,e),gt(this,t));return!1===r?n:""===r?n.join(""):n.join(r||this.lineSeparator())},getLine:function(e){var t=this.getLineHandle(e);return t&&t.text},getLineHandle:function(e){if(lt(this,e))return et(this,e)},getLineNumber:function(e){return it(e)},getLineHandleVisualStart:function(e){return"number"==typeof e&&(e=et(this,e)),tr(e)},lineCount:function(){return this.size},firstLine:function(){return this.first},lastLine:function(){return this.first+this.size-1},clipPos:function(e){return gt(this,e)},getCursor:function(e){var t=this.sel.primary();return null==e||"head"==e?t.head:"anchor"==e?t.anchor:"end"==e||"to"==e||!1===e?t.to():t.from()},listSelections:function(){return this.sel.ranges},somethingSelected:function(){return this.sel.somethingSelected()},setCursor:zi((function(e,t,r){Go(this,gt(this,"number"==typeof e?st(e,t||0):e),null,r)})),setSelection:zi((function(e,t,r){Go(this,gt(this,e),gt(this,t||e),r)})),extendSelection:zi((function(e,t,r){Ro(this,gt(this,e),t&&gt(this,t),r)})),extendSelections:zi((function(e,t){Bo(this,mt(this,e),t)})),extendSelectionsBy:zi((function(e,t){Bo(this,mt(this,Q(this.sel.ranges,e)),t)})),setSelections:zi((function(e,t,r){if(e.length){for(var n=[],i=0;i<e.length;i++)n[i]=new so(gt(this,e[i].anchor),gt(this,e[i].head||e[i].anchor));null==t&&(t=Math.min(e.length-1,this.sel.primIndex)),Ko(this,uo(this.cm,n,t),r)}})),addSelection:zi((function(e,t,r){var n=this.sel.ranges.slice(0);n.push(new so(gt(this,e),gt(this,t||e))),Ko(this,uo(this.cm,n,n.length-1),r)})),getSelection:function(e){for(var t,r=this.sel.ranges,n=0;n<r.length;n++){var i=tt(this,r[n].from(),r[n].to());t=t?t.concat(i):i}return!1===e?t:t.join(e||this.lineSeparator())},getSelections:function(e){for(var t=[],r=this.sel.ranges,n=0;n<r.length;n++){var i=tt(this,r[n].from(),r[n].to());!1!==e&&(i=i.join(e||this.lineSeparator())),t[n]=i}return t},replaceSelection:function(e,t,r){for(var n=[],i=0;i<this.sel.ranges.length;i++)n[i]=e;this.replaceSelections(n,t,r||"+input")},replaceSelections:zi((function(e,t,r){for(var n=[],i=this.sel,o=0;o<i.ranges.length;o++){var l=i.ranges[o];n[o]={from:l.from(),to:l.to(),text:this.splitLines(e[o]),origin:r}}for(var a=t&&"end"!=t&&vo(this,n,t),s=n.length-1;s>=0;s--)tl(this,n[s]);a?Uo(this,a):this.cm&&ci(this.cm)})),undo:zi((function(){nl(this,"undo")})),redo:zi((function(){nl(this,"redo")})),undoSelection:zi((function(){nl(this,"undo",!0)})),redoSelection:zi((function(){nl(this,"redo",!0)})),setExtending:function(e){this.extend=e},getExtending:function(){return this.extend},historySize:function(){for(var e=this.history,t=0,r=0,n=0;n<e.done.length;n++)e.done[n].ranges||++t;for(var i=0;i<e.undone.length;i++)e.undone[i].ranges||++r;return{undo:t,redo:r}},clearHistory:function(){var e=this;this.history=new Lo(this.history),xo(this,(function(t){return t.history=e.history}),!0)},markClean:function(){this.cleanGeneration=this.changeGeneration(!0)},changeGeneration:function(e){return e&&(this.history.lastOp=this.history.lastSelOp=this.history.lastOrigin=null),this.history.generation},isClean:function(e){return this.history.generation==(e||this.cleanGeneration)},getHistory:function(){return{done:zo(this.history.done),undone:zo(this.history.undone)}},setHistory:function(e){var t=this.history=new Lo(this.history);t.done=zo(e.done.slice(0),null,!0),t.undone=zo(e.undone.slice(0),null,!0)},setGutterMarker:zi((function(e,t,r){return fl(this,e,"gutter",(function(e){var n=e.gutterMarkers||(e.gutterMarkers={});return n[t]=r,!r&&le(n)&&(e.gutterMarkers=null),!0}))})),clearGutter:zi((function(e){var t=this;this.iter((function(r){r.gutterMarkers&&r.gutterMarkers[e]&&fl(t,r,"gutter",(function(){return r.gutterMarkers[e]=null,le(r.gutterMarkers)&&(r.gutterMarkers=null),!0}))}))})),lineInfo:function(e){var t;if("number"==typeof e){if(!lt(this,e))return null;if(t=e,!(e=et(this,e)))return null}else if(null==(t=it(e)))return null;return{line:t,handle:e,text:e.text,gutterMarkers:e.gutterMarkers,textClass:e.textClass,bgClass:e.bgClass,wrapClass:e.wrapClass,widgets:e.widgets}},addLineClass:zi((function(e,t,r){return fl(this,e,"gutter"==t?"gutter":"class",(function(e){var n="text"==t?"textClass":"background"==t?"bgClass":"gutter"==t?"gutterClass":"wrapClass";if(e[n]){if(L(r).test(e[n]))return!1;e[n]+=" "+r}else e[n]=r;return!0}))})),removeLineClass:zi((function(e,t,r){return fl(this,e,"gutter"==t?"gutter":"class",(function(e){var n="text"==t?"textClass":"background"==t?"bgClass":"gutter"==t?"gutterClass":"wrapClass",i=e[n];if(!i)return!1;if(null==r)e[n]=null;else{var o=i.match(L(r));if(!o)return!1;var l=o.index+o[0].length;e[n]=i.slice(0,o.index)+(o.index&&l!=i.length?" ":"")+i.slice(l)||null}return!0}))})),addLineWidget:zi((function(e,t,r){return vl(this,e,t,r)})),removeLineWidget:function(e){e.clear()},markText:function(e,t,r){return bl(this,gt(this,e),gt(this,t),r,r&&r.type||"range")},setBookmark:function(e,t){var r={replacedWith:t&&(null==t.nodeType?t.widget:t),insertLeft:t&&t.insertLeft,clearWhenEmpty:!1,shared:t&&t.shared,handleMouseEvents:t&&t.handleMouseEvents};return bl(this,e=gt(this,e),e,r,"bookmark")},findMarksAt:function(e){var t=[],r=et(this,(e=gt(this,e)).line).markedSpans;if(r)for(var n=0;n<r.length;++n){var i=r[n];(null==i.from||i.from<=e.ch)&&(null==i.to||i.to>=e.ch)&&t.push(i.marker.parent||i.marker)}return t},findMarks:function(e,t,r){e=gt(this,e),t=gt(this,t);var n=[],i=e.line;return this.iter(e.line,t.line+1,(function(o){var l=o.markedSpans;if(l)for(var a=0;a<l.length;a++){var s=l[a];null!=s.to&&i==e.line&&e.ch>=s.to||null==s.from&&i!=e.line||null!=s.from&&i==t.line&&s.from>=t.ch||r&&!r(s.marker)||n.push(s.marker.parent||s.marker)}++i})),n},getAllMarks:function(){var e=[];return this.iter((function(t){var r=t.markedSpans;if(r)for(var n=0;n<r.length;++n)null!=r[n].from&&e.push(r[n].marker)})),e},posFromIndex:function(e){var t,r=this.first,n=this.lineSeparator().length;return this.iter((function(i){var o=i.text.length+n;if(o>e)return t=e,!0;e-=o,++r})),gt(this,st(r,t))},indexFromPos:function(e){var t=(e=gt(this,e)).ch;if(e.line<this.first||e.ch<0)return 0;var r=this.lineSeparator().length;return this.iter(this.first,e.line,(function(e){t+=e.text.length+r})),t},copy:function(e){var t=new Tl(rt(this,this.first,this.first+this.size),this.modeOption,this.first,this.lineSep,this.direction);return t.scrollTop=this.scrollTop,t.scrollLeft=this.scrollLeft,t.sel=this.sel,t.extend=!1,e&&(t.history.undoDepth=this.history.undoDepth,t.setHistory(this.getHistory())),t},linkedDoc:function(e){e||(e={});var t=this.first,r=this.first+this.size;null!=e.from&&e.from>t&&(t=e.from),null!=e.to&&e.to<r&&(r=e.to);var n=new Tl(rt(this,t,r),e.mode||this.modeOption,t,this.lineSep,this.direction);return e.sharedHist&&(n.history=this.history),(this.linked||(this.linked=[])).push({doc:n,sharedHist:e.sharedHist}),n.linked=[{doc:this,isParent:!0,sharedHist:e.sharedHist}],kl(n,Cl(this)),n},unlinkDoc:function(e){if(e instanceof Ba&&(e=e.doc),this.linked)for(var t=0;t<this.linked.length;++t)if(this.linked[t].doc==e){this.linked.splice(t,1),e.unlinkDoc(this),Sl(Cl(this));break}if(e.history==this.history){var r=[e.id];xo(e,(function(e){return r.push(e.id)}),!0),e.history=new Lo(null),e.history.done=zo(this.history.done,r),e.history.undone=zo(this.history.undone,r)}},iterLinkedDocs:function(e){xo(this,e)},getMode:function(){return this.mode},getEditor:function(){return this.cm},splitLines:function(e){return this.lineSep?e.split(this.lineSep):ze(e)},lineSeparator:function(){return this.lineSep||"\n"},setDirection:zi((function(e){"rtl"!=e&&(e="ltr"),e!=this.direction&&(this.direction=e,this.iter((function(e){return e.order=null})),this.cm&&So(this.cm))}))}),Tl.prototype.eachLine=Tl.prototype.iter;var Ml=0;function Nl(e){var t=this;if(Dl(t),!xe(t,e)&&!_r(t.display,e)){Le(e),l&&(Ml=+new Date);var r=In(t,e,!0),n=e.dataTransfer.files;if(r&&!t.isReadOnly())if(n&&n.length&&window.FileReader&&window.File)for(var i=n.length,o=Array(i),a=0,s=function(){++a==i&&Ei(t,(function(){var e={from:r=gt(t.doc,r),to:r,text:t.doc.splitLines(o.filter((function(e){return null!=e})).join(t.doc.lineSeparator())),origin:"paste"};tl(t.doc,e),Uo(t.doc,co(gt(t.doc,r),gt(t.doc,fo(e))))}))()},u=function(e,r){if(t.options.allowDropFileTypes&&-1==j(t.options.allowDropFileTypes,e.type))s();else{var n=new FileReader;n.onerror=function(){return s()},n.onload=function(){var e=n.result;/[\x00-\x08\x0e-\x1f]{2}/.test(e)||(o[r]=e),s()},n.readAsText(e)}},c=0;c<n.length;c++)u(n[c],c);else{if(t.state.draggingText&&t.doc.sel.contains(r)>-1)return t.state.draggingText(e),void setTimeout((function(){return t.display.input.focus()}),20);try{var f=e.dataTransfer.getData("Text");if(f){var d;if(t.state.draggingText&&!t.state.draggingText.copy&&(d=t.listSelections()),_o(t.doc,co(r,r)),d)for(var h=0;h<d.length;++h)al(t.doc,"",d[h].anchor,d[h].head,"drag");t.replaceSelection(f,"around","paste"),t.display.input.focus()}}catch(e){}}}}function Ol(e,t){if(l&&(!e.state.draggingText||+new Date-Ml<100))Ne(t);else if(!xe(e,t)&&!_r(e.display,t)&&(t.dataTransfer.setData("Text",e.getSelection()),t.dataTransfer.effectAllowed="copyMove",t.dataTransfer.setDragImage&&!h)){var r=A("img",null,null,"position: fixed; left: 0; top: 0;");r.src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==",d&&(r.width=r.height=1,e.display.wrapper.appendChild(r),r._top=r.offsetTop),t.dataTransfer.setDragImage(r,0,0),d&&r.parentNode.removeChild(r)}}function Al(e,t){var r=In(e,t);if(r){var n=document.createDocumentFragment();$n(e,r,n),e.display.dragCursor||(e.display.dragCursor=A("div",null,"CodeMirror-cursors CodeMirror-dragcursors"),e.display.lineSpace.insertBefore(e.display.dragCursor,e.display.cursorDiv)),O(e.display.dragCursor,n)}}function Dl(e){e.display.dragCursor&&(e.display.lineSpace.removeChild(e.display.dragCursor),e.display.dragCursor=null)}function Wl(e){if(document.getElementsByClassName){for(var t=document.getElementsByClassName("CodeMirror"),r=[],n=0;n<t.length;n++){var i=t[n].CodeMirror;i&&r.push(i)}r.length&&r[0].operation((function(){for(var t=0;t<r.length;t++)e(r[t])}))}}var Hl=!1;function Fl(){Hl||(El(),Hl=!0)}function El(){var e;me(window,"resize",(function(){null==e&&(e=setTimeout((function(){e=null,Wl(Pl)}),100))})),me(window,"blur",(function(){return Wl(ti)}))}function Pl(e){var t=e.display;t.cachedCharWidth=t.cachedTextHeight=t.cachedPaddingH=null,t.scrollbarsClipped=!1,e.setSize()}for(var zl={3:"Pause",8:"Backspace",9:"Tab",13:"Enter",16:"Shift",17:"Ctrl",18:"Alt",19:"Pause",20:"CapsLock",27:"Esc",32:"Space",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"Left",38:"Up",39:"Right",40:"Down",44:"PrintScrn",45:"Insert",46:"Delete",59:";",61:"=",91:"Mod",92:"Mod",93:"Mod",106:"*",107:"=",109:"-",110:".",111:"/",145:"ScrollLock",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'",224:"Mod",63232:"Up",63233:"Down",63234:"Left",63235:"Right",63272:"Delete",63273:"Home",63275:"End",63276:"PageUp",63277:"PageDown",63302:"Insert"},Il=0;Il<10;Il++)zl[Il+48]=zl[Il+96]=String(Il);for(var Rl=65;Rl<=90;Rl++)zl[Rl]=String.fromCharCode(Rl);for(var Bl=1;Bl<=12;Bl++)zl[Bl+111]=zl[Bl+63235]="F"+Bl;var Vl={};function Gl(e){var t,r,n,i,o=e.split(/-(?!$)/);e=o[o.length-1];for(var l=0;l<o.length-1;l++){var a=o[l];if(/^(cmd|meta|m)$/i.test(a))i=!0;else if(/^a(lt)?$/i.test(a))t=!0;else if(/^(c|ctrl|control)$/i.test(a))r=!0;else{if(!/^s(hift)?$/i.test(a))throw new Error("Unrecognized modifier name: "+a);n=!0}}return t&&(e="Alt-"+e),r&&(e="Ctrl-"+e),i&&(e="Cmd-"+e),n&&(e="Shift-"+e),e}function jl(e){var t={};for(var r in e)if(e.hasOwnProperty(r)){var n=e[r];if(/^(name|fallthrough|(de|at)tach)$/.test(r))continue;if("..."==n){delete e[r];continue}for(var i=Q(r.split(" "),Gl),o=0;o<i.length;o++){var l=void 0,a=void 0;o==i.length-1?(a=i.join(" "),l=n):(a=i.slice(0,o+1).join(" "),l="...");var s=t[a];if(s){if(s!=l)throw new Error("Inconsistent bindings for "+a)}else t[a]=l}delete e[r]}for(var u in t)e[u]=t[u];return e}function Ul(e,t,r,n){var i=(t=$l(t)).call?t.call(e,n):t[e];if(!1===i)return"nothing";if("..."===i)return"multi";if(null!=i&&r(i))return"handled";if(t.fallthrough){if("[object Array]"!=Object.prototype.toString.call(t.fallthrough))return Ul(e,t.fallthrough,r,n);for(var o=0;o<t.fallthrough.length;o++){var l=Ul(e,t.fallthrough[o],r,n);if(l)return l}}}function Kl(e){var t="string"==typeof e?e:zl[e.keyCode];return"Ctrl"==t||"Alt"==t||"Shift"==t||"Mod"==t}function _l(e,t,r){var n=e;return t.altKey&&"Alt"!=n&&(e="Alt-"+e),(k?t.metaKey:t.ctrlKey)&&"Ctrl"!=n&&(e="Ctrl-"+e),(k?t.ctrlKey:t.metaKey)&&"Mod"!=n&&(e="Cmd-"+e),!r&&t.shiftKey&&"Shift"!=n&&(e="Shift-"+e),e}function Xl(e,t){if(d&&34==e.keyCode&&e.char)return!1;var r=zl[e.keyCode];return null!=r&&!e.altGraphKey&&(3==e.keyCode&&e.code&&(r=e.code),_l(r,e,t))}function $l(e){return"string"==typeof e?Vl[e]:e}function Yl(e,t){for(var r=e.doc.sel.ranges,n=[],i=0;i<r.length;i++){for(var o=t(r[i]);n.length&&ut(o.from,J(n).to)<=0;){var l=n.pop();if(ut(l.from,o.from)<0){o.from=l.from;break}}n.push(o)}Fi(e,(function(){for(var t=n.length-1;t>=0;t--)al(e.doc,"",n[t].from,n[t].to,"+delete");ci(e)}))}function ql(e,t,r){var n=ue(e.text,t+r,r);return n<0||n>e.text.length?null:n}function Zl(e,t,r){var n=ql(e,t.ch,r);return null==n?null:new st(t.line,n,r<0?"after":"before")}function Jl(e,t,r,n,i){if(e){"rtl"==t.doc.direction&&(i=-i);var o=ge(r,t.doc.direction);if(o){var l,a=i<0?J(o):o[0],s=i<0==(1==a.level)?"after":"before";if(a.level>0||"rtl"==t.doc.direction){var u=on(t,r);l=i<0?r.text.length-1:0;var c=ln(t,u,l).top;l=ce((function(e){return ln(t,u,e).top==c}),i<0==(1==a.level)?a.from:a.to-1,l),"before"==s&&(l=ql(r,l,1))}else l=i<0?a.to:a.from;return new st(n,l,s)}}return new st(n,i<0?r.text.length:0,i<0?"before":"after")}function Ql(e,t,r,n){var i=ge(t,e.doc.direction);if(!i)return Zl(t,r,n);r.ch>=t.text.length?(r.ch=t.text.length,r.sticky="before"):r.ch<=0&&(r.ch=0,r.sticky="after");var o=he(i,r.ch,r.sticky),l=i[o];if("ltr"==e.doc.direction&&l.level%2==0&&(n>0?l.to>r.ch:l.from<r.ch))return Zl(t,r,n);var a,s=function(e,r){return ql(t,e instanceof st?e.ch:e,r)},u=function(r){return e.options.lineWrapping?(a=a||on(e,t),Mn(e,t,a,r)):{begin:0,end:t.text.length}},c=u("before"==r.sticky?s(r,-1):r.ch);if("rtl"==e.doc.direction||1==l.level){var f=1==l.level==n<0,d=s(r,f?1:-1);if(null!=d&&(f?d<=l.to&&d<=c.end:d>=l.from&&d>=c.begin)){var h=f?"before":"after";return new st(r.line,d,h)}}var p=function(e,t,n){for(var o=function(e,t){return t?new st(r.line,s(e,1),"before"):new st(r.line,e,"after")};e>=0&&e<i.length;e+=t){var l=i[e],a=t>0==(1!=l.level),u=a?n.begin:s(n.end,-1);if(l.from<=u&&u<l.to)return o(u,a);if(u=a?l.from:s(l.to,-1),n.begin<=u&&u<n.end)return o(u,a)}},g=p(o+n,n,c);if(g)return g;var v=n>0?c.end:s(c.begin,-1);return null==v||n>0&&v==t.text.length||!(g=p(n>0?0:i.length-1,n,u(v)))?null:g}Vl.basic={Left:"goCharLeft",Right:"goCharRight",Up:"goLineUp",Down:"goLineDown",End:"goLineEnd",Home:"goLineStartSmart",PageUp:"goPageUp",PageDown:"goPageDown",Delete:"delCharAfter",Backspace:"delCharBefore","Shift-Backspace":"delCharBefore",Tab:"defaultTab","Shift-Tab":"indentAuto",Enter:"newlineAndIndent",Insert:"toggleOverwrite",Esc:"singleSelection"},Vl.pcDefault={"Ctrl-A":"selectAll","Ctrl-D":"deleteLine","Ctrl-Z":"undo","Shift-Ctrl-Z":"redo","Ctrl-Y":"redo","Ctrl-Home":"goDocStart","Ctrl-End":"goDocEnd","Ctrl-Up":"goLineUp","Ctrl-Down":"goLineDown","Ctrl-Left":"goGroupLeft","Ctrl-Right":"goGroupRight","Alt-Left":"goLineStart","Alt-Right":"goLineEnd","Ctrl-Backspace":"delGroupBefore","Ctrl-Delete":"delGroupAfter","Ctrl-S":"save","Ctrl-F":"find","Ctrl-G":"findNext","Shift-Ctrl-G":"findPrev","Shift-Ctrl-F":"replace","Shift-Ctrl-R":"replaceAll","Ctrl-[":"indentLess","Ctrl-]":"indentMore","Ctrl-U":"undoSelection","Shift-Ctrl-U":"redoSelection","Alt-U":"redoSelection",fallthrough:"basic"},Vl.emacsy={"Ctrl-F":"goCharRight","Ctrl-B":"goCharLeft","Ctrl-P":"goLineUp","Ctrl-N":"goLineDown","Ctrl-A":"goLineStart","Ctrl-E":"goLineEnd","Ctrl-V":"goPageDown","Shift-Ctrl-V":"goPageUp","Ctrl-D":"delCharAfter","Ctrl-H":"delCharBefore","Alt-Backspace":"delWordBefore","Ctrl-K":"killLine","Ctrl-T":"transposeChars","Ctrl-O":"openLine"},Vl.macDefault={"Cmd-A":"selectAll","Cmd-D":"deleteLine","Cmd-Z":"undo","Shift-Cmd-Z":"redo","Cmd-Y":"redo","Cmd-Home":"goDocStart","Cmd-Up":"goDocStart","Cmd-End":"goDocEnd","Cmd-Down":"goDocEnd","Alt-Left":"goGroupLeft","Alt-Right":"goGroupRight","Cmd-Left":"goLineLeft","Cmd-Right":"goLineRight","Alt-Backspace":"delGroupBefore","Ctrl-Alt-Backspace":"delGroupAfter","Alt-Delete":"delGroupAfter","Cmd-S":"save","Cmd-F":"find","Cmd-G":"findNext","Shift-Cmd-G":"findPrev","Cmd-Alt-F":"replace","Shift-Cmd-Alt-F":"replaceAll","Cmd-[":"indentLess","Cmd-]":"indentMore","Cmd-Backspace":"delWrappedLineLeft","Cmd-Delete":"delWrappedLineRight","Cmd-U":"undoSelection","Shift-Cmd-U":"redoSelection","Ctrl-Up":"goDocStart","Ctrl-Down":"goDocEnd",fallthrough:["basic","emacsy"]},Vl.default=b?Vl.macDefault:Vl.pcDefault;var ea={selectAll:Qo,singleSelection:function(e){return e.setSelection(e.getCursor("anchor"),e.getCursor("head"),_)},killLine:function(e){return Yl(e,(function(t){if(t.empty()){var r=et(e.doc,t.head.line).text.length;return t.head.ch==r&&t.head.line<e.lastLine()?{from:t.head,to:st(t.head.line+1,0)}:{from:t.head,to:st(t.head.line,r)}}return{from:t.from(),to:t.to()}}))},deleteLine:function(e){return Yl(e,(function(t){return{from:st(t.from().line,0),to:gt(e.doc,st(t.to().line+1,0))}}))},delLineLeft:function(e){return Yl(e,(function(e){return{from:st(e.from().line,0),to:e.from()}}))},delWrappedLineLeft:function(e){return Yl(e,(function(t){var r=e.charCoords(t.head,"div").top+5;return{from:e.coordsChar({left:0,top:r},"div"),to:t.from()}}))},delWrappedLineRight:function(e){return Yl(e,(function(t){var r=e.charCoords(t.head,"div").top+5,n=e.coordsChar({left:e.display.lineDiv.offsetWidth+100,top:r},"div");return{from:t.from(),to:n}}))},undo:function(e){return e.undo()},redo:function(e){return e.redo()},undoSelection:function(e){return e.undoSelection()},redoSelection:function(e){return e.redoSelection()},goDocStart:function(e){return e.extendSelection(st(e.firstLine(),0))},goDocEnd:function(e){return e.extendSelection(st(e.lastLine()))},goLineStart:function(e){return e.extendSelectionsBy((function(t){return ta(e,t.head.line)}),{origin:"+move",bias:1})},goLineStartSmart:function(e){return e.extendSelectionsBy((function(t){return na(e,t.head)}),{origin:"+move",bias:1})},goLineEnd:function(e){return e.extendSelectionsBy((function(t){return ra(e,t.head.line)}),{origin:"+move",bias:-1})},goLineRight:function(e){return e.extendSelectionsBy((function(t){var r=e.cursorCoords(t.head,"div").top+5;return e.coordsChar({left:e.display.lineDiv.offsetWidth+100,top:r},"div")}),$)},goLineLeft:function(e){return e.extendSelectionsBy((function(t){var r=e.cursorCoords(t.head,"div").top+5;return e.coordsChar({left:0,top:r},"div")}),$)},goLineLeftSmart:function(e){return e.extendSelectionsBy((function(t){var r=e.cursorCoords(t.head,"div").top+5,n=e.coordsChar({left:0,top:r},"div");return n.ch<e.getLine(n.line).search(/\S/)?na(e,t.head):n}),$)},goLineUp:function(e){return e.moveV(-1,"line")},goLineDown:function(e){return e.moveV(1,"line")},goPageUp:function(e){return e.moveV(-1,"page")},goPageDown:function(e){return e.moveV(1,"page")},goCharLeft:function(e){return e.moveH(-1,"char")},goCharRight:function(e){return e.moveH(1,"char")},goColumnLeft:function(e){return e.moveH(-1,"column")},goColumnRight:function(e){return e.moveH(1,"column")},goWordLeft:function(e){return e.moveH(-1,"word")},goGroupRight:function(e){return e.moveH(1,"group")},goGroupLeft:function(e){return e.moveH(-1,"group")},goWordRight:function(e){return e.moveH(1,"word")},delCharBefore:function(e){return e.deleteH(-1,"codepoint")},delCharAfter:function(e){return e.deleteH(1,"char")},delWordBefore:function(e){return e.deleteH(-1,"word")},delWordAfter:function(e){return e.deleteH(1,"word")},delGroupBefore:function(e){return e.deleteH(-1,"group")},delGroupAfter:function(e){return e.deleteH(1,"group")},indentAuto:function(e){return e.indentSelection("smart")},indentMore:function(e){return e.indentSelection("add")},indentLess:function(e){return e.indentSelection("subtract")},insertTab:function(e){return e.replaceSelection("\t")},insertSoftTab:function(e){for(var t=[],r=e.listSelections(),n=e.options.tabSize,i=0;i<r.length;i++){var o=r[i].from(),l=V(e.getLine(o.line),o.ch,n);t.push(Z(n-l%n))}e.replaceSelections(t)},defaultTab:function(e){e.somethingSelected()?e.indentSelection("add"):e.execCommand("insertTab")},transposeChars:function(e){return Fi(e,(function(){for(var t=e.listSelections(),r=[],n=0;n<t.length;n++)if(t[n].empty()){var i=t[n].head,o=et(e.doc,i.line).text;if(o)if(i.ch==o.length&&(i=new st(i.line,i.ch-1)),i.ch>0)i=new st(i.line,i.ch+1),e.replaceRange(o.charAt(i.ch-1)+o.charAt(i.ch-2),st(i.line,i.ch-2),i,"+transpose");else if(i.line>e.doc.first){var l=et(e.doc,i.line-1).text;l&&(i=new st(i.line,1),e.replaceRange(o.charAt(0)+e.doc.lineSeparator()+l.charAt(l.length-1),st(i.line-1,l.length-1),i,"+transpose"))}r.push(new so(i,i))}e.setSelections(r)}))},newlineAndIndent:function(e){return Fi(e,(function(){for(var t=e.listSelections(),r=t.length-1;r>=0;r--)e.replaceRange(e.doc.lineSeparator(),t[r].anchor,t[r].head,"+input");t=e.listSelections();for(var n=0;n<t.length;n++)e.indentLine(t[n].from().line,null,!0);ci(e)}))},openLine:function(e){return e.replaceSelection("\n","start")},toggleOverwrite:function(e){return e.toggleOverwrite()}};function ta(e,t){var r=et(e.doc,t),n=tr(r);return n!=r&&(t=it(n)),Jl(!0,e,n,t,1)}function ra(e,t){var r=et(e.doc,t),n=rr(r);return n!=r&&(t=it(n)),Jl(!0,e,r,t,-1)}function na(e,t){var r=ta(e,t.line),n=et(e.doc,r.line),i=ge(n,e.doc.direction);if(!i||0==i[0].level){var o=Math.max(r.ch,n.text.search(/\S/)),l=t.line==r.line&&t.ch<=o&&t.ch;return st(r.line,l?0:o,r.sticky)}return r}function ia(e,t,r){if("string"==typeof t&&!(t=ea[t]))return!1;e.display.input.ensurePolled();var n=e.display.shift,i=!1;try{e.isReadOnly()&&(e.state.suppressEdits=!0),r&&(e.display.shift=!1),i=t(e)!=K}finally{e.display.shift=n,e.state.suppressEdits=!1}return i}function oa(e,t,r){for(var n=0;n<e.state.keyMaps.length;n++){var i=Ul(t,e.state.keyMaps[n],r,e);if(i)return i}return e.options.extraKeys&&Ul(t,e.options.extraKeys,r,e)||Ul(t,e.options.keyMap,r,e)}var la=new G;function aa(e,t,r,n){var i=e.state.keySeq;if(i){if(Kl(t))return"handled";if(/\'$/.test(t)?e.state.keySeq=null:la.set(50,(function(){e.state.keySeq==i&&(e.state.keySeq=null,e.display.input.reset())})),sa(e,i+" "+t,r,n))return!0}return sa(e,t,r,n)}function sa(e,t,r,n){var i=oa(e,t,n);return"multi"==i&&(e.state.keySeq=t),"handled"==i&&Dr(e,"keyHandled",e,t,r),"handled"!=i&&"multi"!=i||(Le(r),Zn(e)),!!i}function ua(e,t){var r=Xl(t,!0);return!!r&&(t.shiftKey&&!e.state.keySeq?aa(e,"Shift-"+r,t,(function(t){return ia(e,t,!0)}))||aa(e,r,t,(function(t){if("string"==typeof t?/^go[A-Z]/.test(t):t.motion)return ia(e,t)})):aa(e,r,t,(function(t){return ia(e,t)})))}function ca(e,t,r){return aa(e,"'"+r+"'",t,(function(t){return ia(e,t,!0)}))}var fa=null;function da(e){var t=this;if(!(e.target&&e.target!=t.display.input.getField()||(t.curOp.focus=H(z(t)),xe(t,e)))){l&&a<11&&27==e.keyCode&&(e.returnValue=!1);var n=e.keyCode;t.display.shift=16==n||e.shiftKey;var i=ua(t,e);d&&(fa=i?n:null,i||88!=n||Re||!(b?e.metaKey:e.ctrlKey)||t.replaceSelection("",null,"cut")),r&&!b&&!i&&46==n&&e.shiftKey&&!e.ctrlKey&&document.execCommand&&document.execCommand("cut"),18!=n||/\bCodeMirror-crosshair\b/.test(t.display.lineDiv.className)||ha(t)}}function ha(e){var t=e.display.lineDiv;function r(e){18!=e.keyCode&&e.altKey||(M(t,"CodeMirror-crosshair"),be(document,"keyup",r),be(document,"mouseover",r))}F(t,"CodeMirror-crosshair"),me(document,"keyup",r),me(document,"mouseover",r)}function pa(e){16==e.keyCode&&(this.doc.sel.shift=!1),xe(this,e)}function ga(e){var t=this;if(!(e.target&&e.target!=t.display.input.getField()||_r(t.display,e)||xe(t,e)||e.ctrlKey&&!e.altKey||b&&e.metaKey)){var r=e.keyCode,n=e.charCode;if(d&&r==fa)return fa=null,void Le(e);if(!d||e.which&&!(e.which<10)||!ua(t,e)){var i=String.fromCharCode(null==n?r:n);"\b"!=i&&(ca(t,e,i)||t.display.input.onKeyPress(e))}}}var va,ma,ya=400,ba=function(e,t,r){this.time=e,this.pos=t,this.button=r};function wa(e,t){var r=+new Date;return ma&&ma.compare(r,e,t)?(va=ma=null,"triple"):va&&va.compare(r,e,t)?(ma=new ba(r,e,t),va=null,"double"):(va=new ba(r,e,t),ma=null,"single")}function xa(e){var t=this,r=t.display;if(!(xe(t,e)||r.activeTouch&&r.input.supportsTouch()))if(r.input.ensurePolled(),r.shift=e.shiftKey,_r(r,e))s||(r.scroller.draggable=!1,setTimeout((function(){return r.scroller.draggable=!0}),100));else if(!Aa(t,e)){var n=In(t,e),i=Ae(e),o=n?wa(n,i):"single";I(t).focus(),1==i&&t.state.selectingText&&t.state.selectingText(e),n&&Ca(t,i,n,o,e)||(1==i?n?Sa(t,n,o,e):Oe(e)==r.scroller&&Le(e):2==i?(n&&Ro(t.doc,n),setTimeout((function(){return r.input.focus()}),20)):3==i&&(S?t.display.input.onContextMenu(e):Qn(t)))}}function Ca(e,t,r,n,i){var o="Click";return"double"==n?o="Double"+o:"triple"==n&&(o="Triple"+o),aa(e,_l(o=(1==t?"Left":2==t?"Middle":"Right")+o,i),i,(function(t){if("string"==typeof t&&(t=ea[t]),!t)return!1;var n=!1;try{e.isReadOnly()&&(e.state.suppressEdits=!0),n=t(e,r)!=K}finally{e.state.suppressEdits=!1}return n}))}function ka(e,t,r){var n=e.getOption("configureMouse"),i=n?n(e,t,r):{};if(null==i.unit){var o=w?r.shiftKey&&r.metaKey:r.altKey;i.unit=o?"rectangle":"single"==t?"char":"double"==t?"word":"line"}return(null==i.extend||e.doc.extend)&&(i.extend=e.doc.extend||r.shiftKey),null==i.addNew&&(i.addNew=b?r.metaKey:r.ctrlKey),null==i.moveOnDrag&&(i.moveOnDrag=!(b?r.altKey:r.ctrlKey)),i}function Sa(e,t,r,n){l?setTimeout(R(Jn,e),0):e.curOp.focus=H(z(e));var i,o=ka(e,r,n),a=e.doc.sel;e.options.dragDrop&&He&&!e.isReadOnly()&&"single"==r&&(i=a.contains(t))>-1&&(ut((i=a.ranges[i]).from(),t)<0||t.xRel>0)&&(ut(i.to(),t)>0||t.xRel<0)?La(e,n,t,o):Ma(e,n,t,o)}function La(e,t,r,n){var i=e.display,o=!1,u=Ei(e,(function(t){s&&(i.scroller.draggable=!1),e.state.draggingText=!1,e.state.delayingBlurEvent&&(e.hasFocus()?e.state.delayingBlurEvent=!1:Qn(e)),be(i.wrapper.ownerDocument,"mouseup",u),be(i.wrapper.ownerDocument,"mousemove",c),be(i.scroller,"dragstart",f),be(i.scroller,"drop",u),o||(Le(t),n.addNew||Ro(e.doc,r,null,null,n.extend),s&&!h||l&&9==a?setTimeout((function(){i.wrapper.ownerDocument.body.focus({preventScroll:!0}),i.input.focus()}),20):i.input.focus())})),c=function(e){o=o||Math.abs(t.clientX-e.clientX)+Math.abs(t.clientY-e.clientY)>=10},f=function(){return o=!0};s&&(i.scroller.draggable=!0),e.state.draggingText=u,u.copy=!n.moveOnDrag,me(i.wrapper.ownerDocument,"mouseup",u),me(i.wrapper.ownerDocument,"mousemove",c),me(i.scroller,"dragstart",f),me(i.scroller,"drop",u),e.state.delayingBlurEvent=!0,setTimeout((function(){return i.input.focus()}),20),i.scroller.dragDrop&&i.scroller.dragDrop()}function Ta(e,t,r){if("char"==r)return new so(t,t);if("word"==r)return e.findWordAt(t);if("line"==r)return new so(st(t.line,0),gt(e.doc,st(t.line+1,0)));var n=r(e,t);return new so(n.from,n.to)}function Ma(e,t,r,n){l&&Qn(e);var i=e.display,o=e.doc;Le(t);var a,s,u=o.sel,c=u.ranges;if(n.addNew&&!n.extend?(s=o.sel.contains(r),a=s>-1?c[s]:new so(r,r)):(a=o.sel.primary(),s=o.sel.primIndex),"rectangle"==n.unit)n.addNew||(a=new so(r,r)),r=In(e,t,!0,!0),s=-1;else{var f=Ta(e,r,n.unit);a=n.extend?Io(a,f.anchor,f.head,n.extend):f}n.addNew?-1==s?(s=c.length,Ko(o,uo(e,c.concat([a]),s),{scroll:!1,origin:"*mouse"})):c.length>1&&c[s].empty()&&"char"==n.unit&&!n.extend?(Ko(o,uo(e,c.slice(0,s).concat(c.slice(s+1)),0),{scroll:!1,origin:"*mouse"}),u=o.sel):Vo(o,s,a,X):(s=0,Ko(o,new ao([a],0),X),u=o.sel);var d=r;function h(t){if(0!=ut(d,t))if(d=t,"rectangle"==n.unit){for(var i=[],l=e.options.tabSize,c=V(et(o,r.line).text,r.ch,l),f=V(et(o,t.line).text,t.ch,l),h=Math.min(c,f),p=Math.max(c,f),g=Math.min(r.line,t.line),v=Math.min(e.lastLine(),Math.max(r.line,t.line));g<=v;g++){var m=et(o,g).text,y=Y(m,h,l);h==p?i.push(new so(st(g,y),st(g,y))):m.length>y&&i.push(new so(st(g,y),st(g,Y(m,p,l))))}i.length||i.push(new so(r,r)),Ko(o,uo(e,u.ranges.slice(0,s).concat(i),s),{origin:"*mouse",scroll:!1}),e.scrollIntoView(t)}else{var b,w=a,x=Ta(e,t,n.unit),C=w.anchor;ut(x.anchor,C)>0?(b=x.head,C=ht(w.from(),x.anchor)):(b=x.anchor,C=dt(w.to(),x.head));var k=u.ranges.slice(0);k[s]=Na(e,new so(gt(o,C),b)),Ko(o,uo(e,k,s),X)}}var p=i.wrapper.getBoundingClientRect(),g=0;function v(t){var r=++g,l=In(e,t,!0,"rectangle"==n.unit);if(l)if(0!=ut(l,d)){e.curOp.focus=H(z(e)),h(l);var a=ii(i,o);(l.line>=a.to||l.line<a.from)&&setTimeout(Ei(e,(function(){g==r&&v(t)})),150)}else{var s=t.clientY<p.top?-20:t.clientY>p.bottom?20:0;s&&setTimeout(Ei(e,(function(){g==r&&(i.scroller.scrollTop+=s,v(t))})),50)}}function m(t){e.state.selectingText=!1,g=1/0,t&&(Le(t),i.input.focus()),be(i.wrapper.ownerDocument,"mousemove",y),be(i.wrapper.ownerDocument,"mouseup",b),o.history.lastSelOrigin=null}var y=Ei(e,(function(e){0!==e.buttons&&Ae(e)?v(e):m(e)})),b=Ei(e,m);e.state.selectingText=b,me(i.wrapper.ownerDocument,"mousemove",y),me(i.wrapper.ownerDocument,"mouseup",b)}function Na(e,t){var r=t.anchor,n=t.head,i=et(e.doc,r.line);if(0==ut(r,n)&&r.sticky==n.sticky)return t;var o=ge(i);if(!o)return t;var l=he(o,r.ch,r.sticky),a=o[l];if(a.from!=r.ch&&a.to!=r.ch)return t;var s,u=l+(a.from==r.ch==(1!=a.level)?0:1);if(0==u||u==o.length)return t;if(n.line!=r.line)s=(n.line-r.line)*("ltr"==e.doc.direction?1:-1)>0;else{var c=he(o,n.ch,n.sticky),f=c-l||(n.ch-r.ch)*(1==a.level?-1:1);s=c==u-1||c==u?f<0:f>0}var d=o[u+(s?-1:0)],h=s==(1==d.level),p=h?d.from:d.to,g=h?"after":"before";return r.ch==p&&r.sticky==g?t:new so(new st(r.line,p,g),n)}function Oa(e,t,r,n){var i,o;if(t.touches)i=t.touches[0].clientX,o=t.touches[0].clientY;else try{i=t.clientX,o=t.clientY}catch(e){return!1}if(i>=Math.floor(e.display.gutters.getBoundingClientRect().right))return!1;n&&Le(t);var l=e.display,a=l.lineDiv.getBoundingClientRect();if(o>a.bottom||!ke(e,r))return Me(t);o-=a.top-l.viewOffset;for(var s=0;s<e.display.gutterSpecs.length;++s){var u=l.gutters.childNodes[s];if(u&&u.getBoundingClientRect().right>=i)return we(e,r,e,ot(e.doc,o),e.display.gutterSpecs[s].className,t),Me(t)}}function Aa(e,t){return Oa(e,t,"gutterClick",!0)}function Da(e,t){_r(e.display,t)||Wa(e,t)||xe(e,t,"contextmenu")||S||e.display.input.onContextMenu(t)}function Wa(e,t){return!!ke(e,"gutterContextMenu")&&Oa(e,t,"gutterContextMenu",!1)}function Ha(e){e.display.wrapper.className=e.display.wrapper.className.replace(/\s*cm-s-\S+/g,"")+e.options.theme.replace(/(^|\s)\s*/g," cm-s-"),gn(e)}ba.prototype.compare=function(e,t,r){return this.time+ya>e&&0==ut(t,this.pos)&&r==this.button};var Fa={toString:function(){return"CodeMirror.Init"}},Ea={},Pa={};function za(e){var t=e.optionHandlers;function r(r,n,i,o){e.defaults[r]=n,i&&(t[r]=o?function(e,t,r){r!=Fa&&i(e,t,r)}:i)}e.defineOption=r,e.Init=Fa,r("value","",(function(e,t){return e.setValue(t)}),!0),r("mode",null,(function(e,t){e.doc.modeOption=t,mo(e)}),!0),r("indentUnit",2,mo,!0),r("indentWithTabs",!1),r("smartIndent",!0),r("tabSize",4,(function(e){yo(e),gn(e),Bn(e)}),!0),r("lineSeparator",null,(function(e,t){if(e.doc.lineSep=t,t){var r=[],n=e.doc.first;e.doc.iter((function(e){for(var i=0;;){var o=e.text.indexOf(t,i);if(-1==o)break;i=o+t.length,r.push(st(n,o))}n++}));for(var i=r.length-1;i>=0;i--)al(e.doc,t,r[i],st(r[i].line,r[i].ch+t.length))}})),r("specialChars",/[\u0000-\u001f\u007f-\u009f\u00ad\u061c\u200b\u200e\u200f\u2028\u2029\u202d\u202e\u2066\u2067\u2069\ufeff\ufff9-\ufffc]/g,(function(e,t,r){e.state.specialChars=new RegExp(t.source+(t.test("\t")?"":"|\t"),"g"),r!=Fa&&e.refresh()})),r("specialCharPlaceholder",yr,(function(e){return e.refresh()}),!0),r("electricChars",!0),r("inputStyle",y?"contenteditable":"textarea",(function(){throw new Error("inputStyle can not (yet) be changed in a running editor")}),!0),r("spellcheck",!1,(function(e,t){return e.getInputField().spellcheck=t}),!0),r("autocorrect",!1,(function(e,t){return e.getInputField().autocorrect=t}),!0),r("autocapitalize",!1,(function(e,t){return e.getInputField().autocapitalize=t}),!0),r("rtlMoveVisually",!x),r("wholeLineUpdateBefore",!0),r("theme","default",(function(e){Ha(e),eo(e)}),!0),r("keyMap","default",(function(e,t,r){var n=$l(t),i=r!=Fa&&$l(r);i&&i.detach&&i.detach(e,n),n.attach&&n.attach(e,i||null)})),r("extraKeys",null),r("configureMouse",null),r("lineWrapping",!1,Ra,!0),r("gutters",[],(function(e,t){e.display.gutterSpecs=Ji(t,e.options.lineNumbers),eo(e)}),!0),r("fixedGutter",!0,(function(e,t){e.display.gutters.style.left=t?En(e.display)+"px":"0",e.refresh()}),!0),r("coverGutterNextToScrollbar",!1,(function(e){return xi(e)}),!0),r("scrollbarStyle","native",(function(e){Si(e),xi(e),e.display.scrollbars.setScrollTop(e.doc.scrollTop),e.display.scrollbars.setScrollLeft(e.doc.scrollLeft)}),!0),r("lineNumbers",!1,(function(e,t){e.display.gutterSpecs=Ji(e.options.gutters,t),eo(e)}),!0),r("firstLineNumber",1,eo,!0),r("lineNumberFormatter",(function(e){return e}),eo,!0),r("showCursorWhenSelecting",!1,_n,!0),r("resetSelectionOnContextMenu",!0),r("lineWiseCopyCut",!0),r("pasteLinesPerSelection",!0),r("selectionsMayTouch",!1),r("readOnly",!1,(function(e,t){"nocursor"==t&&(ti(e),e.display.input.blur()),e.display.input.readOnlyChanged(t)})),r("screenReaderLabel",null,(function(e,t){t=""===t?null:t,e.display.input.screenReaderLabelChanged(t)})),r("disableInput",!1,(function(e,t){t||e.display.input.reset()}),!0),r("dragDrop",!0,Ia),r("allowDropFileTypes",null),r("cursorBlinkRate",530),r("cursorScrollMargin",0),r("cursorHeight",1,_n,!0),r("singleCursorHeightPerLine",!0,_n,!0),r("workTime",100),r("workDelay",100),r("flattenSpans",!0,yo,!0),r("addModeClass",!1,yo,!0),r("pollInterval",100),r("undoDepth",200,(function(e,t){return e.doc.history.undoDepth=t})),r("historyEventDelay",1250),r("viewportMargin",10,(function(e){return e.refresh()}),!0),r("maxHighlightLength",1e4,yo,!0),r("moveInputWithCursor",!0,(function(e,t){t||e.display.input.resetPosition()})),r("tabindex",null,(function(e,t){return e.display.input.getField().tabIndex=t||""})),r("autofocus",null),r("direction","ltr",(function(e,t){return e.doc.setDirection(t)}),!0),r("phrases",null)}function Ia(e,t,r){if(!t!=!(r&&r!=Fa)){var n=e.display.dragFunctions,i=t?me:be;i(e.display.scroller,"dragstart",n.start),i(e.display.scroller,"dragenter",n.enter),i(e.display.scroller,"dragover",n.over),i(e.display.scroller,"dragleave",n.leave),i(e.display.scroller,"drop",n.drop)}}function Ra(e){e.options.lineWrapping?(F(e.display.wrapper,"CodeMirror-wrap"),e.display.sizer.style.minWidth="",e.display.sizerWidth=null):(M(e.display.wrapper,"CodeMirror-wrap"),cr(e)),zn(e),Bn(e),gn(e),setTimeout((function(){return xi(e)}),100)}function Ba(e,t){var r=this;if(!(this instanceof Ba))return new Ba(e,t);this.options=t=t?B(t):{},B(Ea,t,!1);var n=t.value;"string"==typeof n?n=new Tl(n,t.mode,null,t.lineSeparator,t.direction):t.mode&&(n.modeOption=t.mode),this.doc=n;var i=new Ba.inputStyles[t.inputStyle](this),o=this.display=new to(e,n,i,t);for(var u in o.wrapper.CodeMirror=this,Ha(this),t.lineWrapping&&(this.display.wrapper.className+=" CodeMirror-wrap"),Si(this),this.state={keyMaps:[],overlays:[],modeGen:0,overwrite:!1,delayingBlurEvent:!1,focused:!1,suppressEdits:!1,pasteIncoming:-1,cutIncoming:-1,selectingText:!1,draggingText:!1,highlight:new G,keySeq:null,specialChars:null},t.autofocus&&!y&&o.input.focus(),l&&a<11&&setTimeout((function(){return r.display.input.reset(!0)}),20),Va(this),Fl(),Ti(this),this.curOp.forceUpdate=!0,Co(this,n),t.autofocus&&!y||this.hasFocus()?setTimeout((function(){r.hasFocus()&&!r.state.focused&&ei(r)}),20):ti(this),Pa)Pa.hasOwnProperty(u)&&Pa[u](this,t[u],Fa);Zi(this),t.finishInit&&t.finishInit(this);for(var c=0;c<Ga.length;++c)Ga[c](this);Mi(this),s&&t.lineWrapping&&"optimizelegibility"==getComputedStyle(o.lineDiv).textRendering&&(o.lineDiv.style.textRendering="auto")}function Va(e){var t=e.display;me(t.scroller,"mousedown",Ei(e,xa)),me(t.scroller,"dblclick",l&&a<11?Ei(e,(function(t){if(!xe(e,t)){var r=In(e,t);if(r&&!Aa(e,t)&&!_r(e.display,t)){Le(t);var n=e.findWordAt(r);Ro(e.doc,n.anchor,n.head)}}})):function(t){return xe(e,t)||Le(t)}),me(t.scroller,"contextmenu",(function(t){return Da(e,t)})),me(t.input.getField(),"contextmenu",(function(r){t.scroller.contains(r.target)||Da(e,r)}));var r,n={end:0};function i(){t.activeTouch&&(r=setTimeout((function(){return t.activeTouch=null}),1e3),(n=t.activeTouch).end=+new Date)}function o(e){if(1!=e.touches.length)return!1;var t=e.touches[0];return t.radiusX<=1&&t.radiusY<=1}function s(e,t){if(null==t.left)return!0;var r=t.left-e.left,n=t.top-e.top;return r*r+n*n>400}me(t.scroller,"touchstart",(function(i){if(!xe(e,i)&&!o(i)&&!Aa(e,i)){t.input.ensurePolled(),clearTimeout(r);var l=+new Date;t.activeTouch={start:l,moved:!1,prev:l-n.end<=300?n:null},1==i.touches.length&&(t.activeTouch.left=i.touches[0].pageX,t.activeTouch.top=i.touches[0].pageY)}})),me(t.scroller,"touchmove",(function(){t.activeTouch&&(t.activeTouch.moved=!0)})),me(t.scroller,"touchend",(function(r){var n=t.activeTouch;if(n&&!_r(t,r)&&null!=n.left&&!n.moved&&new Date-n.start<300){var o,l=e.coordsChar(t.activeTouch,"page");o=!n.prev||s(n,n.prev)?new so(l,l):!n.prev.prev||s(n,n.prev.prev)?e.findWordAt(l):new so(st(l.line,0),gt(e.doc,st(l.line+1,0))),e.setSelection(o.anchor,o.head),e.focus(),Le(r)}i()})),me(t.scroller,"touchcancel",i),me(t.scroller,"scroll",(function(){t.scroller.clientHeight&&(gi(e,t.scroller.scrollTop),mi(e,t.scroller.scrollLeft,!0),we(e,"scroll",e))})),me(t.scroller,"mousewheel",(function(t){return lo(e,t)})),me(t.scroller,"DOMMouseScroll",(function(t){return lo(e,t)})),me(t.wrapper,"scroll",(function(){return t.wrapper.scrollTop=t.wrapper.scrollLeft=0})),t.dragFunctions={enter:function(t){xe(e,t)||Ne(t)},over:function(t){xe(e,t)||(Al(e,t),Ne(t))},start:function(t){return Ol(e,t)},drop:Ei(e,Nl),leave:function(t){xe(e,t)||Dl(e)}};var u=t.input.getField();me(u,"keyup",(function(t){return pa.call(e,t)})),me(u,"keydown",Ei(e,da)),me(u,"keypress",Ei(e,ga)),me(u,"focus",(function(t){return ei(e,t)})),me(u,"blur",(function(t){return ti(e,t)}))}Ba.defaults=Ea,Ba.optionHandlers=Pa;var Ga=[];function ja(e,t,r,n){var i,o=e.doc;null==r&&(r="add"),"smart"==r&&(o.mode.indent?i=Ct(e,t).state:r="prev");var l=e.options.tabSize,a=et(o,t),s=V(a.text,null,l);a.stateAfter&&(a.stateAfter=null);var u,c=a.text.match(/^\s*/)[0];if(n||/\S/.test(a.text)){if("smart"==r&&((u=o.mode.indent(i,a.text.slice(c.length),a.text))==K||u>150)){if(!n)return;r="prev"}}else u=0,r="not";"prev"==r?u=t>o.first?V(et(o,t-1).text,null,l):0:"add"==r?u=s+e.options.indentUnit:"subtract"==r?u=s-e.options.indentUnit:"number"==typeof r&&(u=s+r),u=Math.max(0,u);var f="",d=0;if(e.options.indentWithTabs)for(var h=Math.floor(u/l);h;--h)d+=l,f+="\t";if(d<u&&(f+=Z(u-d)),f!=c)return al(o,f,st(t,0),st(t,c.length),"+input"),a.stateAfter=null,!0;for(var p=0;p<o.sel.ranges.length;p++){var g=o.sel.ranges[p];if(g.head.line==t&&g.head.ch<c.length){var v=st(t,c.length);Vo(o,p,new so(v,v));break}}}Ba.defineInitHook=function(e){return Ga.push(e)};var Ua=null;function Ka(e){Ua=e}function _a(e,t,r,n,i){var o=e.doc;e.display.shift=!1,n||(n=o.sel);var l=+new Date-200,a="paste"==i||e.state.pasteIncoming>l,s=ze(t),u=null;if(a&&n.ranges.length>1)if(Ua&&Ua.text.join("\n")==t){if(n.ranges.length%Ua.text.length==0){u=[];for(var c=0;c<Ua.text.length;c++)u.push(o.splitLines(Ua.text[c]))}}else s.length==n.ranges.length&&e.options.pasteLinesPerSelection&&(u=Q(s,(function(e){return[e]})));for(var f=e.curOp.updateInput,d=n.ranges.length-1;d>=0;d--){var h=n.ranges[d],p=h.from(),g=h.to();h.empty()&&(r&&r>0?p=st(p.line,p.ch-r):e.state.overwrite&&!a?g=st(g.line,Math.min(et(o,g.line).text.length,g.ch+J(s).length)):a&&Ua&&Ua.lineWise&&Ua.text.join("\n")==s.join("\n")&&(p=g=st(p.line,0)));var v={from:p,to:g,text:u?u[d%u.length]:s,origin:i||(a?"paste":e.state.cutIncoming>l?"cut":"+input")};tl(e.doc,v),Dr(e,"inputRead",e,v)}t&&!a&&$a(e,t),ci(e),e.curOp.updateInput<2&&(e.curOp.updateInput=f),e.curOp.typing=!0,e.state.pasteIncoming=e.state.cutIncoming=-1}function Xa(e,t){var r=e.clipboardData&&e.clipboardData.getData("Text");if(r)return e.preventDefault(),t.isReadOnly()||t.options.disableInput||!t.hasFocus()||Fi(t,(function(){return _a(t,r,0,null,"paste")})),!0}function $a(e,t){if(e.options.electricChars&&e.options.smartIndent)for(var r=e.doc.sel,n=r.ranges.length-1;n>=0;n--){var i=r.ranges[n];if(!(i.head.ch>100||n&&r.ranges[n-1].head.line==i.head.line)){var o=e.getModeAt(i.head),l=!1;if(o.electricChars){for(var a=0;a<o.electricChars.length;a++)if(t.indexOf(o.electricChars.charAt(a))>-1){l=ja(e,i.head.line,"smart");break}}else o.electricInput&&o.electricInput.test(et(e.doc,i.head.line).text.slice(0,i.head.ch))&&(l=ja(e,i.head.line,"smart"));l&&Dr(e,"electricInput",e,i.head.line)}}}function Ya(e){for(var t=[],r=[],n=0;n<e.doc.sel.ranges.length;n++){var i=e.doc.sel.ranges[n].head.line,o={anchor:st(i,0),head:st(i+1,0)};r.push(o),t.push(e.getRange(o.anchor,o.head))}return{text:t,ranges:r}}function qa(e,t,r,n){e.setAttribute("autocorrect",r?"":"off"),e.setAttribute("autocapitalize",n?"":"off"),e.setAttribute("spellcheck",!!t)}function Za(){var e=A("textarea",null,null,"position: absolute; bottom: -1em; padding: 0; width: 1px; height: 1em; min-height: 1em; outline: none"),t=A("div",[e],null,"overflow: hidden; position: relative; width: 3px; height: 0px;");return s?e.style.width="1000px":e.setAttribute("wrap","off"),v&&(e.style.border="1px solid black"),qa(e),t}function Ja(e){var t=e.optionHandlers,r=e.helpers={};e.prototype={constructor:e,focus:function(){I(this).focus(),this.display.input.focus()},setOption:function(e,r){var n=this.options,i=n[e];n[e]==r&&"mode"!=e||(n[e]=r,t.hasOwnProperty(e)&&Ei(this,t[e])(this,r,i),we(this,"optionChange",this,e))},getOption:function(e){return this.options[e]},getDoc:function(){return this.doc},addKeyMap:function(e,t){this.state.keyMaps[t?"push":"unshift"]($l(e))},removeKeyMap:function(e){for(var t=this.state.keyMaps,r=0;r<t.length;++r)if(t[r]==e||t[r].name==e)return t.splice(r,1),!0},addOverlay:Pi((function(t,r){var n=t.token?t:e.getMode(this.options,t);if(n.startState)throw new Error("Overlays may not be stateful.");ee(this.state.overlays,{mode:n,modeSpec:t,opaque:r&&r.opaque,priority:r&&r.priority||0},(function(e){return e.priority})),this.state.modeGen++,Bn(this)})),removeOverlay:Pi((function(e){for(var t=this.state.overlays,r=0;r<t.length;++r){var n=t[r].modeSpec;if(n==e||"string"==typeof e&&n.name==e)return t.splice(r,1),this.state.modeGen++,void Bn(this)}})),indentLine:Pi((function(e,t,r){"string"!=typeof t&&"number"!=typeof t&&(t=null==t?this.options.smartIndent?"smart":"prev":t?"add":"subtract"),lt(this.doc,e)&&ja(this,e,t,r)})),indentSelection:Pi((function(e){for(var t=this.doc.sel.ranges,r=-1,n=0;n<t.length;n++){var i=t[n];if(i.empty())i.head.line>r&&(ja(this,i.head.line,e,!0),r=i.head.line,n==this.doc.sel.primIndex&&ci(this));else{var o=i.from(),l=i.to(),a=Math.max(r,o.line);r=Math.min(this.lastLine(),l.line-(l.ch?0:1))+1;for(var s=a;s<r;++s)ja(this,s,e);var u=this.doc.sel.ranges;0==o.ch&&t.length==u.length&&u[n].from().ch>0&&Vo(this.doc,n,new so(o,u[n].to()),_)}}})),getTokenAt:function(e,t){return Mt(this,e,t)},getLineTokens:function(e,t){return Mt(this,st(e),t,!0)},getTokenTypeAt:function(e){e=gt(this.doc,e);var t,r=xt(this,et(this.doc,e.line)),n=0,i=(r.length-1)/2,o=e.ch;if(0==o)t=r[2];else for(;;){var l=n+i>>1;if((l?r[2*l-1]:0)>=o)i=l;else{if(!(r[2*l+1]<o)){t=r[2*l+2];break}n=l+1}}var a=t?t.indexOf("overlay "):-1;return a<0?t:0==a?null:t.slice(0,a-1)},getModeAt:function(t){var r=this.doc.mode;return r.innerMode?e.innerMode(r,this.getTokenAt(t).state).mode:r},getHelper:function(e,t){return this.getHelpers(e,t)[0]},getHelpers:function(e,t){var n=[];if(!r.hasOwnProperty(t))return n;var i=r[t],o=this.getModeAt(e);if("string"==typeof o[t])i[o[t]]&&n.push(i[o[t]]);else if(o[t])for(var l=0;l<o[t].length;l++){var a=i[o[t][l]];a&&n.push(a)}else o.helperType&&i[o.helperType]?n.push(i[o.helperType]):i[o.name]&&n.push(i[o.name]);for(var s=0;s<i._global.length;s++){var u=i._global[s];u.pred(o,this)&&-1==j(n,u.val)&&n.push(u.val)}return n},getStateAfter:function(e,t){var r=this.doc;return Ct(this,(e=pt(r,null==e?r.first+r.size-1:e))+1,t).state},cursorCoords:function(e,t){var r=this.doc.sel.primary();return Cn(this,null==e?r.head:"object"==typeof e?gt(this.doc,e):e?r.from():r.to(),t||"page")},charCoords:function(e,t){return xn(this,gt(this.doc,e),t||"page")},coordsChar:function(e,t){return Ln(this,(e=wn(this,e,t||"page")).left,e.top)},lineAtHeight:function(e,t){return e=wn(this,{top:e,left:0},t||"page").top,ot(this.doc,e+this.display.viewOffset)},heightAtLine:function(e,t,r){var n,i=!1;if("number"==typeof e){var o=this.doc.first+this.doc.size-1;e<this.doc.first?e=this.doc.first:e>o&&(e=o,i=!0),n=et(this.doc,e)}else n=e;return bn(this,n,{top:0,left:0},t||"page",r||i).top+(i?this.doc.height-sr(n):0)},defaultTextHeight:function(){return Wn(this.display)},defaultCharWidth:function(){return Hn(this.display)},getViewport:function(){return{from:this.display.viewFrom,to:this.display.viewTo}},addWidget:function(e,t,r,n,i){var o=this.display,l=(e=Cn(this,gt(this.doc,e))).bottom,a=e.left;if(t.style.position="absolute",t.setAttribute("cm-ignore-events","true"),this.display.input.setUneditable(t),o.sizer.appendChild(t),"over"==n)l=e.top;else if("above"==n||"near"==n){var s=Math.max(o.wrapper.clientHeight,this.doc.height),u=Math.max(o.sizer.clientWidth,o.lineSpace.clientWidth);("above"==n||e.bottom+t.offsetHeight>s)&&e.top>t.offsetHeight?l=e.top-t.offsetHeight:e.bottom+t.offsetHeight<=s&&(l=e.bottom),a+t.offsetWidth>u&&(a=u-t.offsetWidth)}t.style.top=l+"px",t.style.left=t.style.right="","right"==i?(a=o.sizer.clientWidth-t.offsetWidth,t.style.right="0px"):("left"==i?a=0:"middle"==i&&(a=(o.sizer.clientWidth-t.offsetWidth)/2),t.style.left=a+"px"),r&&ai(this,{left:a,top:l,right:a+t.offsetWidth,bottom:l+t.offsetHeight})},triggerOnKeyDown:Pi(da),triggerOnKeyPress:Pi(ga),triggerOnKeyUp:pa,triggerOnMouseDown:Pi(xa),execCommand:function(e){if(ea.hasOwnProperty(e))return ea[e].call(null,this)},triggerElectric:Pi((function(e){$a(this,e)})),findPosH:function(e,t,r,n){var i=1;t<0&&(i=-1,t=-t);for(var o=gt(this.doc,e),l=0;l<t&&!(o=Qa(this.doc,o,i,r,n)).hitSide;++l);return o},moveH:Pi((function(e,t){var r=this;this.extendSelectionsBy((function(n){return r.display.shift||r.doc.extend||n.empty()?Qa(r.doc,n.head,e,t,r.options.rtlMoveVisually):e<0?n.from():n.to()}),$)})),deleteH:Pi((function(e,t){var r=this.doc.sel,n=this.doc;r.somethingSelected()?n.replaceSelection("",null,"+delete"):Yl(this,(function(r){var i=Qa(n,r.head,e,t,!1);return e<0?{from:i,to:r.head}:{from:r.head,to:i}}))})),findPosV:function(e,t,r,n){var i=1,o=n;t<0&&(i=-1,t=-t);for(var l=gt(this.doc,e),a=0;a<t;++a){var s=Cn(this,l,"div");if(null==o?o=s.left:s.left=o,(l=es(this,s,i,r)).hitSide)break}return l},moveV:Pi((function(e,t){var r=this,n=this.doc,i=[],o=!this.display.shift&&!n.extend&&n.sel.somethingSelected();if(n.extendSelectionsBy((function(l){if(o)return e<0?l.from():l.to();var a=Cn(r,l.head,"div");null!=l.goalColumn&&(a.left=l.goalColumn),i.push(a.left);var s=es(r,a,e,t);return"page"==t&&l==n.sel.primary()&&ui(r,xn(r,s,"div").top-a.top),s}),$),i.length)for(var l=0;l<n.sel.ranges.length;l++)n.sel.ranges[l].goalColumn=i[l]})),findWordAt:function(e){var t=et(this.doc,e.line).text,r=e.ch,n=e.ch;if(t){var i=this.getHelper(e,"wordChars");"before"!=e.sticky&&n!=t.length||!r?++n:--r;for(var o=t.charAt(r),l=oe(o,i)?function(e){return oe(e,i)}:/\s/.test(o)?function(e){return/\s/.test(e)}:function(e){return!/\s/.test(e)&&!oe(e)};r>0&&l(t.charAt(r-1));)--r;for(;n<t.length&&l(t.charAt(n));)++n}return new so(st(e.line,r),st(e.line,n))},toggleOverwrite:function(e){null!=e&&e==this.state.overwrite||((this.state.overwrite=!this.state.overwrite)?F(this.display.cursorDiv,"CodeMirror-overwrite"):M(this.display.cursorDiv,"CodeMirror-overwrite"),we(this,"overwriteToggle",this,this.state.overwrite))},hasFocus:function(){return this.display.input.getField()==H(z(this))},isReadOnly:function(){return!(!this.options.readOnly&&!this.doc.cantEdit)},scrollTo:Pi((function(e,t){fi(this,e,t)})),getScrollInfo:function(){var e=this.display.scroller;return{left:e.scrollLeft,top:e.scrollTop,height:e.scrollHeight-qr(this)-this.display.barHeight,width:e.scrollWidth-qr(this)-this.display.barWidth,clientHeight:Jr(this),clientWidth:Zr(this)}},scrollIntoView:Pi((function(e,t){null==e?(e={from:this.doc.sel.primary().head,to:null},null==t&&(t=this.options.cursorScrollMargin)):"number"==typeof e?e={from:st(e,0),to:null}:null==e.from&&(e={from:e,to:null}),e.to||(e.to=e.from),e.margin=t||0,null!=e.from.line?di(this,e):pi(this,e.from,e.to,e.margin)})),setSize:Pi((function(e,t){var r=this,n=function(e){return"number"==typeof e||/^\d+$/.test(String(e))?e+"px":e};null!=e&&(this.display.wrapper.style.width=n(e)),null!=t&&(this.display.wrapper.style.height=n(t)),this.options.lineWrapping&&pn(this);var i=this.display.viewFrom;this.doc.iter(i,this.display.viewTo,(function(e){if(e.widgets)for(var t=0;t<e.widgets.length;t++)if(e.widgets[t].noHScroll){Vn(r,i,"widget");break}++i})),this.curOp.forceUpdate=!0,we(this,"refresh",this)})),operation:function(e){return Fi(this,e)},startOperation:function(){return Ti(this)},endOperation:function(){return Mi(this)},refresh:Pi((function(){var e=this.display.cachedTextHeight;Bn(this),this.curOp.forceUpdate=!0,gn(this),fi(this,this.doc.scrollLeft,this.doc.scrollTop),$i(this.display),(null==e||Math.abs(e-Wn(this.display))>.5||this.options.lineWrapping)&&zn(this),we(this,"refresh",this)})),swapDoc:Pi((function(e){var t=this.doc;return t.cm=null,this.state.selectingText&&this.state.selectingText(),Co(this,e),gn(this),this.display.input.reset(),fi(this,e.scrollLeft,e.scrollTop),this.curOp.forceScroll=!0,Dr(this,"swapDoc",this,t),t})),phrase:function(e){var t=this.options.phrases;return t&&Object.prototype.hasOwnProperty.call(t,e)?t[e]:e},getInputField:function(){return this.display.input.getField()},getWrapperElement:function(){return this.display.wrapper},getScrollerElement:function(){return this.display.scroller},getGutterElement:function(){return this.display.gutters}},Se(e),e.registerHelper=function(t,n,i){r.hasOwnProperty(t)||(r[t]=e[t]={_global:[]}),r[t][n]=i},e.registerGlobalHelper=function(t,n,i,o){e.registerHelper(t,n,o),r[t]._global.push({pred:i,val:o})}}function Qa(e,t,r,n,i){var o=t,l=r,a=et(e,t.line),s=i&&"rtl"==e.direction?-r:r;function u(){var r=t.line+s;return!(r<e.first||r>=e.first+e.size)&&(t=new st(r,t.ch,t.sticky),a=et(e,r))}function c(o){var l;if("codepoint"==n){var c=a.text.charCodeAt(t.ch+(r>0?0:-1));if(isNaN(c))l=null;else{var f=r>0?c>=55296&&c<56320:c>=56320&&c<57343;l=new st(t.line,Math.max(0,Math.min(a.text.length,t.ch+r*(f?2:1))),-r)}}else l=i?Ql(e.cm,a,t,r):Zl(a,t,r);if(null==l){if(o||!u())return!1;t=Jl(i,e.cm,a,t.line,s)}else t=l;return!0}if("char"==n||"codepoint"==n)c();else if("column"==n)c(!0);else if("word"==n||"group"==n)for(var f=null,d="group"==n,h=e.cm&&e.cm.getHelper(t,"wordChars"),p=!0;!(r<0)||c(!p);p=!1){var g=a.text.charAt(t.ch)||"\n",v=oe(g,h)?"w":d&&"\n"==g?"n":!d||/\s/.test(g)?null:"p";if(!d||p||v||(v="s"),f&&f!=v){r<0&&(r=1,c(),t.sticky="after");break}if(v&&(f=v),r>0&&!c(!p))break}var m=Zo(e,t,o,l,!0);return ct(o,m)&&(m.hitSide=!0),m}function es(e,t,r,n){var i,o,l=e.doc,a=t.left;if("page"==n){var s=Math.min(e.display.wrapper.clientHeight,I(e).innerHeight||l(e).documentElement.clientHeight),u=Math.max(s-.5*Wn(e.display),3);i=(r>0?t.bottom:t.top)+r*u}else"line"==n&&(i=r>0?t.bottom+3:t.top-3);for(;(o=Ln(e,a,i)).outside;){if(r<0?i<=0:i>=l.height){o.hitSide=!0;break}i+=5*r}return o}var ts=function(e){this.cm=e,this.lastAnchorNode=this.lastAnchorOffset=this.lastFocusNode=this.lastFocusOffset=null,this.polling=new G,this.composing=null,this.gracePeriod=!1,this.readDOMTimeout=null};function rs(e,t){var r=nn(e,t.line);if(!r||r.hidden)return null;var n=et(e.doc,t.line),i=en(r,n,t.line),o=ge(n,e.doc.direction),l="left";o&&(l=he(o,t.ch)%2?"right":"left");var a=un(i.map,t.ch,l);return a.offset="right"==a.collapse?a.end:a.start,a}function ns(e){for(var t=e;t;t=t.parentNode)if(/CodeMirror-gutter-wrapper/.test(t.className))return!0;return!1}function is(e,t){return t&&(e.bad=!0),e}function os(e,t,r,n,i){var o="",l=!1,a=e.doc.lineSeparator(),s=!1;function u(e){return function(t){return t.id==e}}function c(){l&&(o+=a,s&&(o+=a),l=s=!1)}function f(e){e&&(c(),o+=e)}function d(t){if(1==t.nodeType){var r=t.getAttribute("cm-text");if(r)return void f(r);var o,h=t.getAttribute("cm-marker");if(h){var p=e.findMarks(st(n,0),st(i+1,0),u(+h));return void(p.length&&(o=p[0].find(0))&&f(tt(e.doc,o.from,o.to).join(a)))}if("false"==t.getAttribute("contenteditable"))return;var g=/^(pre|div|p|li|table|br)$/i.test(t.nodeName);if(!/^br$/i.test(t.nodeName)&&0==t.textContent.length)return;g&&c();for(var v=0;v<t.childNodes.length;v++)d(t.childNodes[v]);/^(pre|p)$/i.test(t.nodeName)&&(s=!0),g&&(l=!0)}else 3==t.nodeType&&f(t.nodeValue.replace(/\u200b/g,"").replace(/\u00a0/g," "))}for(;d(t),t!=r;)t=t.nextSibling,s=!1;return o}function ls(e,t,r){var n;if(t==e.display.lineDiv){if(!(n=e.display.lineDiv.childNodes[r]))return is(e.clipPos(st(e.display.viewTo-1)),!0);t=null,r=0}else for(n=t;;n=n.parentNode){if(!n||n==e.display.lineDiv)return null;if(n.parentNode&&n.parentNode==e.display.lineDiv)break}for(var i=0;i<e.display.view.length;i++){var o=e.display.view[i];if(o.node==n)return as(o,t,r)}}function as(e,t,r){var n=e.text.firstChild,i=!1;if(!t||!W(n,t))return is(st(it(e.line),0),!0);if(t==n&&(i=!0,t=n.childNodes[r],r=0,!t)){var o=e.rest?J(e.rest):e.line;return is(st(it(o),o.text.length),i)}var l=3==t.nodeType?t:null,a=t;for(l||1!=t.childNodes.length||3!=t.firstChild.nodeType||(l=t.firstChild,r&&(r=l.nodeValue.length));a.parentNode!=n;)a=a.parentNode;var s=e.measure,u=s.maps;function c(t,r,n){for(var i=-1;i<(u?u.length:0);i++)for(var o=i<0?s.map:u[i],l=0;l<o.length;l+=3){var a=o[l+2];if(a==t||a==r){var c=it(i<0?e.line:e.rest[i]),f=o[l]+n;return(n<0||a!=t)&&(f=o[l+(n?1:0)]),st(c,f)}}}var f=c(l,a,r);if(f)return is(f,i);for(var d=a.nextSibling,h=l?l.nodeValue.length-r:0;d;d=d.nextSibling){if(f=c(d,d.firstChild,0))return is(st(f.line,f.ch-h),i);h+=d.textContent.length}for(var p=a.previousSibling,g=r;p;p=p.previousSibling){if(f=c(p,p.firstChild,-1))return is(st(f.line,f.ch+g),i);g+=p.textContent.length}}ts.prototype.init=function(e){var t=this,r=this,n=r.cm,i=r.div=e.lineDiv;function o(e){for(var t=e.target;t;t=t.parentNode){if(t==i)return!0;if(/\bCodeMirror-(?:line)?widget\b/.test(t.className))break}return!1}function l(e){if(o(e)&&!xe(n,e)){if(n.somethingSelected())Ka({lineWise:!1,text:n.getSelections()}),"cut"==e.type&&n.replaceSelection("",null,"cut");else{if(!n.options.lineWiseCopyCut)return;var t=Ya(n);Ka({lineWise:!0,text:t.text}),"cut"==e.type&&n.operation((function(){n.setSelections(t.ranges,0,_),n.replaceSelection("",null,"cut")}))}if(e.clipboardData){e.clipboardData.clearData();var l=Ua.text.join("\n");if(e.clipboardData.setData("Text",l),e.clipboardData.getData("Text")==l)return void e.preventDefault()}var a=Za(),s=a.firstChild;n.display.lineSpace.insertBefore(a,n.display.lineSpace.firstChild),s.value=Ua.text.join("\n");var u=H(i.ownerDocument);P(s),setTimeout((function(){n.display.lineSpace.removeChild(a),u.focus(),u==i&&r.showPrimarySelection()}),50)}}i.contentEditable=!0,qa(i,n.options.spellcheck,n.options.autocorrect,n.options.autocapitalize),me(i,"paste",(function(e){!o(e)||xe(n,e)||Xa(e,n)||a<=11&&setTimeout(Ei(n,(function(){return t.updateFromDOM()})),20)})),me(i,"compositionstart",(function(e){t.composing={data:e.data,done:!1}})),me(i,"compositionupdate",(function(e){t.composing||(t.composing={data:e.data,done:!1})})),me(i,"compositionend",(function(e){t.composing&&(e.data!=t.composing.data&&t.readFromDOMSoon(),t.composing.done=!0)})),me(i,"touchstart",(function(){return r.forceCompositionEnd()})),me(i,"input",(function(){t.composing||t.readFromDOMSoon()})),me(i,"copy",l),me(i,"cut",l)},ts.prototype.screenReaderLabelChanged=function(e){e?this.div.setAttribute("aria-label",e):this.div.removeAttribute("aria-label")},ts.prototype.prepareSelection=function(){var e=Xn(this.cm,!1);return e.focus=H(this.div.ownerDocument)==this.div,e},ts.prototype.showSelection=function(e,t){e&&this.cm.display.view.length&&((e.focus||t)&&this.showPrimarySelection(),this.showMultipleSelections(e))},ts.prototype.getSelection=function(){return this.cm.display.wrapper.ownerDocument.getSelection()},ts.prototype.showPrimarySelection=function(){var e=this.getSelection(),t=this.cm,n=t.doc.sel.primary(),i=n.from(),o=n.to();if(t.display.viewTo==t.display.viewFrom||i.line>=t.display.viewTo||o.line<t.display.viewFrom)e.removeAllRanges();else{var l=ls(t,e.anchorNode,e.anchorOffset),a=ls(t,e.focusNode,e.focusOffset);if(!l||l.bad||!a||a.bad||0!=ut(ht(l,a),i)||0!=ut(dt(l,a),o)){var s=t.display.view,u=i.line>=t.display.viewFrom&&rs(t,i)||{node:s[0].measure.map[2],offset:0},c=o.line<t.display.viewTo&&rs(t,o);if(!c){var f=s[s.length-1].measure,d=f.maps?f.maps[f.maps.length-1]:f.map;c={node:d[d.length-1],offset:d[d.length-2]-d[d.length-3]}}if(u&&c){var h,p=e.rangeCount&&e.getRangeAt(0);try{h=T(u.node,u.offset,c.offset,c.node)}catch(e){}h&&(!r&&t.state.focused?(e.collapse(u.node,u.offset),h.collapsed||(e.removeAllRanges(),e.addRange(h))):(e.removeAllRanges(),e.addRange(h)),p&&null==e.anchorNode?e.addRange(p):r&&this.startGracePeriod()),this.rememberSelection()}else e.removeAllRanges()}}},ts.prototype.startGracePeriod=function(){var e=this;clearTimeout(this.gracePeriod),this.gracePeriod=setTimeout((function(){e.gracePeriod=!1,e.selectionChanged()&&e.cm.operation((function(){return e.cm.curOp.selectionChanged=!0}))}),20)},ts.prototype.showMultipleSelections=function(e){O(this.cm.display.cursorDiv,e.cursors),O(this.cm.display.selectionDiv,e.selection)},ts.prototype.rememberSelection=function(){var e=this.getSelection();this.lastAnchorNode=e.anchorNode,this.lastAnchorOffset=e.anchorOffset,this.lastFocusNode=e.focusNode,this.lastFocusOffset=e.focusOffset},ts.prototype.selectionInEditor=function(){var e=this.getSelection();if(!e.rangeCount)return!1;var t=e.getRangeAt(0).commonAncestorContainer;return W(this.div,t)},ts.prototype.focus=function(){"nocursor"!=this.cm.options.readOnly&&(this.selectionInEditor()&&H(this.div.ownerDocument)==this.div||this.showSelection(this.prepareSelection(),!0),this.div.focus())},ts.prototype.blur=function(){this.div.blur()},ts.prototype.getField=function(){return this.div},ts.prototype.supportsTouch=function(){return!0},ts.prototype.receivedFocus=function(){var e=this,t=this;function r(){t.cm.state.focused&&(t.pollSelection(),t.polling.set(t.cm.options.pollInterval,r))}this.selectionInEditor()?setTimeout((function(){return e.pollSelection()}),20):Fi(this.cm,(function(){return t.cm.curOp.selectionChanged=!0})),this.polling.set(this.cm.options.pollInterval,r)},ts.prototype.selectionChanged=function(){var e=this.getSelection();return e.anchorNode!=this.lastAnchorNode||e.anchorOffset!=this.lastAnchorOffset||e.focusNode!=this.lastFocusNode||e.focusOffset!=this.lastFocusOffset},ts.prototype.pollSelection=function(){if(null==this.readDOMTimeout&&!this.gracePeriod&&this.selectionChanged()){var e=this.getSelection(),t=this.cm;if(m&&c&&this.cm.display.gutterSpecs.length&&ns(e.anchorNode))return this.cm.triggerOnKeyDown({type:"keydown",keyCode:8,preventDefault:Math.abs}),this.blur(),void this.focus();if(!this.composing){this.rememberSelection();var r=ls(t,e.anchorNode,e.anchorOffset),n=ls(t,e.focusNode,e.focusOffset);r&&n&&Fi(t,(function(){Ko(t.doc,co(r,n),_),(r.bad||n.bad)&&(t.curOp.selectionChanged=!0)}))}}},ts.prototype.pollContent=function(){null!=this.readDOMTimeout&&(clearTimeout(this.readDOMTimeout),this.readDOMTimeout=null);var e,t,r,n=this.cm,i=n.display,o=n.doc.sel.primary(),l=o.from(),a=o.to();if(0==l.ch&&l.line>n.firstLine()&&(l=st(l.line-1,et(n.doc,l.line-1).length)),a.ch==et(n.doc,a.line).text.length&&a.line<n.lastLine()&&(a=st(a.line+1,0)),l.line<i.viewFrom||a.line>i.viewTo-1)return!1;l.line==i.viewFrom||0==(e=Rn(n,l.line))?(t=it(i.view[0].line),r=i.view[0].node):(t=it(i.view[e].line),r=i.view[e-1].node.nextSibling);var s,u,c=Rn(n,a.line);if(c==i.view.length-1?(s=i.viewTo-1,u=i.lineDiv.lastChild):(s=it(i.view[c+1].line)-1,u=i.view[c+1].node.previousSibling),!r)return!1;for(var f=n.doc.splitLines(os(n,r,u,t,s)),d=tt(n.doc,st(t,0),st(s,et(n.doc,s).text.length));f.length>1&&d.length>1;)if(J(f)==J(d))f.pop(),d.pop(),s--;else{if(f[0]!=d[0])break;f.shift(),d.shift(),t++}for(var h=0,p=0,g=f[0],v=d[0],m=Math.min(g.length,v.length);h<m&&g.charCodeAt(h)==v.charCodeAt(h);)++h;for(var y=J(f),b=J(d),w=Math.min(y.length-(1==f.length?h:0),b.length-(1==d.length?h:0));p<w&&y.charCodeAt(y.length-p-1)==b.charCodeAt(b.length-p-1);)++p;if(1==f.length&&1==d.length&&t==l.line)for(;h&&h>l.ch&&y.charCodeAt(y.length-p-1)==b.charCodeAt(b.length-p-1);)h--,p++;f[f.length-1]=y.slice(0,y.length-p).replace(/^\u200b+/,""),f[0]=f[0].slice(h).replace(/\u200b+$/,"");var x=st(t,h),C=st(s,d.length?J(d).length-p:0);return f.length>1||f[0]||ut(x,C)?(al(n.doc,f,x,C,"+input"),!0):void 0},ts.prototype.ensurePolled=function(){this.forceCompositionEnd()},ts.prototype.reset=function(){this.forceCompositionEnd()},ts.prototype.forceCompositionEnd=function(){this.composing&&(clearTimeout(this.readDOMTimeout),this.composing=null,this.updateFromDOM(),this.div.blur(),this.div.focus())},ts.prototype.readFromDOMSoon=function(){var e=this;null==this.readDOMTimeout&&(this.readDOMTimeout=setTimeout((function(){if(e.readDOMTimeout=null,e.composing){if(!e.composing.done)return;e.composing=null}e.updateFromDOM()}),80))},ts.prototype.updateFromDOM=function(){var e=this;!this.cm.isReadOnly()&&this.pollContent()||Fi(this.cm,(function(){return Bn(e.cm)}))},ts.prototype.setUneditable=function(e){e.contentEditable="false"},ts.prototype.onKeyPress=function(e){0==e.charCode||this.composing||(e.preventDefault(),this.cm.isReadOnly()||Ei(this.cm,_a)(this.cm,String.fromCharCode(null==e.charCode?e.keyCode:e.charCode),0))},ts.prototype.readOnlyChanged=function(e){this.div.contentEditable=String("nocursor"!=e)},ts.prototype.onContextMenu=function(){},ts.prototype.resetPosition=function(){},ts.prototype.needsContentAttribute=!0;var ss=function(e){this.cm=e,this.prevInput="",this.pollingFast=!1,this.polling=new G,this.hasSelection=!1,this.composing=null,this.resetting=!1};function us(e,t){if((t=t?B(t):{}).value=e.value,!t.tabindex&&e.tabIndex&&(t.tabindex=e.tabIndex),!t.placeholder&&e.placeholder&&(t.placeholder=e.placeholder),null==t.autofocus){var r=H(e.ownerDocument);t.autofocus=r==e||null!=e.getAttribute("autofocus")&&r==document.body}function n(){e.value=a.getValue()}var i;if(e.form&&(me(e.form,"submit",n),!t.leaveSubmitMethodAlone)){var o=e.form;i=o.submit;try{var l=o.submit=function(){n(),o.submit=i,o.submit(),o.submit=l}}catch(e){}}t.finishInit=function(r){r.save=n,r.getTextArea=function(){return e},r.toTextArea=function(){r.toTextArea=isNaN,n(),e.parentNode.removeChild(r.getWrapperElement()),e.style.display="",e.form&&(be(e.form,"submit",n),t.leaveSubmitMethodAlone||"function"!=typeof e.form.submit||(e.form.submit=i))}},e.style.display="none";var a=Ba((function(t){return e.parentNode.insertBefore(t,e.nextSibling)}),t);return a}function cs(e){e.off=be,e.on=me,e.wheelEventPixels=oo,e.Doc=Tl,e.splitLines=ze,e.countColumn=V,e.findColumn=Y,e.isWordChar=ie,e.Pass=K,e.signal=we,e.Line=fr,e.changeEnd=fo,e.scrollbarModel=ki,e.Pos=st,e.cmpPos=ut,e.modes=Ge,e.mimeModes=je,e.resolveMode=_e,e.getMode=Xe,e.modeExtensions=$e,e.extendMode=Ye,e.copyState=qe,e.startState=Je,e.innerMode=Ze,e.commands=ea,e.keyMap=Vl,e.keyName=Xl,e.isModifierKey=Kl,e.lookupKey=Ul,e.normalizeKeyMap=jl,e.StringStream=Qe,e.SharedTextMarker=wl,e.TextMarker=yl,e.LineWidget=pl,e.e_preventDefault=Le,e.e_stopPropagation=Te,e.e_stop=Ne,e.addClass=F,e.contains=W,e.rmClass=M,e.keyNames=zl}ss.prototype.init=function(e){var t=this,r=this,n=this.cm;this.createField(e);var i=this.textarea;function o(e){if(!xe(n,e)){if(n.somethingSelected())Ka({lineWise:!1,text:n.getSelections()});else{if(!n.options.lineWiseCopyCut)return;var t=Ya(n);Ka({lineWise:!0,text:t.text}),"cut"==e.type?n.setSelections(t.ranges,null,_):(r.prevInput="",i.value=t.text.join("\n"),P(i))}"cut"==e.type&&(n.state.cutIncoming=+new Date)}}e.wrapper.insertBefore(this.wrapper,e.wrapper.firstChild),v&&(i.style.width="0px"),me(i,"input",(function(){l&&a>=9&&t.hasSelection&&(t.hasSelection=null),r.poll()})),me(i,"paste",(function(e){xe(n,e)||Xa(e,n)||(n.state.pasteIncoming=+new Date,r.fastPoll())})),me(i,"cut",o),me(i,"copy",o),me(e.scroller,"paste",(function(t){if(!_r(e,t)&&!xe(n,t)){if(!i.dispatchEvent)return n.state.pasteIncoming=+new Date,void r.focus();var o=new Event("paste");o.clipboardData=t.clipboardData,i.dispatchEvent(o)}})),me(e.lineSpace,"selectstart",(function(t){_r(e,t)||Le(t)})),me(i,"compositionstart",(function(){var e=n.getCursor("from");r.composing&&r.composing.range.clear(),r.composing={start:e,range:n.markText(e,n.getCursor("to"),{className:"CodeMirror-composing"})}})),me(i,"compositionend",(function(){r.composing&&(r.poll(),r.composing.range.clear(),r.composing=null)}))},ss.prototype.createField=function(e){this.wrapper=Za(),this.textarea=this.wrapper.firstChild},ss.prototype.screenReaderLabelChanged=function(e){e?this.textarea.setAttribute("aria-label",e):this.textarea.removeAttribute("aria-label")},ss.prototype.prepareSelection=function(){var e=this.cm,t=e.display,r=e.doc,n=Xn(e);if(e.options.moveInputWithCursor){var i=Cn(e,r.sel.primary().head,"div"),o=t.wrapper.getBoundingClientRect(),l=t.lineDiv.getBoundingClientRect();n.teTop=Math.max(0,Math.min(t.wrapper.clientHeight-10,i.top+l.top-o.top)),n.teLeft=Math.max(0,Math.min(t.wrapper.clientWidth-10,i.left+l.left-o.left))}return n},ss.prototype.showSelection=function(e){var t=this.cm.display;O(t.cursorDiv,e.cursors),O(t.selectionDiv,e.selection),null!=e.teTop&&(this.wrapper.style.top=e.teTop+"px",this.wrapper.style.left=e.teLeft+"px")},ss.prototype.reset=function(e){if(!(this.contextMenuPending||this.composing&&e)){var t=this.cm;if(this.resetting=!0,t.somethingSelected()){this.prevInput="";var r=t.getSelection();this.textarea.value=r,t.state.focused&&P(this.textarea),l&&a>=9&&(this.hasSelection=r)}else e||(this.prevInput=this.textarea.value="",l&&a>=9&&(this.hasSelection=null));this.resetting=!1}},ss.prototype.getField=function(){return this.textarea},ss.prototype.supportsTouch=function(){return!1},ss.prototype.focus=function(){if("nocursor"!=this.cm.options.readOnly&&(!y||H(this.textarea.ownerDocument)!=this.textarea))try{this.textarea.focus()}catch(e){}},ss.prototype.blur=function(){this.textarea.blur()},ss.prototype.resetPosition=function(){this.wrapper.style.top=this.wrapper.style.left=0},ss.prototype.receivedFocus=function(){this.slowPoll()},ss.prototype.slowPoll=function(){var e=this;this.pollingFast||this.polling.set(this.cm.options.pollInterval,(function(){e.poll(),e.cm.state.focused&&e.slowPoll()}))},ss.prototype.fastPoll=function(){var e=!1,t=this;function r(){t.poll()||e?(t.pollingFast=!1,t.slowPoll()):(e=!0,t.polling.set(60,r))}t.pollingFast=!0,t.polling.set(20,r)},ss.prototype.poll=function(){var e=this,t=this.cm,r=this.textarea,n=this.prevInput;if(this.contextMenuPending||this.resetting||!t.state.focused||Ie(r)&&!n&&!this.composing||t.isReadOnly()||t.options.disableInput||t.state.keySeq)return!1;var i=r.value;if(i==n&&!t.somethingSelected())return!1;if(l&&a>=9&&this.hasSelection===i||b&&/[\uf700-\uf7ff]/.test(i))return t.display.input.reset(),!1;if(t.doc.sel==t.display.selForContextMenu){var o=i.charCodeAt(0);if(8203!=o||n||(n="​"),8666==o)return this.reset(),this.cm.execCommand("undo")}for(var s=0,u=Math.min(n.length,i.length);s<u&&n.charCodeAt(s)==i.charCodeAt(s);)++s;return Fi(t,(function(){_a(t,i.slice(s),n.length-s,null,e.composing?"*compose":null),i.length>1e3||i.indexOf("\n")>-1?r.value=e.prevInput="":e.prevInput=i,e.composing&&(e.composing.range.clear(),e.composing.range=t.markText(e.composing.start,t.getCursor("to"),{className:"CodeMirror-composing"}))})),!0},ss.prototype.ensurePolled=function(){this.pollingFast&&this.poll()&&(this.pollingFast=!1)},ss.prototype.onKeyPress=function(){l&&a>=9&&(this.hasSelection=null),this.fastPoll()},ss.prototype.onContextMenu=function(e){var t=this,r=t.cm,n=r.display,i=t.textarea;t.contextMenuPending&&t.contextMenuPending();var o=In(r,e),u=n.scroller.scrollTop;if(o&&!d){r.options.resetSelectionOnContextMenu&&-1==r.doc.sel.contains(o)&&Ei(r,Ko)(r.doc,co(o),_);var c,f=i.style.cssText,h=t.wrapper.style.cssText,p=t.wrapper.offsetParent.getBoundingClientRect();if(t.wrapper.style.cssText="position: static",i.style.cssText="position: absolute; width: 30px; height: 30px;\n      top: "+(e.clientY-p.top-5)+"px; left: "+(e.clientX-p.left-5)+"px;\n      z-index: 1000; background: "+(l?"rgba(255, 255, 255, .05)":"transparent")+";\n      outline: none; border-width: 0; outline: none; overflow: hidden; opacity: .05; filter: alpha(opacity=5);",s&&(c=i.ownerDocument.defaultView.scrollY),n.input.focus(),s&&i.ownerDocument.defaultView.scrollTo(null,c),n.input.reset(),r.somethingSelected()||(i.value=t.prevInput=" "),t.contextMenuPending=m,n.selForContextMenu=r.doc.sel,clearTimeout(n.detectingSelectAll),l&&a>=9&&v(),S){Ne(e);var g=function(){be(window,"mouseup",g),setTimeout(m,20)};me(window,"mouseup",g)}else setTimeout(m,50)}function v(){if(null!=i.selectionStart){var e=r.somethingSelected(),o="​"+(e?i.value:"");i.value="⇚",i.value=o,t.prevInput=e?"":"​",i.selectionStart=1,i.selectionEnd=o.length,n.selForContextMenu=r.doc.sel}}function m(){if(t.contextMenuPending==m&&(t.contextMenuPending=!1,t.wrapper.style.cssText=h,i.style.cssText=f,l&&a<9&&n.scrollbars.setScrollTop(n.scroller.scrollTop=u),null!=i.selectionStart)){(!l||l&&a<9)&&v();var e=0,o=function(){n.selForContextMenu==r.doc.sel&&0==i.selectionStart&&i.selectionEnd>0&&"​"==t.prevInput?Ei(r,Qo)(r):e++<10?n.detectingSelectAll=setTimeout(o,500):(n.selForContextMenu=null,n.input.reset())};n.detectingSelectAll=setTimeout(o,200)}}},ss.prototype.readOnlyChanged=function(e){e||this.reset(),this.textarea.disabled="nocursor"==e,this.textarea.readOnly=!!e},ss.prototype.setUneditable=function(){},ss.prototype.needsContentAttribute=!1,za(Ba),Ja(Ba);var fs="iter insert remove copy getEditor constructor".split(" ");for(var ds in Tl.prototype)Tl.prototype.hasOwnProperty(ds)&&j(fs,ds)<0&&(Ba.prototype[ds]=function(e){return function(){return e.apply(this.doc,arguments)}}(Tl.prototype[ds]));return Se(Tl),Ba.inputStyles={textarea:ss,contenteditable:ts},Ba.defineMode=function(e){Ba.defaults.mode||"null"==e||(Ba.defaults.mode=e),Ue.apply(this,arguments)},Ba.defineMIME=Ke,Ba.defineMode("null",(function(){return{token:function(e){return e.skipToEnd()}}})),Ba.defineMIME("text/plain","null"),Ba.defineExtension=function(e,t){Ba.prototype[e]=t},Ba.defineDocExtension=function(e,t){Tl.prototype[e]=t},Ba.fromTextArea=us,cs(Ba),Ba.version="5.65.9",Ba}()},792:function(e,t,r){!function(e){"use strict";e.defineMode("javascript",(function(t,r){var n,i,o=t.indentUnit,l=r.statementIndent,a=r.jsonld,s=r.json||a,u=!1!==r.trackScope,c=r.typescript,f=r.wordCharacters||/[\w$\xa1-\uffff]/,d=function(){function e(e){return{type:e,style:"keyword"}}var t=e("keyword a"),r=e("keyword b"),n=e("keyword c"),i=e("keyword d"),o=e("operator"),l={type:"atom",style:"atom"};return{if:e("if"),while:t,with:t,else:r,do:r,try:r,finally:r,return:i,break:i,continue:i,new:e("new"),delete:n,void:n,throw:n,debugger:e("debugger"),var:e("var"),const:e("var"),let:e("var"),function:e("function"),catch:e("catch"),for:e("for"),switch:e("switch"),case:e("case"),default:e("default"),in:o,typeof:o,instanceof:o,true:l,false:l,null:l,undefined:l,NaN:l,Infinity:l,this:e("this"),class:e("class"),super:e("atom"),yield:n,export:e("export"),import:e("import"),extends:n,await:n}}(),h=/[+\-*&%=<>!?|~^@]/,p=/^@(context|id|value|language|type|container|list|set|reverse|index|base|vocab|graph)"/;function g(e){for(var t,r=!1,n=!1;null!=(t=e.next());){if(!r){if("/"==t&&!n)return;"["==t?n=!0:n&&"]"==t&&(n=!1)}r=!r&&"\\"==t}}function v(e,t,r){return n=e,i=r,t}function m(e,t){var r=e.next();if('"'==r||"'"==r)return t.tokenize=y(r),t.tokenize(e,t);if("."==r&&e.match(/^\d[\d_]*(?:[eE][+\-]?[\d_]+)?/))return v("number","number");if("."==r&&e.match(".."))return v("spread","meta");if(/[\[\]{}\(\),;\:\.]/.test(r))return v(r);if("="==r&&e.eat(">"))return v("=>","operator");if("0"==r&&e.match(/^(?:x[\dA-Fa-f_]+|o[0-7_]+|b[01_]+)n?/))return v("number","number");if(/\d/.test(r))return e.match(/^[\d_]*(?:n|(?:\.[\d_]*)?(?:[eE][+\-]?[\d_]+)?)?/),v("number","number");if("/"==r)return e.eat("*")?(t.tokenize=b,b(e,t)):e.eat("/")?(e.skipToEnd(),v("comment","comment")):it(e,t,1)?(g(e),e.match(/^\b(([gimyus])(?![gimyus]*\2))+\b/),v("regexp","string-2")):(e.eat("="),v("operator","operator",e.current()));if("`"==r)return t.tokenize=w,w(e,t);if("#"==r&&"!"==e.peek())return e.skipToEnd(),v("meta","meta");if("#"==r&&e.eatWhile(f))return v("variable","property");if("<"==r&&e.match("!--")||"-"==r&&e.match("->")&&!/\S/.test(e.string.slice(0,e.start)))return e.skipToEnd(),v("comment","comment");if(h.test(r))return">"==r&&t.lexical&&">"==t.lexical.type||(e.eat("=")?"!"!=r&&"="!=r||e.eat("="):/[<>*+\-|&?]/.test(r)&&(e.eat(r),">"==r&&e.eat(r))),"?"==r&&e.eat(".")?v("."):v("operator","operator",e.current());if(f.test(r)){e.eatWhile(f);var n=e.current();if("."!=t.lastType){if(d.propertyIsEnumerable(n)){var i=d[n];return v(i.type,i.style,n)}if("async"==n&&e.match(/^(\s|\/\*([^*]|\*(?!\/))*?\*\/)*[\[\(\w]/,!1))return v("async","keyword",n)}return v("variable","variable",n)}}function y(e){return function(t,r){var n,i=!1;if(a&&"@"==t.peek()&&t.match(p))return r.tokenize=m,v("jsonld-keyword","meta");for(;null!=(n=t.next())&&(n!=e||i);)i=!i&&"\\"==n;return i||(r.tokenize=m),v("string","string")}}function b(e,t){for(var r,n=!1;r=e.next();){if("/"==r&&n){t.tokenize=m;break}n="*"==r}return v("comment","comment")}function w(e,t){for(var r,n=!1;null!=(r=e.next());){if(!n&&("`"==r||"$"==r&&e.eat("{"))){t.tokenize=m;break}n=!n&&"\\"==r}return v("quasi","string-2",e.current())}var x="([{}])";function C(e,t){t.fatArrowAt&&(t.fatArrowAt=null);var r=e.string.indexOf("=>",e.start);if(!(r<0)){if(c){var n=/:\s*(?:\w+(?:<[^>]*>|\[\])?|\{[^}]*\})\s*$/.exec(e.string.slice(e.start,r));n&&(r=n.index)}for(var i=0,o=!1,l=r-1;l>=0;--l){var a=e.string.charAt(l),s=x.indexOf(a);if(s>=0&&s<3){if(!i){++l;break}if(0==--i){"("==a&&(o=!0);break}}else if(s>=3&&s<6)++i;else if(f.test(a))o=!0;else if(/["'\/`]/.test(a))for(;;--l){if(0==l)return;if(e.string.charAt(l-1)==a&&"\\"!=e.string.charAt(l-2)){l--;break}}else if(o&&!i){++l;break}}o&&!i&&(t.fatArrowAt=l)}}var k={atom:!0,number:!0,variable:!0,string:!0,regexp:!0,this:!0,import:!0,"jsonld-keyword":!0};function S(e,t,r,n,i,o){this.indented=e,this.column=t,this.type=r,this.prev=i,this.info=o,null!=n&&(this.align=n)}function L(e,t){if(!u)return!1;for(var r=e.localVars;r;r=r.next)if(r.name==t)return!0;for(var n=e.context;n;n=n.prev)for(r=n.vars;r;r=r.next)if(r.name==t)return!0}function T(e,t,r,n,i){var o=e.cc;for(M.state=e,M.stream=i,M.marked=null,M.cc=o,M.style=t,e.lexical.hasOwnProperty("align")||(e.lexical.align=!0);;)if((o.length?o.pop():s?K:j)(r,n)){for(;o.length&&o[o.length-1].lex;)o.pop()();return M.marked?M.marked:"variable"==r&&L(e,n)?"variable-2":t}}var M={state:null,column:null,marked:null,cc:null};function N(){for(var e=arguments.length-1;e>=0;e--)M.cc.push(arguments[e])}function O(){return N.apply(null,arguments),!0}function A(e,t){for(var r=t;r;r=r.next)if(r.name==e)return!0;return!1}function D(e){var t=M.state;if(M.marked="def",u){if(t.context)if("var"==t.lexical.info&&t.context&&t.context.block){var n=W(e,t.context);if(null!=n)return void(t.context=n)}else if(!A(e,t.localVars))return void(t.localVars=new E(e,t.localVars));r.globalVars&&!A(e,t.globalVars)&&(t.globalVars=new E(e,t.globalVars))}}function W(e,t){if(t){if(t.block){var r=W(e,t.prev);return r?r==t.prev?t:new F(r,t.vars,!0):null}return A(e,t.vars)?t:new F(t.prev,new E(e,t.vars),!1)}return null}function H(e){return"public"==e||"private"==e||"protected"==e||"abstract"==e||"readonly"==e}function F(e,t,r){this.prev=e,this.vars=t,this.block=r}function E(e,t){this.name=e,this.next=t}var P=new E("this",new E("arguments",null));function z(){M.state.context=new F(M.state.context,M.state.localVars,!1),M.state.localVars=P}function I(){M.state.context=new F(M.state.context,M.state.localVars,!0),M.state.localVars=null}function R(){M.state.localVars=M.state.context.vars,M.state.context=M.state.context.prev}function B(e,t){var r=function(){var r=M.state,n=r.indented;if("stat"==r.lexical.type)n=r.lexical.indented;else for(var i=r.lexical;i&&")"==i.type&&i.align;i=i.prev)n=i.indented;r.lexical=new S(n,M.stream.column(),e,null,r.lexical,t)};return r.lex=!0,r}function V(){var e=M.state;e.lexical.prev&&(")"==e.lexical.type&&(e.indented=e.lexical.indented),e.lexical=e.lexical.prev)}function G(e){function t(r){return r==e?O():";"==e||"}"==r||")"==r||"]"==r?N():O(t)}return t}function j(e,t){return"var"==e?O(B("vardef",t),Ne,G(";"),V):"keyword a"==e?O(B("form"),X,j,V):"keyword b"==e?O(B("form"),j,V):"keyword d"==e?M.stream.match(/^\s*$/,!1)?O():O(B("stat"),Y,G(";"),V):"debugger"==e?O(G(";")):"{"==e?O(B("}"),I,de,V,R):";"==e?O():"if"==e?("else"==M.state.lexical.info&&M.state.cc[M.state.cc.length-1]==V&&M.state.cc.pop()(),O(B("form"),X,j,V,Fe)):"function"==e?O(Ie):"for"==e?O(B("form"),I,Ee,j,R,V):"class"==e||c&&"interface"==t?(M.marked="keyword",O(B("form","class"==e?e:t),je,V)):"variable"==e?c&&"declare"==t?(M.marked="keyword",O(j)):c&&("module"==t||"enum"==t||"type"==t)&&M.stream.match(/^\s*\w/,!1)?(M.marked="keyword","enum"==t?O(tt):"type"==t?O(Be,G("operator"),me,G(";")):O(B("form"),Oe,G("{"),B("}"),de,V,V)):c&&"namespace"==t?(M.marked="keyword",O(B("form"),K,j,V)):c&&"abstract"==t?(M.marked="keyword",O(j)):O(B("stat"),oe):"switch"==e?O(B("form"),X,G("{"),B("}","switch"),I,de,V,V,R):"case"==e?O(K,G(":")):"default"==e?O(G(":")):"catch"==e?O(B("form"),z,U,j,V,R):"export"==e?O(B("stat"),Xe,V):"import"==e?O(B("stat"),Ye,V):"async"==e?O(j):"@"==t?O(K,j):N(B("stat"),K,G(";"),V)}function U(e){if("("==e)return O(Ve,G(")"))}function K(e,t){return $(e,t,!1)}function _(e,t){return $(e,t,!0)}function X(e){return"("!=e?N():O(B(")"),Y,G(")"),V)}function $(e,t,r){if(M.state.fatArrowAt==M.stream.start){var n=r?te:ee;if("("==e)return O(z,B(")"),ce(Ve,")"),V,G("=>"),n,R);if("variable"==e)return N(z,Oe,G("=>"),n,R)}var i=r?Z:q;return k.hasOwnProperty(e)?O(i):"function"==e?O(Ie,i):"class"==e||c&&"interface"==t?(M.marked="keyword",O(B("form"),Ge,V)):"keyword c"==e||"async"==e?O(r?_:K):"("==e?O(B(")"),Y,G(")"),V,i):"operator"==e||"spread"==e?O(r?_:K):"["==e?O(B("]"),et,V,i):"{"==e?fe(ae,"}",null,i):"quasi"==e?N(J,i):"new"==e?O(re(r)):O()}function Y(e){return e.match(/[;\}\)\],]/)?N():N(K)}function q(e,t){return","==e?O(Y):Z(e,t,!1)}function Z(e,t,r){var n=0==r?q:Z,i=0==r?K:_;return"=>"==e?O(z,r?te:ee,R):"operator"==e?/\+\+|--/.test(t)||c&&"!"==t?O(n):c&&"<"==t&&M.stream.match(/^([^<>]|<[^<>]*>)*>\s*\(/,!1)?O(B(">"),ce(me,">"),V,n):"?"==t?O(K,G(":"),i):O(i):"quasi"==e?N(J,n):";"!=e?"("==e?fe(_,")","call",n):"."==e?O(le,n):"["==e?O(B("]"),Y,G("]"),V,n):c&&"as"==t?(M.marked="keyword",O(me,n)):"regexp"==e?(M.state.lastType=M.marked="operator",M.stream.backUp(M.stream.pos-M.stream.start-1),O(i)):void 0:void 0}function J(e,t){return"quasi"!=e?N():"${"!=t.slice(t.length-2)?O(J):O(Y,Q)}function Q(e){if("}"==e)return M.marked="string-2",M.state.tokenize=w,O(J)}function ee(e){return C(M.stream,M.state),N("{"==e?j:K)}function te(e){return C(M.stream,M.state),N("{"==e?j:_)}function re(e){return function(t){return"."==t?O(e?ie:ne):"variable"==t&&c?O(Le,e?Z:q):N(e?_:K)}}function ne(e,t){if("target"==t)return M.marked="keyword",O(q)}function ie(e,t){if("target"==t)return M.marked="keyword",O(Z)}function oe(e){return":"==e?O(V,j):N(q,G(";"),V)}function le(e){if("variable"==e)return M.marked="property",O()}function ae(e,t){return"async"==e?(M.marked="property",O(ae)):"variable"==e||"keyword"==M.style?(M.marked="property","get"==t||"set"==t?O(se):(c&&M.state.fatArrowAt==M.stream.start&&(r=M.stream.match(/^\s*:\s*/,!1))&&(M.state.fatArrowAt=M.stream.pos+r[0].length),O(ue))):"number"==e||"string"==e?(M.marked=a?"property":M.style+" property",O(ue)):"jsonld-keyword"==e?O(ue):c&&H(t)?(M.marked="keyword",O(ae)):"["==e?O(K,he,G("]"),ue):"spread"==e?O(_,ue):"*"==t?(M.marked="keyword",O(ae)):":"==e?N(ue):void 0;var r}function se(e){return"variable"!=e?N(ue):(M.marked="property",O(Ie))}function ue(e){return":"==e?O(_):"("==e?N(Ie):void 0}function ce(e,t,r){function n(i,o){if(r?r.indexOf(i)>-1:","==i){var l=M.state.lexical;return"call"==l.info&&(l.pos=(l.pos||0)+1),O((function(r,n){return r==t||n==t?N():N(e)}),n)}return i==t||o==t?O():r&&r.indexOf(";")>-1?N(e):O(G(t))}return function(r,i){return r==t||i==t?O():N(e,n)}}function fe(e,t,r){for(var n=3;n<arguments.length;n++)M.cc.push(arguments[n]);return O(B(t,r),ce(e,t),V)}function de(e){return"}"==e?O():N(j,de)}function he(e,t){if(c){if(":"==e)return O(me);if("?"==t)return O(he)}}function pe(e,t){if(c&&(":"==e||"in"==t))return O(me)}function ge(e){if(c&&":"==e)return M.stream.match(/^\s*\w+\s+is\b/,!1)?O(K,ve,me):O(me)}function ve(e,t){if("is"==t)return M.marked="keyword",O()}function me(e,t){return"keyof"==t||"typeof"==t||"infer"==t||"readonly"==t?(M.marked="keyword",O("typeof"==t?_:me)):"variable"==e||"void"==t?(M.marked="type",O(Se)):"|"==t||"&"==t?O(me):"string"==e||"number"==e||"atom"==e?O(Se):"["==e?O(B("]"),ce(me,"]",","),V,Se):"{"==e?O(B("}"),be,V,Se):"("==e?O(ce(ke,")"),ye,Se):"<"==e?O(ce(me,">"),me):"quasi"==e?N(xe,Se):void 0}function ye(e){if("=>"==e)return O(me)}function be(e){return e.match(/[\}\)\]]/)?O():","==e||";"==e?O(be):N(we,be)}function we(e,t){return"variable"==e||"keyword"==M.style?(M.marked="property",O(we)):"?"==t||"number"==e||"string"==e?O(we):":"==e?O(me):"["==e?O(G("variable"),pe,G("]"),we):"("==e?N(Re,we):e.match(/[;\}\)\],]/)?void 0:O()}function xe(e,t){return"quasi"!=e?N():"${"!=t.slice(t.length-2)?O(xe):O(me,Ce)}function Ce(e){if("}"==e)return M.marked="string-2",M.state.tokenize=w,O(xe)}function ke(e,t){return"variable"==e&&M.stream.match(/^\s*[?:]/,!1)||"?"==t?O(ke):":"==e?O(me):"spread"==e?O(ke):N(me)}function Se(e,t){return"<"==t?O(B(">"),ce(me,">"),V,Se):"|"==t||"."==e||"&"==t?O(me):"["==e?O(me,G("]"),Se):"extends"==t||"implements"==t?(M.marked="keyword",O(me)):"?"==t?O(me,G(":"),me):void 0}function Le(e,t){if("<"==t)return O(B(">"),ce(me,">"),V,Se)}function Te(){return N(me,Me)}function Me(e,t){if("="==t)return O(me)}function Ne(e,t){return"enum"==t?(M.marked="keyword",O(tt)):N(Oe,he,We,He)}function Oe(e,t){return c&&H(t)?(M.marked="keyword",O(Oe)):"variable"==e?(D(t),O()):"spread"==e?O(Oe):"["==e?fe(De,"]"):"{"==e?fe(Ae,"}"):void 0}function Ae(e,t){return"variable"!=e||M.stream.match(/^\s*:/,!1)?("variable"==e&&(M.marked="property"),"spread"==e?O(Oe):"}"==e?N():"["==e?O(K,G("]"),G(":"),Ae):O(G(":"),Oe,We)):(D(t),O(We))}function De(){return N(Oe,We)}function We(e,t){if("="==t)return O(_)}function He(e){if(","==e)return O(Ne)}function Fe(e,t){if("keyword b"==e&&"else"==t)return O(B("form","else"),j,V)}function Ee(e,t){return"await"==t?O(Ee):"("==e?O(B(")"),Pe,V):void 0}function Pe(e){return"var"==e?O(Ne,ze):"variable"==e?O(ze):N(ze)}function ze(e,t){return")"==e?O():";"==e?O(ze):"in"==t||"of"==t?(M.marked="keyword",O(K,ze)):N(K,ze)}function Ie(e,t){return"*"==t?(M.marked="keyword",O(Ie)):"variable"==e?(D(t),O(Ie)):"("==e?O(z,B(")"),ce(Ve,")"),V,ge,j,R):c&&"<"==t?O(B(">"),ce(Te,">"),V,Ie):void 0}function Re(e,t){return"*"==t?(M.marked="keyword",O(Re)):"variable"==e?(D(t),O(Re)):"("==e?O(z,B(")"),ce(Ve,")"),V,ge,R):c&&"<"==t?O(B(">"),ce(Te,">"),V,Re):void 0}function Be(e,t){return"keyword"==e||"variable"==e?(M.marked="type",O(Be)):"<"==t?O(B(">"),ce(Te,">"),V):void 0}function Ve(e,t){return"@"==t&&O(K,Ve),"spread"==e?O(Ve):c&&H(t)?(M.marked="keyword",O(Ve)):c&&"this"==e?O(he,We):N(Oe,he,We)}function Ge(e,t){return"variable"==e?je(e,t):Ue(e,t)}function je(e,t){if("variable"==e)return D(t),O(Ue)}function Ue(e,t){return"<"==t?O(B(">"),ce(Te,">"),V,Ue):"extends"==t||"implements"==t||c&&","==e?("implements"==t&&(M.marked="keyword"),O(c?me:K,Ue)):"{"==e?O(B("}"),Ke,V):void 0}function Ke(e,t){return"async"==e||"variable"==e&&("static"==t||"get"==t||"set"==t||c&&H(t))&&M.stream.match(/^\s+[\w$\xa1-\uffff]/,!1)?(M.marked="keyword",O(Ke)):"variable"==e||"keyword"==M.style?(M.marked="property",O(_e,Ke)):"number"==e||"string"==e?O(_e,Ke):"["==e?O(K,he,G("]"),_e,Ke):"*"==t?(M.marked="keyword",O(Ke)):c&&"("==e?N(Re,Ke):";"==e||","==e?O(Ke):"}"==e?O():"@"==t?O(K,Ke):void 0}function _e(e,t){if("!"==t)return O(_e);if("?"==t)return O(_e);if(":"==e)return O(me,We);if("="==t)return O(_);var r=M.state.lexical.prev;return N(r&&"interface"==r.info?Re:Ie)}function Xe(e,t){return"*"==t?(M.marked="keyword",O(Qe,G(";"))):"default"==t?(M.marked="keyword",O(K,G(";"))):"{"==e?O(ce($e,"}"),Qe,G(";")):N(j)}function $e(e,t){return"as"==t?(M.marked="keyword",O(G("variable"))):"variable"==e?N(_,$e):void 0}function Ye(e){return"string"==e?O():"("==e?N(K):"."==e?N(q):N(qe,Ze,Qe)}function qe(e,t){return"{"==e?fe(qe,"}"):("variable"==e&&D(t),"*"==t&&(M.marked="keyword"),O(Je))}function Ze(e){if(","==e)return O(qe,Ze)}function Je(e,t){if("as"==t)return M.marked="keyword",O(qe)}function Qe(e,t){if("from"==t)return M.marked="keyword",O(K)}function et(e){return"]"==e?O():N(ce(_,"]"))}function tt(){return N(B("form"),Oe,G("{"),B("}"),ce(rt,"}"),V,V)}function rt(){return N(Oe,We)}function nt(e,t){return"operator"==e.lastType||","==e.lastType||h.test(t.charAt(0))||/[,.]/.test(t.charAt(0))}function it(e,t,r){return t.tokenize==m&&/^(?:operator|sof|keyword [bcd]|case|new|export|default|spread|[\[{}\(,;:]|=>)$/.test(t.lastType)||"quasi"==t.lastType&&/\{\s*$/.test(e.string.slice(0,e.pos-(r||0)))}return z.lex=I.lex=!0,R.lex=!0,V.lex=!0,{startState:function(e){var t={tokenize:m,lastType:"sof",cc:[],lexical:new S((e||0)-o,0,"block",!1),localVars:r.localVars,context:r.localVars&&new F(null,null,!1),indented:e||0};return r.globalVars&&"object"==typeof r.globalVars&&(t.globalVars=r.globalVars),t},token:function(e,t){if(e.sol()&&(t.lexical.hasOwnProperty("align")||(t.lexical.align=!1),t.indented=e.indentation(),C(e,t)),t.tokenize!=b&&e.eatSpace())return null;var r=t.tokenize(e,t);return"comment"==n?r:(t.lastType="operator"!=n||"++"!=i&&"--"!=i?n:"incdec",T(t,r,n,i,e))},indent:function(t,n){if(t.tokenize==b||t.tokenize==w)return e.Pass;if(t.tokenize!=m)return 0;var i,a=n&&n.charAt(0),s=t.lexical;if(!/^\s*else\b/.test(n))for(var u=t.cc.length-1;u>=0;--u){var c=t.cc[u];if(c==V)s=s.prev;else if(c!=Fe&&c!=R)break}for(;("stat"==s.type||"form"==s.type)&&("}"==a||(i=t.cc[t.cc.length-1])&&(i==q||i==Z)&&!/^[,\.=+\-*:?[\(]/.test(n));)s=s.prev;l&&")"==s.type&&"stat"==s.prev.type&&(s=s.prev);var f=s.type,d=a==f;return"vardef"==f?s.indented+("operator"==t.lastType||","==t.lastType?s.info.length+1:0):"form"==f&&"{"==a?s.indented:"form"==f?s.indented+o:"stat"==f?s.indented+(nt(t,n)?l||o:0):"switch"!=s.info||d||0==r.doubleIndentSwitch?s.align?s.column+(d?0:1):s.indented+(d?0:o):s.indented+(/^(?:case|default)\b/.test(n)?o:2*o)},electricInput:/^\s*(?:case .*?:|default:|\{|\})$/,blockCommentStart:s?null:"/*",blockCommentEnd:s?null:"*/",blockCommentContinue:s?null:" * ",lineComment:s?null:"//",fold:"brace",closeBrackets:"()[]{}''\"\"``",helperType:s?"json":"javascript",jsonldMode:a,jsonMode:s,expressionAllowed:it,skipExpression:function(t){T(t,"atom","atom","true",new e.StringStream("",2,null))}}})),e.registerHelper("wordChars","javascript",/[\w$]/),e.defineMIME("text/javascript","javascript"),e.defineMIME("text/ecmascript","javascript"),e.defineMIME("application/javascript","javascript"),e.defineMIME("application/x-javascript","javascript"),e.defineMIME("application/ecmascript","javascript"),e.defineMIME("application/json",{name:"javascript",json:!0}),e.defineMIME("application/x-json",{name:"javascript",json:!0}),e.defineMIME("application/manifest+json",{name:"javascript",json:!0}),e.defineMIME("application/ld+json",{name:"javascript",jsonld:!0}),e.defineMIME("text/typescript",{name:"javascript",typescript:!0}),e.defineMIME("application/typescript",{name:"javascript",typescript:!0})}(r(237))}},t={};function r(n){var i=t[n];if(void 0!==i)return i.exports;var o=t[n]={exports:{}};return e[n].call(o.exports,o,o.exports,r),o.exports}r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,{a:t}),t},r.d=function(e,t){for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},function(){"use strict";var e=r(237),t=r.n(e);r(795),r(274),r(792);const n={init(){document.addEventListener("DOMContentLoaded",(function(){if(void 0===CLD_METADATA)return;const e=document.getElementById("meta-data");t()(e,{value:JSON.stringify(CLD_METADATA,null,"  "),lineNumbers:!0,theme:"material",readOnly:!0,mode:{name:"javascript",json:!0},matchBrackets:!0,foldGutter:!0,htmlMode:!0,gutters:["CodeMirror-linenumbers","CodeMirror-foldgutter"],viewportMargin:50}).setSize(null,600)}))}};n.init()}()}();