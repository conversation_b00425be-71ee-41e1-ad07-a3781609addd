!function(){var t={616:function(t){t.exports=function(t,e){var r,n,i=0;function o(){var o,a,s=r,c=arguments.length;t:for(;s;){if(s.args.length===arguments.length){for(a=0;a<c;a++)if(s.args[a]!==arguments[a]){s=s.next;continue t}return s!==r&&(s===n&&(n=s.prev),s.prev.next=s.next,s.next&&(s.next.prev=s.prev),s.next=r,s.prev=null,r.prev=s,r=s),s.val}s=s.next}for(o=new Array(c),a=0;a<c;a++)o[a]=arguments[a];return s={args:o,val:t.apply(null,o)},r?(r.prev=s,s.next=r):n=s,i===e.maxSize?(n=n.prev).next=null:i++,r=s,s.val}return e=e||{},o.clear=function(){r=null,n=null,i=0},o}},604:function(t,e,r){var n;!function(){"use strict";var i={not_string:/[^s]/,not_bool:/[^t]/,not_type:/[^T]/,not_primitive:/[^v]/,number:/[diefg]/,numeric_arg:/[bcdiefguxX]/,json:/[j]/,not_json:/[^j]/,text:/^[^\x25]+/,modulo:/^\x25{2}/,placeholder:/^\x25(?:([1-9]\d*)\$|\(([^)]+)\))?(\+)?(0|'[^$])?(-)?(\d+)?(?:\.(\d+))?([b-gijostTuvxX])/,key:/^([a-z_][a-z_\d]*)/i,key_access:/^\.([a-z_][a-z_\d]*)/i,index_access:/^\[(\d+)\]/,sign:/^[+-]/};function o(t){return function(t,e){var r,n,a,s,c,u,l,p,f,h=1,d=t.length,y="";for(n=0;n<d;n++)if("string"==typeof t[n])y+=t[n];else if("object"==typeof t[n]){if((s=t[n]).keys)for(r=e[h],a=0;a<s.keys.length;a++){if(null==r)throw new Error(o('[sprintf] Cannot access property "%s" of undefined value "%s"',s.keys[a],s.keys[a-1]));r=r[s.keys[a]]}else r=s.param_no?e[s.param_no]:e[h++];if(i.not_type.test(s.type)&&i.not_primitive.test(s.type)&&r instanceof Function&&(r=r()),i.numeric_arg.test(s.type)&&"number"!=typeof r&&isNaN(r))throw new TypeError(o("[sprintf] expecting number but found %T",r));switch(i.number.test(s.type)&&(p=r>=0),s.type){case"b":r=parseInt(r,10).toString(2);break;case"c":r=String.fromCharCode(parseInt(r,10));break;case"d":case"i":r=parseInt(r,10);break;case"j":r=JSON.stringify(r,null,s.width?parseInt(s.width):0);break;case"e":r=s.precision?parseFloat(r).toExponential(s.precision):parseFloat(r).toExponential();break;case"f":r=s.precision?parseFloat(r).toFixed(s.precision):parseFloat(r);break;case"g":r=s.precision?String(Number(r.toPrecision(s.precision))):parseFloat(r);break;case"o":r=(parseInt(r,10)>>>0).toString(8);break;case"s":r=String(r),r=s.precision?r.substring(0,s.precision):r;break;case"t":r=String(!!r),r=s.precision?r.substring(0,s.precision):r;break;case"T":r=Object.prototype.toString.call(r).slice(8,-1).toLowerCase(),r=s.precision?r.substring(0,s.precision):r;break;case"u":r=parseInt(r,10)>>>0;break;case"v":r=r.valueOf(),r=s.precision?r.substring(0,s.precision):r;break;case"x":r=(parseInt(r,10)>>>0).toString(16);break;case"X":r=(parseInt(r,10)>>>0).toString(16).toUpperCase()}i.json.test(s.type)?y+=r:(!i.number.test(s.type)||p&&!s.sign?f="":(f=p?"+":"-",r=r.toString().replace(i.sign,"")),u=s.pad_char?"0"===s.pad_char?"0":s.pad_char.charAt(1):" ",l=s.width-(f+r).length,c=s.width&&l>0?u.repeat(l):"",y+=s.align?f+r+c:"0"===u?f+c+r:c+f+r)}return y}(function(t){if(s[t])return s[t];var e,r=t,n=[],o=0;for(;r;){if(null!==(e=i.text.exec(r)))n.push(e[0]);else if(null!==(e=i.modulo.exec(r)))n.push("%");else{if(null===(e=i.placeholder.exec(r)))throw new SyntaxError("[sprintf] unexpected placeholder");if(e[2]){o|=1;var a=[],c=e[2],u=[];if(null===(u=i.key.exec(c)))throw new SyntaxError("[sprintf] failed to parse named argument key");for(a.push(u[1]);""!==(c=c.substring(u[0].length));)if(null!==(u=i.key_access.exec(c)))a.push(u[1]);else{if(null===(u=i.index_access.exec(c)))throw new SyntaxError("[sprintf] failed to parse named argument key");a.push(u[1])}e[2]=a}else o|=2;if(3===o)throw new Error("[sprintf] mixing positional and named placeholders is not (yet) supported");n.push({placeholder:e[0],param_no:e[1],keys:e[2],sign:e[3],pad_char:e[4],align:e[5],width:e[6],precision:e[7],type:e[8]})}r=r.substring(e[0].length)}return s[t]=n}(t),arguments)}function a(t,e){return o.apply(null,[t].concat(e||[]))}var s=Object.create(null);o,a,"undefined"!=typeof window&&(window.sprintf=o,window.vsprintf=a,void 0===(n=function(){return{sprintf:o,vsprintf:a}}.call(e,r,e,t))||(t.exports=n))}()},633:function(t,e,r){var n=r(738).default;function i(){"use strict";t.exports=i=function(){return r},t.exports.__esModule=!0,t.exports.default=t.exports;var e,r={},o=Object.prototype,a=o.hasOwnProperty,s=Object.defineProperty||function(t,e,r){t[e]=r.value},c="function"==typeof Symbol?Symbol:{},u=c.iterator||"@@iterator",l=c.asyncIterator||"@@asyncIterator",p=c.toStringTag||"@@toStringTag";function f(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{f({},"")}catch(e){f=function(t,e,r){return t[e]=r}}function h(t,e,r,n){var i=e&&e.prototype instanceof w?e:w,o=Object.create(i.prototype),a=new F(n||[]);return s(o,"_invoke",{value:A(t,r,a)}),o}function d(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}r.wrap=h;var y="suspendedStart",v="suspendedYield",b="executing",g="completed",m={};function w(){}function O(){}function j(){}var x={};f(x,u,(function(){return this}));var _=Object.getPrototypeOf,P=_&&_(_(T([])));P&&P!==o&&a.call(P,u)&&(x=P);var E=j.prototype=w.prototype=Object.create(x);function k(t){["next","throw","return"].forEach((function(e){f(t,e,(function(t){return this._invoke(e,t)}))}))}function S(t,e){function r(i,o,s,c){var u=d(t[i],t,o);if("throw"!==u.type){var l=u.arg,p=l.value;return p&&"object"==n(p)&&a.call(p,"__await")?e.resolve(p.__await).then((function(t){r("next",t,s,c)}),(function(t){r("throw",t,s,c)})):e.resolve(p).then((function(t){l.value=t,s(l)}),(function(t){return r("throw",t,s,c)}))}c(u.arg)}var i;s(this,"_invoke",{value:function(t,n){function o(){return new e((function(e,i){r(t,n,e,i)}))}return i=i?i.then(o,o):o()}})}function A(t,r,n){var i=y;return function(o,a){if(i===b)throw Error("Generator is already running");if(i===g){if("throw"===o)throw a;return{value:e,done:!0}}for(n.method=o,n.arg=a;;){var s=n.delegate;if(s){var c=L(s,n);if(c){if(c===m)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(i===y)throw i=g,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);i=b;var u=d(t,r,n);if("normal"===u.type){if(i=n.done?g:v,u.arg===m)continue;return{value:u.arg,done:n.done}}"throw"===u.type&&(i=g,n.method="throw",n.arg=u.arg)}}}function L(t,r){var n=r.method,i=t.iterator[n];if(i===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,L(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),m;var o=d(i,t.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,m;var a=o.arg;return a?a.done?(r[t.resultName]=a.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,m):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,m)}function D(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function I(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function F(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(D,this),this.reset(!0)}function T(t){if(t||""===t){var r=t[u];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var i=-1,o=function r(){for(;++i<t.length;)if(a.call(t,i))return r.value=t[i],r.done=!1,r;return r.value=e,r.done=!0,r};return o.next=o}}throw new TypeError(n(t)+" is not iterable")}return O.prototype=j,s(E,"constructor",{value:j,configurable:!0}),s(j,"constructor",{value:O,configurable:!0}),O.displayName=f(j,p,"GeneratorFunction"),r.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===O||"GeneratorFunction"===(e.displayName||e.name))},r.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,j):(t.__proto__=j,f(t,p,"GeneratorFunction")),t.prototype=Object.create(E),t},r.awrap=function(t){return{__await:t}},k(S.prototype),f(S.prototype,l,(function(){return this})),r.AsyncIterator=S,r.async=function(t,e,n,i,o){void 0===o&&(o=Promise);var a=new S(h(t,e,n,i),o);return r.isGeneratorFunction(e)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},k(E),f(E,p,"Generator"),f(E,u,(function(){return this})),f(E,"toString",(function(){return"[object Generator]"})),r.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},r.values=T,F.prototype={constructor:F,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(I),!t)for(var r in this)"t"===r.charAt(0)&&a.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function n(n,i){return s.type="throw",s.arg=t,r.next=n,i&&(r.method="next",r.arg=e),!!i}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],s=o.completion;if("root"===o.tryLoc)return n("end");if(o.tryLoc<=this.prev){var c=a.call(o,"catchLoc"),u=a.call(o,"finallyLoc");if(c&&u){if(this.prev<o.catchLoc)return n(o.catchLoc,!0);if(this.prev<o.finallyLoc)return n(o.finallyLoc)}else if(c){if(this.prev<o.catchLoc)return n(o.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return n(o.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&a.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var i=n;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=t,o.arg=e,i?(this.method="next",this.next=i.finallyLoc,m):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),m},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),I(r),m}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var i=n.arg;I(r)}return i}}throw Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:T(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),m}},r}t.exports=i,t.exports.__esModule=!0,t.exports.default=t.exports},738:function(t){function e(r){return t.exports=e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t.exports.__esModule=!0,t.exports.default=t.exports,e(r)}t.exports=e,t.exports.__esModule=!0,t.exports.default=t.exports},756:function(t,e,r){var n=r(633)();t.exports=n;try{regeneratorRuntime=n}catch(t){"object"==typeof globalThis?globalThis.regeneratorRuntime=n:Function("r","regeneratorRuntime = r")(n)}}},e={};function r(n){var i=e[n];if(void 0!==i)return i.exports;var o=e[n]={exports:{}};return t[n](o,o.exports,r),o.exports}r.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return r.d(e,{a:e}),e},r.d=function(t,e){for(var n in e)r.o(e,n)&&!r.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})},r.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},function(){"use strict";var t,e,n,i,o=r(616),a=r.n(o);r(604),a()(console.error);function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}function c(t){var e=function(t,e){if("object"!=s(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=s(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==s(e)?e:e+""}function u(t,e,r){return(e=c(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}t={"(":9,"!":8,"*":7,"/":7,"%":7,"+":6,"-":6,"<":5,"<=":5,">":5,">=":5,"==":4,"!=":4,"&&":3,"||":2,"?":1,"?:":1},e=["(","?"],n={")":["("],":":["?","?:"]},i=/<=|>=|==|!=|&&|\|\||\?:|\(|!|\*|\/|%|\+|-|<|>|\?|\)|:/;var l={"!":function(t){return!t},"*":function(t,e){return t*e},"/":function(t,e){return t/e},"%":function(t,e){return t%e},"+":function(t,e){return t+e},"-":function(t,e){return t-e},"<":function(t,e){return t<e},"<=":function(t,e){return t<=e},">":function(t,e){return t>e},">=":function(t,e){return t>=e},"==":function(t,e){return t===e},"!=":function(t,e){return t!==e},"&&":function(t,e){return t&&e},"||":function(t,e){return t||e},"?:":function(t,e,r){if(t)throw e;return r}};function p(r){var o=function(r){for(var o,a,s,c,u=[],l=[];o=r.match(i);){for(a=o[0],(s=r.substr(0,o.index).trim())&&u.push(s);c=l.pop();){if(n[a]){if(n[a][0]===c){a=n[a][1]||a;break}}else if(e.indexOf(c)>=0||t[c]<t[a]){l.push(c);break}u.push(c)}n[a]||l.push(a),r=r.substr(o.index+a.length)}return(r=r.trim())&&u.push(r),u.concat(l.reverse())}(r);return function(t){return function(t,e){var r,n,i,o,a,s,c=[];for(r=0;r<t.length;r++){if(a=t[r],o=l[a]){for(n=o.length,i=Array(n);n--;)i[n]=c.pop();try{s=o.apply(null,i)}catch(t){return t}}else s=e.hasOwnProperty(a)?e[a]:+a;c.push(s)}return c[0]}(o,t)}}var f={contextDelimiter:"",onMissingKey:null};function h(t,e){var r;for(r in this.data=t,this.pluralForms={},this.options={},f)this.options[r]=void 0!==e&&r in e?e[r]:f[r]}function d(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function y(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?d(Object(r),!0).forEach((function(e){u(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):d(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}h.prototype.getPluralForm=function(t,e){var r,n,i,o=this.pluralForms[t];return o||("function"!=typeof(i=(r=this.data[t][""])["Plural-Forms"]||r["plural-forms"]||r.plural_forms)&&(n=function(t){var e,r,n;for(e=t.split(";"),r=0;r<e.length;r++)if(0===(n=e[r].trim()).indexOf("plural="))return n.substr(7)}(r["Plural-Forms"]||r["plural-forms"]||r.plural_forms),i=function(t){var e=p(t);return function(t){return+e({n:t})}}(n)),o=this.pluralForms[t]=i),o(e)},h.prototype.dcnpgettext=function(t,e,r,n,i){var o,a,s;return o=void 0===i?0:this.getPluralForm(t,i),a=r,e&&(a=e+this.options.contextDelimiter+r),(s=this.data[t][a])&&s[o]?s[o]:(this.options.onMissingKey&&this.options.onMissingKey(r,t),0===o?r:n)};var v={"":{plural_forms:function(t){return 1===t?0:1}}},b=/^i18n\.(n?gettext|has_translation)(_|$)/;var g=function(t){return"string"!=typeof t||""===t?(console.error("The namespace must be a non-empty string."),!1):!!/^[a-zA-Z][a-zA-Z0-9_.\-\/]*$/.test(t)||(console.error("The namespace can only contain numbers, letters, dashes, periods, underscores and slashes."),!1)};var m=function(t){return"string"!=typeof t||""===t?(console.error("The hook name must be a non-empty string."),!1):/^__/.test(t)?(console.error("The hook name cannot begin with `__`."),!1):!!/^[a-zA-Z][a-zA-Z0-9_.-]*$/.test(t)||(console.error("The hook name can only contain numbers, letters, dashes, periods and underscores."),!1)};var w=function(t,e){return function(r,n,i){var o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:10,a=t[e];if(m(r)&&g(n))if("function"==typeof i)if("number"==typeof o){var s={callback:i,priority:o,namespace:n};if(a[r]){var c,u=a[r].handlers;for(c=u.length;c>0&&!(o>=u[c-1].priority);c--);c===u.length?u[c]=s:u.splice(c,0,s),a.__current.forEach((function(t){t.name===r&&t.currentIndex>=c&&t.currentIndex++}))}else a[r]={handlers:[s],runs:0};"hookAdded"!==r&&t.doAction("hookAdded",r,n,i,o)}else console.error("If specified, the hook priority must be a number.");else console.error("The hook callback must be a function.")}};var O=function(t,e){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return function(n,i){var o=t[e];if(m(n)&&(r||g(i))){if(!o[n])return 0;var a=0;if(r)a=o[n].handlers.length,o[n]={runs:o[n].runs,handlers:[]};else for(var s=o[n].handlers,c=function(t){s[t].namespace===i&&(s.splice(t,1),a++,o.__current.forEach((function(e){e.name===n&&e.currentIndex>=t&&e.currentIndex--})))},u=s.length-1;u>=0;u--)c(u);return"hookRemoved"!==n&&t.doAction("hookRemoved",n,i),a}}};var j=function(t,e){return function(r,n){var i=t[e];return void 0!==n?r in i&&i[r].handlers.some((function(t){return t.namespace===n})):r in i}};var x=function(t,e){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return function(n){var i=t[e];i[n]||(i[n]={handlers:[],runs:0}),i[n].runs++;var o=i[n].handlers;for(var a=arguments.length,s=new Array(a>1?a-1:0),c=1;c<a;c++)s[c-1]=arguments[c];if(!o||!o.length)return r?s[0]:void 0;var u={name:n,currentIndex:0};for(i.__current.push(u);u.currentIndex<o.length;){var l=o[u.currentIndex].callback.apply(null,s);r&&(s[0]=l),u.currentIndex++}return i.__current.pop(),r?s[0]:void 0}};var _=function(t,e){return function(){var r,n,i=t[e];return null!==(r=null===(n=i.__current[i.__current.length-1])||void 0===n?void 0:n.name)&&void 0!==r?r:null}};var P=function(t,e){return function(r){var n=t[e];return void 0===r?void 0!==n.__current[0]:!!n.__current[0]&&r===n.__current[0].name}};var E=function(t,e){return function(r){var n=t[e];if(m(r))return n[r]&&n[r].runs?n[r].runs:0}},k=function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.actions=Object.create(null),this.actions.__current=[],this.filters=Object.create(null),this.filters.__current=[],this.addAction=w(this,"actions"),this.addFilter=w(this,"filters"),this.removeAction=O(this,"actions"),this.removeFilter=O(this,"filters"),this.hasAction=j(this,"actions"),this.hasFilter=j(this,"filters"),this.removeAllActions=O(this,"actions",!0),this.removeAllFilters=O(this,"filters",!0),this.doAction=x(this,"actions"),this.applyFilters=x(this,"filters",!0),this.currentAction=_(this,"actions"),this.currentFilter=_(this,"filters"),this.doingAction=P(this,"actions"),this.doingFilter=P(this,"filters"),this.didAction=E(this,"actions"),this.didFilter=E(this,"filters")};var S=function(){return new k}(),A=(S.addAction,S.addFilter,S.removeAction,S.removeFilter,S.hasAction,S.hasFilter,S.removeAllActions,S.removeAllFilters,S.doAction,S.applyFilters,S.currentAction,S.currentFilter,S.doingAction,S.doingFilter,S.didAction,S.didFilter,S.actions,S.filters,function(t,e,r){var n=new h({}),i=new Set,o=function(){i.forEach((function(t){return t()}))},a=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"default";n.data[e]=y(y(y({},v),n.data[e]),t),n.data[e][""]=y(y({},v[""]),n.data[e][""])},s=function(t,e){a(t,e),o()},c=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"default",e=arguments.length>1?arguments[1]:void 0,r=arguments.length>2?arguments[2]:void 0,i=arguments.length>3?arguments[3]:void 0,o=arguments.length>4?arguments[4]:void 0;return n.data[t]||a(void 0,t),n.dcnpgettext(t,e,r,i,o)},u=function(){return arguments.length>0&&void 0!==arguments[0]?arguments[0]:"default"},l=function(t,e,n){var i=c(n,e,t);return r?(i=r.applyFilters("i18n.gettext_with_context",i,t,e,n),r.applyFilters("i18n.gettext_with_context_"+u(n),i,t,e,n)):i};if(t&&s(t,e),r){var p=function(t){b.test(t)&&o()};r.addAction("hookAdded","core/i18n",p),r.addAction("hookRemoved","core/i18n",p)}return{getLocaleData:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"default";return n.data[t]},setLocaleData:s,resetLocaleData:function(t,e){n.data={},n.pluralForms={},s(t,e)},subscribe:function(t){return i.add(t),function(){return i.delete(t)}},__:function(t,e){var n=c(e,void 0,t);return r?(n=r.applyFilters("i18n.gettext",n,t,e),r.applyFilters("i18n.gettext_"+u(e),n,t,e)):n},_x:l,_n:function(t,e,n,i){var o=c(i,void 0,t,e,n);return r?(o=r.applyFilters("i18n.ngettext",o,t,e,n,i),r.applyFilters("i18n.ngettext_"+u(i),o,t,e,n,i)):o},_nx:function(t,e,n,i,o){var a=c(o,i,t,e,n);return r?(a=r.applyFilters("i18n.ngettext_with_context",a,t,e,n,i,o),r.applyFilters("i18n.ngettext_with_context_"+u(o),a,t,e,n,i,o)):a},isRTL:function(){return"rtl"===l("ltr","text direction")},hasTranslation:function(t,e,i){var o,a,s=e?e+""+t:t,c=!(null===(o=n.data)||void 0===o||null===(a=o[null!=i?i:"default"])||void 0===a||!a[s]);return r&&(c=r.applyFilters("i18n.has_translation",c,t,e,i),c=r.applyFilters("i18n.has_translation_"+u(i),c,t,e,i)),c}}}(void 0,void 0,S)),L=(A.getLocaleData.bind(A),A.setLocaleData.bind(A),A.resetLocaleData.bind(A),A.subscribe.bind(A),A.__.bind(A));A._x.bind(A),A._n.bind(A),A._nx.bind(A),A.isRTL.bind(A),A.hasTranslation.bind(A);var D={preview:null,wrap:null,apply:null,url:null,defaultWidth:null,defaultHeight:null,maxSize:null,init(){return this},createPreview(t=400,e=300){return this.maxSize=t>e?t:e,this.defaultWidth=t,this.defaultHeight=e,this.wrap=document.createElement("div"),this.apply=document.createElement("button"),this.preview=document.createElement("img"),this.apply.type="button",this.apply.classList.add("button-primary"),this.apply.innerText=L("Preview","cloudinary"),this.preview.style.transition="opacity 1s",this.preview.style.opacity=1,this.preview.style.maxWidth="100%",this.preview.style.maxHeight="100%",this.reset(),this.wrap.style.minHeight="200px",this.wrap.style.width=this.maxSize+"px",this.wrap.style.position="relative",this.wrap.style.display="flex",this.wrap.style.alignItems="center",this.wrap.style.justifyContent="center",this.apply.style.position="absolute",this.apply.style.display="none",this.wrap.appendChild(this.preview),this.wrap.appendChild(this.apply),this.preview.addEventListener("load",(t=>{this.preview.style.opacity=1,this.wrap.style.width="",this.wrap.style.height="",this.defaultHeight=this.preview.height,this.defaultWidth=this.preview.width,this.defaultHeight>this.defaultWidth?this.wrap.style.height=this.maxSize+"px":this.wrap.style.width=this.maxSize+"px"})),this.preview.addEventListener("error",(t=>{this.preview.src=this.getNoURL("⚠")})),this.apply.addEventListener("click",(()=>{this.apply.style.display="none",this.reset(),this.preview.style.opacity=.6,this.preview.src=this.url})),this.wrap},reset(){this.preview.src=this.getNoURL()},setSrc(t,e=!1){this.preview.style.opacity=.6,e?(this.apply.style.display="none",this.preview.src=t):(this.apply.style.display="block",this.url=t)},getNoURL(t="︎"){const e=this.defaultWidth/2-23,r=this.defaultHeight/2+25;return`data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="${this.defaultWidth}" height="${this.defaultHeight}"><style>.error { font: normal 50px sans-serif; fill:rgb(255,0,0); }</style><rect width="100%" height="100%" style="fill:rgba(0,0,0,0.2);"></rect><text x="${e}" y="${r}" class="error">${t}</text></svg>`}};function I(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if({}.hasOwnProperty.call(t,n)){if(e.includes(n))continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],e.includes(r)||{}.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function F(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function T(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?F(Object(r),!0).forEach((function(e){u(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):F(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}var C=function(t){var e=function t(e,r){var n=e.headers,i=void 0===n?{}:n;for(var o in i)if("x-wp-nonce"===o.toLowerCase()&&i[o]===t.nonce)return r(e);return r(T(T({},e),{},{headers:T(T({},i),{},{"X-WP-Nonce":t.nonce})}))};return e.nonce=t,e};function N(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function M(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?N(Object(r),!0).forEach((function(e){u(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):N(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}var R=function(t,e){var r,n,i=t.path;return"string"==typeof t.namespace&&"string"==typeof t.endpoint&&(r=t.namespace.replace(/^\/|\/$/g,""),i=(n=t.endpoint.replace(/^\//,""))?r+"/"+n:r),delete t.namespace,delete t.endpoint,e(M(M({},t),{},{path:i}))};function z(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function U(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?z(Object(r),!0).forEach((function(e){u(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):z(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}var $=function(t){return function(e,r){return R(e,(function(e){var n,i=e.url,o=e.path;return"string"==typeof o&&(n=t,-1!==t.indexOf("?")&&(o=o.replace("?","&")),o=o.replace(/^\//,""),"string"==typeof n&&-1!==n.indexOf("?")&&(o=o.replace("?","&")),i=n+o),r(U(U({},e),{},{url:i}))}))}};function B(t){var e=t.split("?"),r=e[1],n=e[0];return r?n+"?"+r.split("&").map((function(t){return t.split("=")})).sort((function(t,e){return t[0].localeCompare(e[0])})).map((function(t){return t.join("=")})).join("&"):n}var G=function(t){var e=Object.keys(t).reduce((function(e,r){return e[B(r)]=t[r],e}),{});return function(t,r){var n=t.parse,i=void 0===n||n;if("string"==typeof t.path){var o=t.method||"GET",a=B(t.path);if("GET"===o&&e[a]){var s=e[a];return delete e[a],Promise.resolve(i?s.body:new window.Response(JSON.stringify(s.body),{status:200,statusText:"OK",headers:s.headers}))}if("OPTIONS"===o&&e[o]&&e[o][a])return Promise.resolve(e[o][a])}return r(t)}};function H(t,e,r,n,i,o,a){try{var s=t[o](a),c=s.value}catch(t){return void r(t)}s.done?e(c):Promise.resolve(c).then(n,i)}var W=r(756),J=r.n(W);function X(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function K(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,s=[],c=!0,u=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=o.call(r)).done)&&(s.push(n.value),s.length!==e);c=!0);}catch(t){u=!0,i=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(u)throw i}}return s}}(t,e)||function(t,e){if(t){if("string"==typeof t)return X(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?X(t,e):void 0}}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Z(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Y(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Z(Object(r),!0).forEach((function(e){u(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Z(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function q(t){return(function(t){var e;try{e=new URL(t,"http://example.com").search.substring(1)}catch(t){}if(e)return e}(t)||"").replace(/\+/g,"%20").split("&").reduce((function(t,e){var r=K(e.split("=").filter(Boolean).map(decodeURIComponent),2),n=r[0],i=r[1],o=void 0===i?"":i;n&&function(t,e,r){for(var n=e.length,i=n-1,o=0;o<n;o++){var a=e[o];!a&&Array.isArray(t)&&(a=t.length.toString());var s=!isNaN(Number(e[o+1]));t[a]=o===i?r:t[a]||(s?[]:{}),Array.isArray(t[a])&&!s&&(t[a]=Y({},t[a])),t=t[a]}}(t,n.replace(/\]/g,"").split("["),o);return t}),{})}function Q(t,e){var r;if("undefined"==typeof Symbol||null==t[Symbol.iterator]){if(Array.isArray(t)||(r=function(t,e){if(!t)return;if("string"==typeof t)return V(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return V(t,e)}(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var n=0,i=function(){};return{s:i,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,s=!1;return{s:function(){r=t[Symbol.iterator]()},n:function(){var t=r.next();return a=t.done,t},e:function(t){s=!0,o=t},f:function(){try{a||null==r.return||r.return()}finally{if(s)throw o}}}}function V(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function tt(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=arguments.length>1?arguments[1]:void 0;if(!e||!Object.keys(e).length)return t;var r=t,n=t.indexOf("?");return-1!==n&&(e=Object.assign(q(t),e),r=r.substr(0,n)),r+"?"+function(t){for(var e,r="",n=Object.entries(t);e=n.shift();){var i=K(e,2),o=i[0],a=i[1];if(Array.isArray(a)||a&&a.constructor===Object){var s,c=Q(Object.entries(a).reverse());try{for(c.s();!(s=c.n()).done;){var u=K(s.value,2),l=u[0],p=u[1];n.unshift(["".concat(o,"[").concat(l,"]"),p])}}catch(t){c.e(t)}finally{c.f()}}else void 0!==a&&(null===a&&(a=""),r+="&"+[o,a].map(encodeURIComponent).join("="))}return r.substr(1)}(e)}function et(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function rt(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?et(Object(r),!0).forEach((function(e){u(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):et(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}var nt=function(t){return t.json?t.json():Promise.reject(t)},it=function(t){return function(t){if(!t)return{};var e=t.match(/<([^>]+)>; rel="next"/);return e?{next:e[1]}:{}}(t.headers.get("link")).next},ot=function(t){var e=!!t.path&&-1!==t.path.indexOf("per_page=-1"),r=!!t.url&&-1!==t.url.indexOf("per_page=-1");return e||r},at=function(){var t,e=(t=J().mark((function t(e,r){var n,i,o,a,s,c;return J().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!1!==e.parse){t.next=2;break}return t.abrupt("return",r(e));case 2:if(ot(e)){t.next=4;break}return t.abrupt("return",r(e));case 4:return t.next=6,Pt(rt(rt({},(l={per_page:100},p=void 0,f=void 0,p=(u=e).path,f=u.url,rt(rt({},I(u,["path","url"])),{},{url:f&&tt(f,l),path:p&&tt(p,l)}))),{},{parse:!1}));case 6:return n=t.sent,t.next=9,nt(n);case 9:if(i=t.sent,Array.isArray(i)){t.next=12;break}return t.abrupt("return",i);case 12:if(o=it(n)){t.next=15;break}return t.abrupt("return",i);case 15:a=[].concat(i);case 16:if(!o){t.next=27;break}return t.next=19,Pt(rt(rt({},e),{},{path:void 0,url:o,parse:!1}));case 19:return s=t.sent,t.next=22,nt(s);case 22:c=t.sent,a=a.concat(c),o=it(s),t.next=16;break;case 27:return t.abrupt("return",a);case 28:case"end":return t.stop()}var u,l,p,f}),t)})),function(){var e=this,r=arguments;return new Promise((function(n,i){var o=t.apply(e,r);function a(t){H(o,n,i,a,s,"next",t)}function s(t){H(o,n,i,a,s,"throw",t)}a(void 0)}))});return function(t,r){return e.apply(this,arguments)}}(),st=at;function ct(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function ut(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ct(Object(r),!0).forEach((function(e){u(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ct(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}var lt=new Set(["PATCH","PUT","DELETE"]);function pt(t,e){return void 0!==function(t,e){return q(t)[e]}(t,e)}var ft=function(t){var e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];return Promise.resolve(function(t){return arguments.length>1&&void 0!==arguments[1]&&!arguments[1]?t:204===t.status?null:t.json?t.json():Promise.reject(t)}(t,e)).catch((function(t){return ht(t,e)}))};function ht(t){if(!(!(arguments.length>1&&void 0!==arguments[1])||arguments[1]))throw t;return function(t){var e={code:"invalid_json",message:L("The response is not a valid JSON response.")};if(!t||!t.json)throw e;return t.json().catch((function(){throw e}))}(t).then((function(t){var e={code:"unknown_error",message:L("An unknown error occurred.")};throw t||e}))}function dt(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function yt(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?dt(Object(r),!0).forEach((function(e){u(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):dt(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}var vt=function(t,e){if(!(t.path&&-1!==t.path.indexOf("/wp/v2/media")||t.url&&-1!==t.url.indexOf("/wp/v2/media")))return e(t);var r=0,n=function t(n){return r++,e({path:"/wp/v2/media/".concat(n,"/post-process"),method:"POST",data:{action:"create-image-subsizes"},parse:!1}).catch((function(){return r<5?t(n):(e({path:"/wp/v2/media/".concat(n,"?force=true"),method:"DELETE"}),Promise.reject())}))};return e(yt(yt({},t),{},{parse:!1})).catch((function(e){var r=e.headers.get("x-wp-upload-attachment-id");return e.status>=500&&e.status<600&&r?n(r).catch((function(){return!1!==t.parse?Promise.reject({code:"post_process",message:L("Media upload failed. If this is a photo or a large image, please scale it down and try again.")}):Promise.reject(e)})):ht(e,t.parse)})).then((function(e){return ft(e,t.parse)}))};function bt(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function gt(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?bt(Object(r),!0).forEach((function(e){u(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):bt(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}var mt={Accept:"application/json, */*;q=0.1"},wt={credentials:"include"},Ot=[function(t,e){return"string"!=typeof t.url||pt(t.url,"_locale")||(t.url=tt(t.url,{_locale:"user"})),"string"!=typeof t.path||pt(t.path,"_locale")||(t.path=tt(t.path,{_locale:"user"})),e(t)},R,function(t,e){var r=t.method,n=void 0===r?"GET":r;return lt.has(n.toUpperCase())&&(t=ut(ut({},t),{},{headers:ut(ut({},t.headers),{},{"X-HTTP-Method-Override":n,"Content-Type":"application/json"}),method:"POST"})),e(t)},st];var jt=function(t){if(t.status>=200&&t.status<300)return t;throw t},xt=function(t){var e=t.url,r=t.path,n=t.data,i=t.parse,o=void 0===i||i,a=I(t,["url","path","data","parse"]),s=t.body,c=t.headers;return c=gt(gt({},mt),c),n&&(s=JSON.stringify(n),c["Content-Type"]="application/json"),window.fetch(e||r||window.location.href,gt(gt(gt({},wt),a),{},{body:s,headers:c})).then((function(t){return Promise.resolve(t).then(jt).catch((function(t){return ht(t,o)})).then((function(t){return ft(t,o)}))}),(function(){throw{code:"fetch_error",message:L("You are probably offline.")}}))};function _t(t){return Ot.reduceRight((function(t,e){return function(r){return e(r,t)}}),xt)(t).catch((function(e){return"rest_cookie_invalid_nonce"!==e.code?Promise.reject(e):window.fetch(_t.nonceEndpoint).then(jt).then((function(t){return t.text()})).then((function(e){return _t.nonceMiddleware.nonce=e,_t(t)}))}))}_t.use=function(t){Ot.unshift(t)},_t.setFetchHandler=function(t){xt=t},_t.createNonceMiddleware=C,_t.createPreloadingMiddleware=G,_t.createRootURLMiddleware=$,_t.fetchAllMiddleware=st,_t.mediaUploadMiddleware=vt;var Pt=_t;var Et={id:null,post_id:null,transformations:null,beforeCallbacks:[],completeCallbacks:[],init(t){if(void 0!==cldData.editor)return Pt.use(Pt.createNonceMiddleware(cldData.editor.nonce)),this.callback=t,this},save(t){this.doBefore(t),Pt({path:cldData.editor.save_url,data:t,method:"POST"}).then((t=>{this.doComplete(t,this)}))},doBefore(t){this.beforeCallbacks.forEach((e=>e(t,this)))},doComplete(t){this.completeCallbacks.forEach((e=>e(t,this)))},onBefore(t){this.beforeCallbacks.push(t)},onComplete(t){this.completeCallbacks.push(t)}};const kt={wrap:document.getElementById("cld-asset-edit"),preview:null,id:null,editor:null,base:null,publicId:null,size:null,transformationsInput:document.getElementById("cld-asset-edit-transformations"),saveButton:document.getElementById("cld-asset-edit-save"),currentURL:null,init(){const t=JSON.parse(this.wrap.dataset.item);this.id=t.ID,this.base=t.base+t.size+"/",this.publicId=t.file,this.transformationsInput.value=t.transformations?t.transformations:"",this.initPreview(),this.initEditor()},initPreview(){this.preview=D.init(),this.wrap.appendChild(this.preview.createPreview(900,675)),this.preview.setSrc(this.base+this.transformationsInput.value+this.publicId,!0),this.transformationsInput.addEventListener("input",(t=>{this.preview.setSrc(this.base+this.transformationsInput.value+this.publicId)})),this.transformationsInput.addEventListener("keydown",(t=>{"Enter"===t.code&&(t.preventDefault(),this.saveButton.dispatchEvent(new Event("click")))}))},initEditor(){this.editor=Et.init(),this.editor.onBefore((()=>this.preview.reset())),this.editor.onComplete((t=>{this.transformationsInput.value=t.transformations,this.preview.setSrc(this.base+t.transformations+this.publicId,!0),t.note&&alert(t.note)})),this.saveButton.addEventListener("click",(()=>{this.editor.save({ID:this.id,transformations:this.transformationsInput.value})}))}};window.addEventListener("load",(()=>kt.init()))}()}();