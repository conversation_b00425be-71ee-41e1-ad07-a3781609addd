!function(){var t={951:function(t,e){var i,n,r,o;o=function(){var t="BKMGTPEZY".split("");function e(t,e){return t&&t.toLowerCase()===e.toLowerCase()}return function(i,n){return i="number"==typeof i?i:0,(n=n||{}).fixed="number"==typeof n.fixed?n.fixed:2,n.spacer="string"==typeof n.spacer?n.spacer:" ",n.calculate=function(t){var r=e(t,"si")?["k","B"]:["K","iB"],o=e(t,"si")?1e3:1024,s=Math.log(i)/Math.log(o)|0,a=i/Math.pow(o,s),l=a.toFixed(n.fixed);return s-1<3&&!e(t,"si")&&e(t,"jedec")&&(r[1]="B"),{suffix:s?(r[0]+"MGTPEZY")[s-1]+r[1]:1==(0|l)?"Byte":"Bytes",magnitude:s,result:a,fixed:l,bits:{result:a/8,fixed:(a/8).toFixed(n.fixed)}}},n.to=function(n,r){var o=e(r,"si")?1e3:1024,s=t.indexOf("string"==typeof n?n[0].toUpperCase():"B"),a=i;if(-1===s||0===s)return a.toFixed(2);for(;s>0;s--)a/=o;return a.toFixed(2)},n.human=function(t){var e=n.calculate(t);return e.fixed+n.spacer+e.suffix},n}},t.exports?t.exports=o():(n=[],void 0===(r="function"==typeof(i=o)?i.apply(e,n):i)||(t.exports=r))},998:function(t,e){var i,n,r;n=[],i=function(){"use strict";function t(t,e){var i,n,r;for(i=1,n=arguments.length;i<n;++i)if(null!=(e=arguments[i]))for(r in e)a(e,r)&&(t[r]=e[r]);return t}function e(t,e){return e.length-t.length}function i(t,e){return t.factor-e.factor}function n(t){return t.replace(/([.*+?=^!:${}()|[\]/\\])/g,"\\$1")}function r(t,e){var i,n;for(i=0,n=t.length;i<n;++i)e(t[i],i)}function o(t,e){var i;for(i in t)a(t,i)&&e(t[i],i)}var s,a=(s=Object.prototype.hasOwnProperty,function(t,e){return null!=t&&s.call(t,e)});function l(t,e){for(;"string"==typeof e;)e=t[e];return e}function c(t){this._prefixes=t;var r=[],s=[];o(t,(function(t,e){r.push(n(e)),s.push({factor:t,prefix:e})}));var l=this._lcPrefixes={};o(t,(function(e,i){var n=i.toLowerCase();a(t,n)||(l[n]=i)})),s.sort(i),this._list=s,r.sort(e),this._regexp=new RegExp("^\\s*(-)?\\s*(\\d+(?:\\.\\d+)?)\\s*("+r.join("|")+")\\s*(.*)\\s*?$","i")}c.create=function(t,e,i){var n={};return void 0===i&&(i=0),r(t,(function(t,r){n[t]=Math.pow(e,r+i)})),new c(n)},c.prototype.findPrefix=function(t){for(var e,i=this._list,n=0,r=i.length-1;n!==r;)i[e=n+r+1>>1].factor>t?r=e-1:n=e;return i[n]},c.prototype.parse=function(t,e){var i=t.match(this._regexp);if(null!==i){var n,r=i[3];if(a(this._prefixes,r))n=this._prefixes[r];else{if(e||(r=r.toLowerCase(),!a(this._lcPrefixes,r)))return;r=this._lcPrefixes[r],n=this._prefixes[r]}var o=+i[2];return void 0!==i[1]&&(o=-o),{factor:n,prefix:r,unit:i[4],value:o}}};var h={binary:c.create(",Ki,Mi,Gi,Ti,Pi,Ei,Zi,Yi".split(","),1024),SI:c.create("y,z,a,f,p,n,µ,m,,k,M,G,T,P,E,Z,Y".split(","),1e3,-8)},u={decimals:2,separator:" ",unit:""},d={scale:"SI",strict:!1};function f(e,i){var n=v(e,i=t({},u,i));e=String(n.value);var r=n.prefix+i.unit;return""===r?e:e+i.separator+r}var p={scale:"binary",unit:"B"};function g(e,i){return f(e,void 0===i?p:t({},p,i))}function m(t,e){var i=b(t,e);return i.value*i.factor}function b(e,i){if("string"!=typeof e)throw new TypeError("str must be a string");i=t({},d,i);var n=l(h,i.scale);if(void 0===n)throw new Error("missing scale");var r=n.parse(e,i.strict);if(void 0===r)throw new Error("cannot parse str");return r}function v(e,i){if(0===e)return{value:0,prefix:""};if(e<0){var n=v(-e,i);return n.value=-n.value,n}if("number"!=typeof e||Number.isNaN(e))throw new TypeError("value must be a number");i=t({},d,i);var r,o=l(h,i.scale);if(void 0===o)throw new Error("missing scale");var s=i.decimals;void 0!==s&&(r=Math.pow(10,s));var c,u=i.prefix;if(void 0!==u){if(!a(o._prefixes,u))throw new Error("invalid prefix");c=o._prefixes[u]}else{var f=o.findPrefix(e);if(void 0!==r)do{var p=(c=f.factor)/r;e=Math.round(e/p)*p}while((f=o.findPrefix(e)).factor!==c);else c=f.factor;u=f.prefix}return{prefix:u,value:void 0===r?e/c:Math.round(e*r/c)/r}}return f.bytes=g,f.parse=m,m.raw=b,f.raw=v,f.Scale=c,f},void 0===(r="function"==typeof i?i.apply(e,n):i)||(t.exports=r)},617:function(){!function(t,e){"use strict";var i,n,r={rootMargin:"256px 0px",threshold:.01,lazyImage:'img[loading="lazy"]',lazyIframe:'iframe[loading="lazy"]'},o={loading:"loading"in HTMLImageElement.prototype&&"loading"in HTMLIFrameElement.prototype,scrolling:"onscroll"in window};function s(t){var e,i,n=[];"picture"===t.parentNode.tagName.toLowerCase()&&((i=(e=t.parentNode).querySelector("source[data-lazy-remove]"))&&e.removeChild(i),n=Array.prototype.slice.call(t.parentNode.querySelectorAll("source"))),n.push(t),n.forEach((function(t){t.hasAttribute("data-lazy-srcset")&&(t.setAttribute("srcset",t.getAttribute("data-lazy-srcset")),t.removeAttribute("data-lazy-srcset"))})),t.setAttribute("src",t.getAttribute("data-lazy-src")),t.removeAttribute("data-lazy-src")}function a(t){var e=document.createElement("div");for(e.innerHTML=function(t){var e=t.textContent||t.innerHTML,n="data:image/svg+xml,%3Csvg xmlns=%27http://www.w3.org/2000/svg%27 viewBox=%270 0 "+((e.match(/width=['"](\d+)['"]/)||!1)[1]||1)+" "+((e.match(/height=['"](\d+)['"]/)||!1)[1]||1)+"%27%3E%3C/svg%3E";return!o.loading&&o.scrolling&&(void 0===i?e=e.replace(/(?:\r\n|\r|\n|\t| )src=/g,' lazyload="1" src='):("picture"===t.parentNode.tagName.toLowerCase()&&(e='<source srcset="'+n+'" data-lazy-remove="true"></source>'+e),e=e.replace(/(?:\r\n|\r|\n|\t| )srcset=/g," data-lazy-srcset=").replace(/(?:\r\n|\r|\n|\t| )src=/g,' src="'+n+'" data-lazy-src='))),e}(t);e.firstChild;)o.loading||!o.scrolling||void 0===i||!e.firstChild.tagName||"img"!==e.firstChild.tagName.toLowerCase()&&"iframe"!==e.firstChild.tagName.toLowerCase()||i.observe(e.firstChild),t.parentNode.insertBefore(e.firstChild,t);t.parentNode.removeChild(t)}function l(){document.querySelectorAll("noscript.loading-lazy").forEach(a),void 0!==window.matchMedia&&window.matchMedia("print").addListener((function(t){t.matches&&document.querySelectorAll(r.lazyImage+"[data-lazy-src],"+r.lazyIframe+"[data-lazy-src]").forEach((function(t){s(t)}))}))}"undefined"!=typeof NodeList&&NodeList.prototype&&!NodeList.prototype.forEach&&(NodeList.prototype.forEach=Array.prototype.forEach),"IntersectionObserver"in window&&(i=new IntersectionObserver((function(t,e){t.forEach((function(t){if(0!==t.intersectionRatio){var i=t.target;e.unobserve(i),s(i)}}))}),r)),n="requestAnimationFrame"in window?window.requestAnimationFrame:function(t){t()},/comp|inter/.test(document.readyState)?n(l):"addEventListener"in document?document.addEventListener("DOMContentLoaded",(function(){n(l)})):document.attachEvent("onreadystatechange",(function(){"complete"===document.readyState&&l()}))}()},616:function(t){t.exports=function(t,e){var i,n,r=0;function o(){var o,s,a=i,l=arguments.length;t:for(;a;){if(a.args.length===arguments.length){for(s=0;s<l;s++)if(a.args[s]!==arguments[s]){a=a.next;continue t}return a!==i&&(a===n&&(n=a.prev),a.prev.next=a.next,a.next&&(a.next.prev=a.prev),a.next=i,a.prev=null,i.prev=a,i=a),a.val}a=a.next}for(o=new Array(l),s=0;s<l;s++)o[s]=arguments[s];return a={args:o,val:t.apply(null,o)},i?(i.prev=a,a.next=i):n=a,r===e.maxSize?(n=n.prev).next=null:r++,i=a,a.val}return e=e||{},o.clear=function(){i=null,n=null,r=0},o}},941:function(t,e,i){var n;t.exports=function(){function t(e,i,n){function r(s,a){if(!i[s]){if(!e[s]){if(o)return o(s,!0);var l=new Error("Cannot find module '"+s+"'");throw l.code="MODULE_NOT_FOUND",l}var c=i[s]={exports:{}};e[s][0].call(c.exports,(function(t){return r(e[s][1][t]||t)}),c,c.exports,t,e,i,n)}return i[s].exports}for(var o=void 0,s=0;s<n.length;s++)r(n[s]);return r}return t}()({1:[function(t,e,n){(function(t){(function(){var i=200,r="__lodash_hash_undefined__",o=800,s=16,a=9007199254740991,l="[object Arguments]",c="[object Array]",h="[object AsyncFunction]",u="[object Boolean]",d="[object Date]",f="[object Error]",p="[object Function]",g="[object GeneratorFunction]",m="[object Map]",b="[object Number]",v="[object Null]",y="[object Object]",x="[object Proxy]",_="[object RegExp]",w="[object Set]",k="[object String]",O="[object Undefined]",S="[object WeakMap]",M="[object ArrayBuffer]",E="[object DataView]",P="[object Float64Array]",T="[object Int8Array]",L="[object Int16Array]",A="[object Int32Array]",C="[object Uint8Array]",D="[object Uint8ClampedArray]",j="[object Uint16Array]",I="[object Uint32Array]",R=/[\\^$.*+?()[\]{}|]/g,F=/^\[object .+?Constructor\]$/,z=/^(?:0|[1-9]\d*)$/,N={};N["[object Float32Array]"]=N[P]=N[T]=N[L]=N[A]=N[C]=N[D]=N[j]=N[I]=!0,N[l]=N[c]=N[M]=N[u]=N[E]=N[d]=N[f]=N[p]=N[m]=N[b]=N[y]=N[_]=N[w]=N[k]=N[S]=!1;var B="object"==typeof t&&t&&t.Object===Object&&t,W="object"==typeof self&&self&&self.Object===Object&&self,V=B||W||Function("return this")(),H="object"==typeof n&&n&&!n.nodeType&&n,$=H&&"object"==typeof e&&e&&!e.nodeType&&e,U=$&&$.exports===H,q=U&&B.process,Y=function(){try{var t=$&&$.require&&$.require("util").types;return t||q&&q.binding&&q.binding("util")}catch(t){}}(),X=Y&&Y.isTypedArray;function G(t,e,i){switch(i.length){case 0:return t.call(e);case 1:return t.call(e,i[0]);case 2:return t.call(e,i[0],i[1]);case 3:return t.call(e,i[0],i[1],i[2])}return t.apply(e,i)}function K(t,e){for(var i=-1,n=Array(t);++i<t;)n[i]=e(i);return n}function J(t){return function(e){return t(e)}}function Q(t,e){return null==t?void 0:t[e]}function Z(t,e){return function(i){return t(e(i))}}var tt,et=Array.prototype,it=Function.prototype,nt=Object.prototype,rt=V["__core-js_shared__"],ot=it.toString,st=nt.hasOwnProperty,at=(tt=/[^.]+$/.exec(rt&&rt.keys&&rt.keys.IE_PROTO||""))?"Symbol(src)_1."+tt:"",lt=nt.toString,ct=ot.call(Object),ht=RegExp("^"+ot.call(st).replace(R,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),ut=U?V.Buffer:void 0,dt=V.Symbol,ft=V.Uint8Array,pt=ut?ut.allocUnsafe:void 0,gt=Z(Object.getPrototypeOf,Object),mt=Object.create,bt=nt.propertyIsEnumerable,vt=et.splice,yt=dt?dt.toStringTag:void 0,xt=function(){try{var t=ye(Object,"defineProperty");return t({},"",{}),t}catch(t){}}(),_t=ut?ut.isBuffer:void 0,wt=Math.max,kt=Date.now,Ot=ye(V,"Map"),St=ye(Object,"create"),Mt=function(){function t(){}return function(e){if(!Ve(e))return{};if(mt)return mt(e);t.prototype=e;var i=new t;return t.prototype=void 0,i}}();function Et(t){var e=-1,i=null==t?0:t.length;for(this.clear();++e<i;){var n=t[e];this.set(n[0],n[1])}}function Pt(){this.__data__=St?St(null):{},this.size=0}function Tt(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=e?1:0,e}function Lt(t){var e=this.__data__;if(St){var i=e[t];return i===r?void 0:i}return st.call(e,t)?e[t]:void 0}function At(t){var e=this.__data__;return St?void 0!==e[t]:st.call(e,t)}function Ct(t,e){var i=this.__data__;return this.size+=this.has(t)?0:1,i[t]=St&&void 0===e?r:e,this}function Dt(t){var e=-1,i=null==t?0:t.length;for(this.clear();++e<i;){var n=t[e];this.set(n[0],n[1])}}function jt(){this.__data__=[],this.size=0}function It(t){var e=this.__data__,i=te(e,t);return!(i<0||(i==e.length-1?e.pop():vt.call(e,i,1),--this.size,0))}function Rt(t){var e=this.__data__,i=te(e,t);return i<0?void 0:e[i][1]}function Ft(t){return te(this.__data__,t)>-1}function zt(t,e){var i=this.__data__,n=te(i,t);return n<0?(++this.size,i.push([t,e])):i[n][1]=e,this}function Nt(t){var e=-1,i=null==t?0:t.length;for(this.clear();++e<i;){var n=t[e];this.set(n[0],n[1])}}function Bt(){this.size=0,this.__data__={hash:new Et,map:new(Ot||Dt),string:new Et}}function Wt(t){var e=ve(this,t).delete(t);return this.size-=e?1:0,e}function Vt(t){return ve(this,t).get(t)}function Ht(t){return ve(this,t).has(t)}function $t(t,e){var i=ve(this,t),n=i.size;return i.set(t,e),this.size+=i.size==n?0:1,this}function Ut(t){var e=this.__data__=new Dt(t);this.size=e.size}function qt(){this.__data__=new Dt,this.size=0}function Yt(t){var e=this.__data__,i=e.delete(t);return this.size=e.size,i}function Xt(t){return this.__data__.get(t)}function Gt(t){return this.__data__.has(t)}function Kt(t,e){var n=this.__data__;if(n instanceof Dt){var r=n.__data__;if(!Ot||r.length<i-1)return r.push([t,e]),this.size=++n.size,this;n=this.__data__=new Nt(r)}return n.set(t,e),this.size=n.size,this}function Jt(t,e){var i=Re(t),n=!i&&Ie(t),r=!i&&!n&&Ne(t),o=!i&&!n&&!r&&Ue(t),s=i||n||r||o,a=s?K(t.length,String):[],l=a.length;for(var c in t)!e&&!st.call(t,c)||s&&("length"==c||r&&("offset"==c||"parent"==c)||o&&("buffer"==c||"byteLength"==c||"byteOffset"==c)||we(c,l))||a.push(c);return a}function Qt(t,e,i){(void 0!==i&&!je(t[e],i)||void 0===i&&!(e in t))&&ee(t,e,i)}function Zt(t,e,i){var n=t[e];st.call(t,e)&&je(n,i)&&(void 0!==i||e in t)||ee(t,e,i)}function te(t,e){for(var i=t.length;i--;)if(je(t[i][0],e))return i;return-1}function ee(t,e,i){"__proto__"==e&&xt?xt(t,e,{configurable:!0,enumerable:!0,value:i,writable:!0}):t[e]=i}Et.prototype.clear=Pt,Et.prototype.delete=Tt,Et.prototype.get=Lt,Et.prototype.has=At,Et.prototype.set=Ct,Dt.prototype.clear=jt,Dt.prototype.delete=It,Dt.prototype.get=Rt,Dt.prototype.has=Ft,Dt.prototype.set=zt,Nt.prototype.clear=Bt,Nt.prototype.delete=Wt,Nt.prototype.get=Vt,Nt.prototype.has=Ht,Nt.prototype.set=$t,Ut.prototype.clear=qt,Ut.prototype.delete=Yt,Ut.prototype.get=Xt,Ut.prototype.has=Gt,Ut.prototype.set=Kt;var ie=be();function ne(t){return null==t?void 0===t?O:v:yt&&yt in Object(t)?xe(t):Pe(t)}function re(t){return He(t)&&ne(t)==l}function oe(t){return!(!Ve(t)||Se(t))&&(Be(t)?ht:F).test(De(t))}function se(t){return He(t)&&We(t.length)&&!!N[ne(t)]}function ae(t){if(!Ve(t))return Ee(t);var e=Me(t),i=[];for(var n in t)("constructor"!=n||!e&&st.call(t,n))&&i.push(n);return i}function le(t,e,i,n,r){t!==e&&ie(e,(function(o,s){if(r||(r=new Ut),Ve(o))ce(t,e,s,i,le,n,r);else{var a=n?n(Le(t,s),o,s+"",t,e,r):void 0;void 0===a&&(a=o),Qt(t,s,a)}}),Ye)}function ce(t,e,i,n,r,o,s){var a=Le(t,i),l=Le(e,i),c=s.get(l);if(c)Qt(t,i,c);else{var h=o?o(a,l,i+"",t,e,s):void 0,u=void 0===h;if(u){var d=Re(l),f=!d&&Ne(l),p=!d&&!f&&Ue(l);h=l,d||f||p?Re(a)?h=a:ze(a)?h=pe(a):f?(u=!1,h=ue(l,!0)):p?(u=!1,h=fe(l,!0)):h=[]:$e(l)||Ie(l)?(h=a,Ie(a)?h=qe(a):Ve(a)&&!Be(a)||(h=_e(l))):u=!1}u&&(s.set(l,h),r(h,l,n,o,s),s.delete(l)),Qt(t,i,h)}}function he(t,e){return Ae(Te(t,e,Ke),t+"")}function ue(t,e){if(e)return t.slice();var i=t.length,n=pt?pt(i):new t.constructor(i);return t.copy(n),n}function de(t){var e=new t.constructor(t.byteLength);return new ft(e).set(new ft(t)),e}function fe(t,e){var i=e?de(t.buffer):t.buffer;return new t.constructor(i,t.byteOffset,t.length)}function pe(t,e){var i=-1,n=t.length;for(e||(e=Array(n));++i<n;)e[i]=t[i];return e}function ge(t,e,i,n){var r=!i;i||(i={});for(var o=-1,s=e.length;++o<s;){var a=e[o],l=n?n(i[a],t[a],a,i,t):void 0;void 0===l&&(l=t[a]),r?ee(i,a,l):Zt(i,a,l)}return i}function me(t){return he((function(e,i){var n=-1,r=i.length,o=r>1?i[r-1]:void 0,s=r>2?i[2]:void 0;for(o=t.length>3&&"function"==typeof o?(r--,o):void 0,s&&ke(i[0],i[1],s)&&(o=r<3?void 0:o,r=1),e=Object(e);++n<r;){var a=i[n];a&&t(e,a,n,o)}return e}))}function be(t){return function(e,i,n){for(var r=-1,o=Object(e),s=n(e),a=s.length;a--;){var l=s[t?a:++r];if(!1===i(o[l],l,o))break}return e}}function ve(t,e){var i=t.__data__;return Oe(e)?i["string"==typeof e?"string":"hash"]:i.map}function ye(t,e){var i=Q(t,e);return oe(i)?i:void 0}function xe(t){var e=st.call(t,yt),i=t[yt];try{t[yt]=void 0;var n=!0}catch(t){}var r=lt.call(t);return n&&(e?t[yt]=i:delete t[yt]),r}function _e(t){return"function"!=typeof t.constructor||Me(t)?{}:Mt(gt(t))}function we(t,e){var i=typeof t;return!!(e=null==e?a:e)&&("number"==i||"symbol"!=i&&z.test(t))&&t>-1&&t%1==0&&t<e}function ke(t,e,i){if(!Ve(i))return!1;var n=typeof e;return!!("number"==n?Fe(i)&&we(e,i.length):"string"==n&&e in i)&&je(i[e],t)}function Oe(t){var e=typeof t;return"string"==e||"number"==e||"symbol"==e||"boolean"==e?"__proto__"!==t:null===t}function Se(t){return!!at&&at in t}function Me(t){var e=t&&t.constructor;return t===("function"==typeof e&&e.prototype||nt)}function Ee(t){var e=[];if(null!=t)for(var i in Object(t))e.push(i);return e}function Pe(t){return lt.call(t)}function Te(t,e,i){return e=wt(void 0===e?t.length-1:e,0),function(){for(var n=arguments,r=-1,o=wt(n.length-e,0),s=Array(o);++r<o;)s[r]=n[e+r];r=-1;for(var a=Array(e+1);++r<e;)a[r]=n[r];return a[e]=i(s),G(t,this,a)}}function Le(t,e){if(("constructor"!==e||"function"!=typeof t[e])&&"__proto__"!=e)return t[e]}var Ae=Ce(xt?function(t,e){return xt(t,"toString",{configurable:!0,enumerable:!1,value:Ge(e),writable:!0})}:Ke);function Ce(t){var e=0,i=0;return function(){var n=kt(),r=s-(n-i);if(i=n,r>0){if(++e>=o)return arguments[0]}else e=0;return t.apply(void 0,arguments)}}function De(t){if(null!=t){try{return ot.call(t)}catch(t){}try{return t+""}catch(t){}}return""}function je(t,e){return t===e||t!=t&&e!=e}var Ie=re(function(){return arguments}())?re:function(t){return He(t)&&st.call(t,"callee")&&!bt.call(t,"callee")},Re=Array.isArray;function Fe(t){return null!=t&&We(t.length)&&!Be(t)}function ze(t){return He(t)&&Fe(t)}var Ne=_t||Je;function Be(t){if(!Ve(t))return!1;var e=ne(t);return e==p||e==g||e==h||e==x}function We(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=a}function Ve(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)}function He(t){return null!=t&&"object"==typeof t}function $e(t){if(!He(t)||ne(t)!=y)return!1;var e=gt(t);if(null===e)return!0;var i=st.call(e,"constructor")&&e.constructor;return"function"==typeof i&&i instanceof i&&ot.call(i)==ct}var Ue=X?J(X):se;function qe(t){return ge(t,Ye(t))}function Ye(t){return Fe(t)?Jt(t,!0):ae(t)}var Xe=me((function(t,e,i){le(t,e,i)}));function Ge(t){return function(){return t}}function Ke(t){return t}function Je(){return!1}e.exports=Xe}).call(this)}).call(this,void 0!==i.g?i.g:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],2:[function(t,e,i){var r,o;r=self,o=function(){return function(){"use strict";var t={720:function(t,e,i){i.r(e),i.d(e,{Scene:function(){return ae},Tweenable:function(){return St},interpolate:function(){return ee},processTweens:function(){return bt},setBezierFunction:function(){return H},shouldScheduleUpdate:function(){return xt},tween:function(){return Mt},unsetBezierFunction:function(){return $}});var n={};i.r(n),i.d(n,{bounce:function(){return I},bouncePast:function(){return R},easeFrom:function(){return z},easeFromTo:function(){return F},easeInBack:function(){return P},easeInCirc:function(){return O},easeInCubic:function(){return c},easeInExpo:function(){return _},easeInOutBack:function(){return L},easeInOutCirc:function(){return M},easeInOutCubic:function(){return u},easeInOutExpo:function(){return k},easeInOutQuad:function(){return l},easeInOutQuart:function(){return p},easeInOutQuint:function(){return b},easeInOutSine:function(){return x},easeInQuad:function(){return s},easeInQuart:function(){return d},easeInQuint:function(){return g},easeInSine:function(){return v},easeOutBack:function(){return T},easeOutBounce:function(){return E},easeOutCirc:function(){return S},easeOutCubic:function(){return h},easeOutExpo:function(){return w},easeOutQuad:function(){return a},easeOutQuart:function(){return f},easeOutQuint:function(){return m},easeOutSine:function(){return y},easeTo:function(){return N},elastic:function(){return A},linear:function(){return o},swingFrom:function(){return D},swingFromTo:function(){return C},swingTo:function(){return j}});var r={};i.r(r),i.d(r,{afterTween:function(){return Gt},beforeTween:function(){return Xt},doesApply:function(){return qt},tweenCreated:function(){return Yt}});var o=function(t){return t},s=function(t){return Math.pow(t,2)},a=function(t){return-(Math.pow(t-1,2)-1)},l=function(t){return(t/=.5)<1?.5*Math.pow(t,2):-.5*((t-=2)*t-2)},c=function(t){return Math.pow(t,3)},h=function(t){return Math.pow(t-1,3)+1},u=function(t){return(t/=.5)<1?.5*Math.pow(t,3):.5*(Math.pow(t-2,3)+2)},d=function(t){return Math.pow(t,4)},f=function(t){return-(Math.pow(t-1,4)-1)},p=function(t){return(t/=.5)<1?.5*Math.pow(t,4):-.5*((t-=2)*Math.pow(t,3)-2)},g=function(t){return Math.pow(t,5)},m=function(t){return Math.pow(t-1,5)+1},b=function(t){return(t/=.5)<1?.5*Math.pow(t,5):.5*(Math.pow(t-2,5)+2)},v=function(t){return 1-Math.cos(t*(Math.PI/2))},y=function(t){return Math.sin(t*(Math.PI/2))},x=function(t){return-.5*(Math.cos(Math.PI*t)-1)},_=function(t){return 0===t?0:Math.pow(2,10*(t-1))},w=function(t){return 1===t?1:1-Math.pow(2,-10*t)},k=function(t){return 0===t?0:1===t?1:(t/=.5)<1?.5*Math.pow(2,10*(t-1)):.5*(2-Math.pow(2,-10*--t))},O=function(t){return-(Math.sqrt(1-t*t)-1)},S=function(t){return Math.sqrt(1-Math.pow(t-1,2))},M=function(t){return(t/=.5)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1)},E=function(t){return t<1/2.75?7.5625*t*t:t<2/2.75?7.5625*(t-=1.5/2.75)*t+.75:t<2.5/2.75?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375},P=function(t){var e=1.70158;return t*t*((e+1)*t-e)},T=function(t){var e=1.70158;return(t-=1)*t*((e+1)*t+e)+1},L=function(t){var e=1.70158;return(t/=.5)<1?t*t*((1+(e*=1.525))*t-e)*.5:.5*((t-=2)*t*((1+(e*=1.525))*t+e)+2)},A=function(t){return-1*Math.pow(4,-8*t)*Math.sin((6*t-1)*(2*Math.PI)/2)+1},C=function(t){var e=1.70158;return(t/=.5)<1?t*t*((1+(e*=1.525))*t-e)*.5:.5*((t-=2)*t*((1+(e*=1.525))*t+e)+2)},D=function(t){var e=1.70158;return t*t*((e+1)*t-e)},j=function(t){var e=1.70158;return(t-=1)*t*((e+1)*t+e)+1},I=function(t){return t<1/2.75?7.5625*t*t:t<2/2.75?7.5625*(t-=1.5/2.75)*t+.75:t<2.5/2.75?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375},R=function(t){return t<1/2.75?7.5625*t*t:t<2/2.75?2-(7.5625*(t-=1.5/2.75)*t+.75):t<2.5/2.75?2-(7.5625*(t-=2.25/2.75)*t+.9375):2-(7.5625*(t-=2.625/2.75)*t+.984375)},F=function(t){return(t/=.5)<1?.5*Math.pow(t,4):-.5*((t-=2)*Math.pow(t,3)-2)},z=function(t){return Math.pow(t,4)},N=function(t){return Math.pow(t,.25)};function B(t,e,i,n,r,o){var s,a,l,c,h,u=0,d=0,f=0,p=function(t){return((u*t+d)*t+f)*t},g=function(t){return(3*u*t+2*d)*t+f},m=function(t){return t>=0?t:0-t};return u=1-(f=3*e)-(d=3*(n-e)-f),l=1-(h=3*i)-(c=3*(r-i)-h),s=t,a=function(t){return 1/(200*t)}(o),function(t){return((l*t+c)*t+h)*t}(function(t,e){var i,n,r,o,s,a;for(r=t,a=0;a<8;a++){if(o=p(r)-t,m(o)<e)return r;if(s=g(r),m(s)<1e-6)break;r-=o/s}if((r=t)<(i=0))return i;if(r>(n=1))return n;for(;i<n;){if(o=p(r),m(o-t)<e)return r;t>o?i=r:n=r,r=.5*(n-i)+i}return r}(s,a))}var W,V=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:.25,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:.25,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:.75,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:.75;return function(r){return B(r,t,e,i,n,1)}},H=function(t,e,i,n,r){var o=V(e,i,n,r);return o.displayName=t,o.x1=e,o.y1=i,o.x2=n,o.y2=r,St.formulas[t]=o},$=function(t){return delete St.formulas[t]};function U(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function q(t,e){for(var i=0;i<e.length;i++){var n=e[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function Y(t){return Y="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Y(t)}function X(t){return function(t){if(Array.isArray(t))return G(t)}(t)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return G(t,e);var i=Object.prototype.toString.call(t).slice(8,-1);return"Object"===i&&t.constructor&&(i=t.constructor.name),"Map"===i||"Set"===i?Array.from(t):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?G(t,e):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function G(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,n=new Array(e);i<e;i++)n[i]=t[i];return n}function K(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,n)}return i}function J(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?K(Object(i),!0).forEach((function(e){Q(t,e,i[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):K(Object(i)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}function Q(t,e,i){return e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}var Z,tt,et,it="linear",nt="undefined"!=typeof window?window:i.g,rt="afterTween",ot="afterTweenEnd",st="beforeTween",at="tweenCreated",lt="function",ct="string",ht=nt.requestAnimationFrame||nt.webkitRequestAnimationFrame||nt.oRequestAnimationFrame||nt.msRequestAnimationFrame||nt.mozCancelRequestAnimationFrame&&nt.mozRequestAnimationFrame||setTimeout,ut=function(){},dt=null,ft=null,pt=J({},n),gt=function(t,e,i,n,r,o,s){var a,l,c,h=t<o?0:(t-o)/r,u=!1;for(var d in s&&s.call&&(u=!0,a=s(h)),e)u||(a=((l=s[d]).call?l:pt[l])(h)),c=i[d],e[d]=c+(n[d]-c)*a;return e},mt=function(t,e){var i=t._timestamp,n=t._currentState,r=t._delay;if(!(e<i+r)){var o=t._duration,s=t._targetState,a=i+r+o,l=e>a?a:e;t._hasEnded=l>=a;var c=o-(a-l),h=t._filters.length>0;if(t._hasEnded)return t._render(s,t._data,c),t.stop(!0);h&&t._applyFilter(st),l<i+r?i=o=l=1:i+=r,gt(l,n,t._originalState,s,o,i,t._easing),h&&t._applyFilter(rt),t._render(n,t._data,c)}},bt=function(){for(var t,e=St.now(),i=dt;i;)t=i._next,mt(i,e),i=t},vt=Date.now||function(){return+new Date},yt=!1,xt=function(t){t&&yt||(yt=t,t&&_t())},_t=function t(){Z=vt(),yt&&ht.call(nt,t,16.666666666666668),bt()},wt=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:it,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(Array.isArray(e))return V.apply(void 0,X(e));var n=Y(e);if(pt[e])return pt[e];if(n===ct||n===lt)for(var r in t)i[r]=e;else for(var o in t)i[o]=e[o]||it;return i},kt=function(t){t===dt?(dt=t._next)?dt._previous=null:ft=null:t===ft?(ft=t._previous)?ft._next=null:dt=null:(tt=t._previous,et=t._next,tt._next=et,et._previous=tt),t._previous=t._next=null},Ot="function"==typeof Promise?Promise:null;W=Symbol.toStringTag;var St=function(){function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0;U(this,t),Q(this,W,"Promise"),this._config={},this._data={},this._delay=0,this._filters=[],this._next=null,this._previous=null,this._timestamp=null,this._hasEnded=!1,this._resolve=null,this._reject=null,this._currentState=e||{},this._originalState={},this._targetState={},this._start=ut,this._render=ut,this._promiseCtor=Ot,i&&this.setConfig(i)}var e;return e=[{key:"_applyFilter",value:function(t){for(var e=this._filters.length;e>0;e--){var i=this._filters[e-e][t];i&&i(this)}}},{key:"tween",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:void 0;return this._isPlaying&&this.stop(),!e&&this._config||this.setConfig(e),this._pausedAtTime=null,this._timestamp=t.now(),this._start(this.get(),this._data),this._delay&&this._render(this._currentState,this._data,0),this._resume(this._timestamp)}},{key:"setConfig",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},i=this._config;for(var n in e)i[n]=e[n];var r=i.promise,o=void 0===r?this._promiseCtor:r,s=i.start,a=void 0===s?ut:s,l=i.finish,c=i.render,h=void 0===c?this._config.step||ut:c,u=i.step,d=void 0===u?ut:u;this._data=i.data||i.attachment||this._data,this._isPlaying=!1,this._pausedAtTime=null,this._scheduleId=null,this._delay=e.delay||0,this._start=a,this._render=h||d,this._duration=i.duration||500,this._promiseCtor=o,l&&(this._resolve=l);var f=e.from,p=e.to,g=void 0===p?{}:p,m=this._currentState,b=this._originalState,v=this._targetState;for(var y in f)m[y]=f[y];var x=!1;for(var _ in m){var w=m[_];x||Y(w)!==ct||(x=!0),b[_]=w,v[_]=g.hasOwnProperty(_)?g[_]:w}if(this._easing=wt(this._currentState,i.easing,this._easing),this._filters.length=0,x){for(var k in t.filters)t.filters[k].doesApply(this)&&this._filters.push(t.filters[k]);this._applyFilter(at)}return this}},{key:"then",value:function(t,e){var i=this;return this._promise=new this._promiseCtor((function(t,e){i._resolve=t,i._reject=e})),this._promise.then(t,e)}},{key:"catch",value:function(t){return this.then().catch(t)}},{key:"finally",value:function(t){return this.then().finally(t)}},{key:"get",value:function(){return J({},this._currentState)}},{key:"set",value:function(t){this._currentState=t}},{key:"pause",value:function(){if(this._isPlaying)return this._pausedAtTime=t.now(),this._isPlaying=!1,kt(this),this}},{key:"resume",value:function(){return this._resume()}},{key:"_resume",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:t.now();return null===this._timestamp?this.tween():this._isPlaying?this._promise:(this._pausedAtTime&&(this._timestamp+=e-this._pausedAtTime,this._pausedAtTime=null),this._isPlaying=!0,null===dt?(dt=this,ft=this):(this._previous=ft,ft._next=this,ft=this),this)}},{key:"seek",value:function(e){e=Math.max(e,0);var i=t.now();return this._timestamp+e===0||(this._timestamp=i-e,mt(this,i)),this}},{key:"stop",value:function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(!this._isPlaying)return this;this._isPlaying=!1,kt(this);var e=this._filters.length>0;return t&&(e&&this._applyFilter(st),gt(1,this._currentState,this._originalState,this._targetState,1,0,this._easing),e&&(this._applyFilter(rt),this._applyFilter(ot))),this._resolve&&this._resolve({data:this._data,state:this._currentState,tweenable:this}),this._resolve=null,this._reject=null,this}},{key:"cancel",value:function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0],e=this._currentState,i=this._data;return this._isPlaying?(this._reject&&this._reject({data:i,state:e,tweenable:this}),this._resolve=null,this._reject=null,this.stop(t)):this}},{key:"isPlaying",value:function(){return this._isPlaying}},{key:"hasEnded",value:function(){return this._hasEnded}},{key:"setScheduleFunction",value:function(e){t.setScheduleFunction(e)}},{key:"data",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;return t&&(this._data=J({},t)),this._data}},{key:"dispose",value:function(){for(var t in this)delete this[t]}}],e&&q(t.prototype,e),t}();function Mt(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=new St;return e.tween(t),e.tweenable=e,e}Q(St,"now",(function(){return Z})),Q(St,"setScheduleFunction",(function(t){return ht=t})),Q(St,"filters",{}),Q(St,"formulas",pt),xt(!0);var Et,Pt,Tt=/(\d|-|\.)/,Lt=/([^\-0-9.]+)/g,At=/[0-9.-]+/g,Ct=(Et=At.source,Pt=/,\s*/.source,new RegExp("rgba?\\(".concat(Et).concat(Pt).concat(Et).concat(Pt).concat(Et,"(").concat(Pt).concat(Et,")?\\)"),"g")),Dt=/^.*\(/,jt=/#([0-9]|[a-f]){3,6}/gi,It="VAL",Rt=function(t,e){return t.map((function(t,i){return"_".concat(e,"_").concat(i)}))};function Ft(t){return parseInt(t,16)}var zt=function(t){return"rgb(".concat((e=t,3===(e=e.replace(/#/,"")).length&&(e=(e=e.split(""))[0]+e[0]+e[1]+e[1]+e[2]+e[2]),[Ft(e.substr(0,2)),Ft(e.substr(2,2)),Ft(e.substr(4,2))]).join(","),")");var e},Nt=function(t,e,i){var n=e.match(t),r=e.replace(t,It);return n&&n.forEach((function(t){return r=r.replace(It,i(t))})),r},Bt=function(t){for(var e in t){var i=t[e];"string"==typeof i&&i.match(jt)&&(t[e]=Nt(jt,i,zt))}},Wt=function(t){var e=t.match(At),i=e.slice(0,3).map(Math.floor),n=t.match(Dt)[0];if(3===e.length)return"".concat(n).concat(i.join(","),")");if(4===e.length)return"".concat(n).concat(i.join(","),",").concat(e[3],")");throw new Error("Invalid rgbChunk: ".concat(t))},Vt=function(t){return t.match(At)},Ht=function(t,e){var i={};return e.forEach((function(e){i[e]=t[e],delete t[e]})),i},$t=function(t,e){return e.map((function(e){return t[e]}))},Ut=function(t,e){return e.forEach((function(e){return t=t.replace(It,+e.toFixed(4))})),t},qt=function(t){for(var e in t._currentState)if("string"==typeof t._currentState[e])return!0;return!1};function Yt(t){var e=t._currentState;[e,t._originalState,t._targetState].forEach(Bt),t._tokenData=function(t){var e,i,n={};for(var r in t){var o=t[r];"string"==typeof o&&(n[r]={formatString:(e=o,i=void 0,i=e.match(Lt),i?(1===i.length||e.charAt(0).match(Tt))&&i.unshift(""):i=["",""],i.join(It)),chunkNames:Rt(Vt(o),r)})}return n}(e)}function Xt(t){var e=t._currentState,i=t._originalState,n=t._targetState,r=t._easing,o=t._tokenData;!function(t,e){var i=function(i){var n=e[i].chunkNames,r=t[i];if("string"==typeof r){var o=r.split(" "),s=o[o.length-1];n.forEach((function(e,i){return t[e]=o[i]||s}))}else n.forEach((function(e){return t[e]=r}));delete t[i]};for(var n in e)i(n)}(r,o),[e,i,n].forEach((function(t){return function(t,e){var i=function(i){Vt(t[i]).forEach((function(n,r){return t[e[i].chunkNames[r]]=+n})),delete t[i]};for(var n in e)i(n)}(t,o)}))}function Gt(t){var e=t._currentState,i=t._originalState,n=t._targetState,r=t._easing,o=t._tokenData;[e,i,n].forEach((function(t){return function(t,e){for(var i in e){var n=e[i],r=n.chunkNames,o=n.formatString,s=Ut(o,$t(Ht(t,r),r));t[i]=Nt(Ct,s,Wt)}}(t,o)})),function(t,e){for(var i in e){var n=e[i].chunkNames,r=t[n[0]];t[i]="string"==typeof r?n.map((function(e){var i=t[e];return delete t[e],i})).join(" "):r}}(r,o)}function Kt(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,n)}return i}function Jt(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?Kt(Object(i),!0).forEach((function(e){Qt(t,e,i[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):Kt(Object(i)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}function Qt(t,e,i){return e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}var Zt=new St,te=St.filters,ee=function(t,e,i,n){var r=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0,o=Jt({},t),s=wt(t,n);for(var a in Zt._filters.length=0,Zt.set({}),Zt._currentState=o,Zt._originalState=t,Zt._targetState=e,Zt._easing=s,te)te[a].doesApply(Zt)&&Zt._filters.push(te[a]);Zt._applyFilter("tweenCreated"),Zt._applyFilter("beforeTween");var l=gt(i,o,t,e,1,r,s);return Zt._applyFilter("afterTween"),l};function ie(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,n=new Array(e);i<e;i++)n[i]=t[i];return n}function ne(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function re(t,e){for(var i=0;i<e.length;i++){var n=e[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function oe(t,e){var i=e.get(t);if(!i)throw new TypeError("attempted to get private field on non-instance");return i.get?i.get.call(t):i.value}var se=new WeakMap,ae=function(){function t(){ne(this,t),se.set(this,{writable:!0,value:[]});for(var e=arguments.length,i=new Array(e),n=0;n<e;n++)i[n]=arguments[n];i.forEach(this.add.bind(this))}var e;return(e=[{key:"add",value:function(t){return oe(this,se).push(t),t}},{key:"remove",value:function(t){var e=oe(this,se).indexOf(t);return~e&&oe(this,se).splice(e,1),t}},{key:"empty",value:function(){return this.tweenables.map(this.remove.bind(this))}},{key:"isPlaying",value:function(){return oe(this,se).some((function(t){return t.isPlaying()}))}},{key:"play",value:function(){return oe(this,se).forEach((function(t){return t.tween()})),this}},{key:"pause",value:function(){return oe(this,se).forEach((function(t){return t.pause()})),this}},{key:"resume",value:function(){return this.playingTweenables.forEach((function(t){return t.resume()})),this}},{key:"stop",value:function(t){return oe(this,se).forEach((function(e){return e.stop(t)})),this}},{key:"tweenables",get:function(){return function(t){if(Array.isArray(t))return ie(t)}(t=oe(this,se))||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return ie(t,e);var i=Object.prototype.toString.call(t).slice(8,-1);return"Object"===i&&t.constructor&&(i=t.constructor.name),"Map"===i||"Set"===i?Array.from(t):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?ie(t,e):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();var t}},{key:"playingTweenables",get:function(){return oe(this,se).filter((function(t){return!t.hasEnded()}))}},{key:"promises",get:function(){return oe(this,se).map((function(t){return t.then()}))}}])&&re(t.prototype,e),t}();St.filters.token=r}},e={};function i(n){if(e[n])return e[n].exports;var r=e[n]={exports:{}};return t[n](r,r.exports,i),r.exports}return i.d=function(t,e){for(var n in e)i.o(e,n)&&!i.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})},i.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"==typeof window)return window}}(),i.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},i.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},i(720)}()},"object"==typeof i&&"object"==typeof e?e.exports=o():"function"==typeof n&&n.amd?n("shifty",[],o):"object"==typeof i?i.shifty=o():r.shifty=o()},{}],3:[function(t,e,i){var n=t("./shape"),r=t("./utils"),o=function(t,e){this._pathTemplate="M 50,50 m 0,-{radius} a {radius},{radius} 0 1 1 0,{2radius} a {radius},{radius} 0 1 1 0,-{2radius}",this.containerAspectRatio=1,n.apply(this,arguments)};o.prototype=new n,o.prototype.constructor=o,o.prototype._pathString=function(t){var e=t.strokeWidth;t.trailWidth&&t.trailWidth>t.strokeWidth&&(e=t.trailWidth);var i=50-e/2;return r.render(this._pathTemplate,{radius:i,"2radius":2*i})},o.prototype._trailString=function(t){return this._pathString(t)},e.exports=o},{"./shape":8,"./utils":10}],4:[function(t,e,i){var n=t("./shape"),r=t("./utils"),o=function(t,e){this._pathTemplate=e.vertical?"M {center},100 L {center},0":"M 0,{center} L 100,{center}",n.apply(this,arguments)};o.prototype=new n,o.prototype.constructor=o,o.prototype._initializeSvg=function(t,e){var i=e.vertical?"0 0 "+e.strokeWidth+" 100":"0 0 100 "+e.strokeWidth;t.setAttribute("viewBox",i),t.setAttribute("preserveAspectRatio","none")},o.prototype._pathString=function(t){return r.render(this._pathTemplate,{center:t.strokeWidth/2})},o.prototype._trailString=function(t){return this._pathString(t)},e.exports=o},{"./shape":8,"./utils":10}],5:[function(t,e,i){e.exports={Line:t("./line"),Circle:t("./circle"),SemiCircle:t("./semicircle"),Square:t("./square"),Path:t("./path"),Shape:t("./shape"),utils:t("./utils")}},{"./circle":3,"./line":4,"./path":6,"./semicircle":7,"./shape":8,"./square":9,"./utils":10}],6:[function(t,e,i){var n=t("shifty"),r=t("./utils"),o=n.Tweenable,s={easeIn:"easeInCubic",easeOut:"easeOutCubic",easeInOut:"easeInOutCubic"},a=function t(e,i){if(!(this instanceof t))throw new Error("Constructor was called without new keyword");var n;i=r.extend({delay:0,duration:800,easing:"linear",from:{},to:{},step:function(){}},i),n=r.isString(e)?document.querySelector(e):e,this.path=n,this._opts=i,this._tweenable=null;var o=this.path.getTotalLength();this.path.style.strokeDasharray=o+" "+o,this.set(0)};a.prototype.value=function(){var t=this._getComputedDashOffset(),e=this.path.getTotalLength();return parseFloat((1-t/e).toFixed(6),10)},a.prototype.set=function(t){this.stop(),this.path.style.strokeDashoffset=this._progressToOffset(t);var e=this._opts.step;if(r.isFunction(e)){var i=this._easing(this._opts.easing);e(this._calculateTo(t,i),this._opts.shape||this,this._opts.attachment)}},a.prototype.stop=function(){this._stopTween(),this.path.style.strokeDashoffset=this._getComputedDashOffset()},a.prototype.animate=function(t,e,i){e=e||{},r.isFunction(e)&&(i=e,e={});var n=r.extend({},e),s=r.extend({},this._opts);e=r.extend(s,e);var a=this._easing(e.easing),l=this._resolveFromAndTo(t,a,n);this.stop(),this.path.getBoundingClientRect();var c=this._getComputedDashOffset(),h=this._progressToOffset(t),u=this;this._tweenable=new o,this._tweenable.tween({from:r.extend({offset:c},l.from),to:r.extend({offset:h},l.to),duration:e.duration,delay:e.delay,easing:a,step:function(t){u.path.style.strokeDashoffset=t.offset;var i=e.shape||u;e.step(t,i,e.attachment)}}).then((function(t){r.isFunction(i)&&i()})).catch((function(t){throw console.error("Error in tweening:",t),t}))},a.prototype._getComputedDashOffset=function(){var t=window.getComputedStyle(this.path,null);return parseFloat(t.getPropertyValue("stroke-dashoffset"),10)},a.prototype._progressToOffset=function(t){var e=this.path.getTotalLength();return e-t*e},a.prototype._resolveFromAndTo=function(t,e,i){return i.from&&i.to?{from:i.from,to:i.to}:{from:this._calculateFrom(e),to:this._calculateTo(t,e)}},a.prototype._calculateFrom=function(t){return n.interpolate(this._opts.from,this._opts.to,this.value(),t)},a.prototype._calculateTo=function(t,e){return n.interpolate(this._opts.from,this._opts.to,t,e)},a.prototype._stopTween=function(){null!==this._tweenable&&(this._tweenable.stop(!0),this._tweenable=null)},a.prototype._easing=function(t){return s.hasOwnProperty(t)?s[t]:t},e.exports=a},{"./utils":10,shifty:2}],7:[function(t,e,i){var n=t("./shape"),r=t("./circle"),o=t("./utils"),s=function(t,e){this._pathTemplate="M 50,50 m -{radius},0 a {radius},{radius} 0 1 1 {2radius},0",this.containerAspectRatio=2,n.apply(this,arguments)};s.prototype=new n,s.prototype.constructor=s,s.prototype._initializeSvg=function(t,e){t.setAttribute("viewBox","0 0 100 50")},s.prototype._initializeTextContainer=function(t,e,i){t.text.style&&(i.style.top="auto",i.style.bottom="0",t.text.alignToBottom?o.setStyle(i,"transform","translate(-50%, 0)"):o.setStyle(i,"transform","translate(-50%, 50%)"))},s.prototype._pathString=r.prototype._pathString,s.prototype._trailString=r.prototype._trailString,e.exports=s},{"./circle":3,"./shape":8,"./utils":10}],8:[function(t,e,i){var n=t("./path"),r=t("./utils"),o="Object is destroyed",s=function t(e,i){if(!(this instanceof t))throw new Error("Constructor was called without new keyword");if(0!==arguments.length){this._opts=r.extend({color:"#555",strokeWidth:1,trailColor:null,trailWidth:null,fill:null,text:{style:{color:null,position:"absolute",left:"50%",top:"50%",padding:0,margin:0,transform:{prefix:!0,value:"translate(-50%, -50%)"}},autoStyleContainer:!0,alignToBottom:!0,value:null,className:"progressbar-text"},svgStyle:{display:"block",width:"100%"},warnings:!1},i,!0),r.isObject(i)&&void 0!==i.svgStyle&&(this._opts.svgStyle=i.svgStyle),r.isObject(i)&&r.isObject(i.text)&&void 0!==i.text.style&&(this._opts.text.style=i.text.style);var o,s=this._createSvgView(this._opts);if(!(o=r.isString(e)?document.querySelector(e):e))throw new Error("Container does not exist: "+e);this._container=o,this._container.appendChild(s.svg),this._opts.warnings&&this._warnContainerAspectRatio(this._container),this._opts.svgStyle&&r.setStyles(s.svg,this._opts.svgStyle),this.svg=s.svg,this.path=s.path,this.trail=s.trail,this.text=null;var a=r.extend({attachment:void 0,shape:this},this._opts);this._progressPath=new n(s.path,a),r.isObject(this._opts.text)&&null!==this._opts.text.value&&this.setText(this._opts.text.value)}};s.prototype.animate=function(t,e,i){if(null===this._progressPath)throw new Error(o);this._progressPath.animate(t,e,i)},s.prototype.stop=function(){if(null===this._progressPath)throw new Error(o);void 0!==this._progressPath&&this._progressPath.stop()},s.prototype.pause=function(){if(null===this._progressPath)throw new Error(o);void 0!==this._progressPath&&this._progressPath._tweenable&&this._progressPath._tweenable.pause()},s.prototype.resume=function(){if(null===this._progressPath)throw new Error(o);void 0!==this._progressPath&&this._progressPath._tweenable&&this._progressPath._tweenable.resume()},s.prototype.destroy=function(){if(null===this._progressPath)throw new Error(o);this.stop(),this.svg.parentNode.removeChild(this.svg),this.svg=null,this.path=null,this.trail=null,this._progressPath=null,null!==this.text&&(this.text.parentNode.removeChild(this.text),this.text=null)},s.prototype.set=function(t){if(null===this._progressPath)throw new Error(o);this._progressPath.set(t)},s.prototype.value=function(){if(null===this._progressPath)throw new Error(o);return void 0===this._progressPath?0:this._progressPath.value()},s.prototype.setText=function(t){if(null===this._progressPath)throw new Error(o);null===this.text&&(this.text=this._createTextContainer(this._opts,this._container),this._container.appendChild(this.text)),r.isObject(t)?(r.removeChildren(this.text),this.text.appendChild(t)):this.text.innerHTML=t},s.prototype._createSvgView=function(t){var e=document.createElementNS("http://www.w3.org/2000/svg","svg");this._initializeSvg(e,t);var i=null;(t.trailColor||t.trailWidth)&&(i=this._createTrail(t),e.appendChild(i));var n=this._createPath(t);return e.appendChild(n),{svg:e,path:n,trail:i}},s.prototype._initializeSvg=function(t,e){t.setAttribute("viewBox","0 0 100 100")},s.prototype._createPath=function(t){var e=this._pathString(t);return this._createPathElement(e,t)},s.prototype._createTrail=function(t){var e=this._trailString(t),i=r.extend({},t);return i.trailColor||(i.trailColor="#eee"),i.trailWidth||(i.trailWidth=i.strokeWidth),i.color=i.trailColor,i.strokeWidth=i.trailWidth,i.fill=null,this._createPathElement(e,i)},s.prototype._createPathElement=function(t,e){var i=document.createElementNS("http://www.w3.org/2000/svg","path");return i.setAttribute("d",t),i.setAttribute("stroke",e.color),i.setAttribute("stroke-width",e.strokeWidth),e.fill?i.setAttribute("fill",e.fill):i.setAttribute("fill-opacity","0"),i},s.prototype._createTextContainer=function(t,e){var i=document.createElement("div");i.className=t.text.className;var n=t.text.style;return n&&(t.text.autoStyleContainer&&(e.style.position="relative"),r.setStyles(i,n),n.color||(i.style.color=t.color)),this._initializeTextContainer(t,e,i),i},s.prototype._initializeTextContainer=function(t,e,i){},s.prototype._pathString=function(t){throw new Error("Override this function for each progress bar")},s.prototype._trailString=function(t){throw new Error("Override this function for each progress bar")},s.prototype._warnContainerAspectRatio=function(t){if(this.containerAspectRatio){var e=window.getComputedStyle(t,null),i=parseFloat(e.getPropertyValue("width"),10),n=parseFloat(e.getPropertyValue("height"),10);r.floatEquals(this.containerAspectRatio,i/n)||(console.warn("Incorrect aspect ratio of container","#"+t.id,"detected:",e.getPropertyValue("width")+"(width)","/",e.getPropertyValue("height")+"(height)","=",i/n),console.warn("Aspect ratio of should be",this.containerAspectRatio))}},e.exports=s},{"./path":6,"./utils":10}],9:[function(t,e,i){var n=t("./shape"),r=t("./utils"),o=function(t,e){this._pathTemplate="M 0,{halfOfStrokeWidth} L {width},{halfOfStrokeWidth} L {width},{width} L {halfOfStrokeWidth},{width} L {halfOfStrokeWidth},{strokeWidth}",this._trailTemplate="M {startMargin},{halfOfStrokeWidth} L {width},{halfOfStrokeWidth} L {width},{width} L {halfOfStrokeWidth},{width} L {halfOfStrokeWidth},{halfOfStrokeWidth}",n.apply(this,arguments)};o.prototype=new n,o.prototype.constructor=o,o.prototype._pathString=function(t){var e=100-t.strokeWidth/2;return r.render(this._pathTemplate,{width:e,strokeWidth:t.strokeWidth,halfOfStrokeWidth:t.strokeWidth/2})},o.prototype._trailString=function(t){var e=100-t.strokeWidth/2;return r.render(this._trailTemplate,{width:e,strokeWidth:t.strokeWidth,halfOfStrokeWidth:t.strokeWidth/2,startMargin:t.strokeWidth/2-t.trailWidth/2})},e.exports=o},{"./shape":8,"./utils":10}],10:[function(t,e,i){var n=t("lodash.merge"),r="Webkit Moz O ms".split(" "),o=.001;function s(t,e){var i=t;for(var n in e)if(e.hasOwnProperty(n)){var r=e[n],o=new RegExp("\\{"+n+"\\}","g");i=i.replace(o,r)}return i}function a(t,e,i){for(var n=t.style,o=0;o<r.length;++o)n[r[o]+c(e)]=i;n[e]=i}function l(t,e){p(e,(function(e,i){null!=e&&(f(e)&&!0===e.prefix?a(t,i,e.value):t.style[i]=e)}))}function c(t){return t.charAt(0).toUpperCase()+t.slice(1)}function h(t){return"string"==typeof t||t instanceof String}function u(t){return"function"==typeof t}function d(t){return"[object Array]"===Object.prototype.toString.call(t)}function f(t){return!d(t)&&"object"==typeof t&&!!t}function p(t,e){for(var i in t)t.hasOwnProperty(i)&&e(t[i],i)}function g(t,e){return Math.abs(t-e)<o}function m(t){for(;t.firstChild;)t.removeChild(t.firstChild)}e.exports={extend:n,render:s,setStyle:a,setStyles:l,capitalize:c,isString:h,isFunction:u,isObject:f,forEachObject:p,floatEquals:g,removeChildren:m}},{"lodash.merge":1}]},{},[5])(5)},604:function(t,e,i){var n;!function(){"use strict";var r={not_string:/[^s]/,not_bool:/[^t]/,not_type:/[^T]/,not_primitive:/[^v]/,number:/[diefg]/,numeric_arg:/[bcdiefguxX]/,json:/[j]/,not_json:/[^j]/,text:/^[^\x25]+/,modulo:/^\x25{2}/,placeholder:/^\x25(?:([1-9]\d*)\$|\(([^)]+)\))?(\+)?(0|'[^$])?(-)?(\d+)?(?:\.(\d+))?([b-gijostTuvxX])/,key:/^([a-z_][a-z_\d]*)/i,key_access:/^\.([a-z_][a-z_\d]*)/i,index_access:/^\[(\d+)\]/,sign:/^[+-]/};function o(t){return function(t,e){var i,n,s,a,l,c,h,u,d,f=1,p=t.length,g="";for(n=0;n<p;n++)if("string"==typeof t[n])g+=t[n];else if("object"==typeof t[n]){if((a=t[n]).keys)for(i=e[f],s=0;s<a.keys.length;s++){if(null==i)throw new Error(o('[sprintf] Cannot access property "%s" of undefined value "%s"',a.keys[s],a.keys[s-1]));i=i[a.keys[s]]}else i=a.param_no?e[a.param_no]:e[f++];if(r.not_type.test(a.type)&&r.not_primitive.test(a.type)&&i instanceof Function&&(i=i()),r.numeric_arg.test(a.type)&&"number"!=typeof i&&isNaN(i))throw new TypeError(o("[sprintf] expecting number but found %T",i));switch(r.number.test(a.type)&&(u=i>=0),a.type){case"b":i=parseInt(i,10).toString(2);break;case"c":i=String.fromCharCode(parseInt(i,10));break;case"d":case"i":i=parseInt(i,10);break;case"j":i=JSON.stringify(i,null,a.width?parseInt(a.width):0);break;case"e":i=a.precision?parseFloat(i).toExponential(a.precision):parseFloat(i).toExponential();break;case"f":i=a.precision?parseFloat(i).toFixed(a.precision):parseFloat(i);break;case"g":i=a.precision?String(Number(i.toPrecision(a.precision))):parseFloat(i);break;case"o":i=(parseInt(i,10)>>>0).toString(8);break;case"s":i=String(i),i=a.precision?i.substring(0,a.precision):i;break;case"t":i=String(!!i),i=a.precision?i.substring(0,a.precision):i;break;case"T":i=Object.prototype.toString.call(i).slice(8,-1).toLowerCase(),i=a.precision?i.substring(0,a.precision):i;break;case"u":i=parseInt(i,10)>>>0;break;case"v":i=i.valueOf(),i=a.precision?i.substring(0,a.precision):i;break;case"x":i=(parseInt(i,10)>>>0).toString(16);break;case"X":i=(parseInt(i,10)>>>0).toString(16).toUpperCase()}r.json.test(a.type)?g+=i:(!r.number.test(a.type)||u&&!a.sign?d="":(d=u?"+":"-",i=i.toString().replace(r.sign,"")),c=a.pad_char?"0"===a.pad_char?"0":a.pad_char.charAt(1):" ",h=a.width-(d+i).length,l=a.width&&h>0?c.repeat(h):"",g+=a.align?d+i+l:"0"===c?d+l+i:l+d+i)}return g}(function(t){if(a[t])return a[t];var e,i=t,n=[],o=0;for(;i;){if(null!==(e=r.text.exec(i)))n.push(e[0]);else if(null!==(e=r.modulo.exec(i)))n.push("%");else{if(null===(e=r.placeholder.exec(i)))throw new SyntaxError("[sprintf] unexpected placeholder");if(e[2]){o|=1;var s=[],l=e[2],c=[];if(null===(c=r.key.exec(l)))throw new SyntaxError("[sprintf] failed to parse named argument key");for(s.push(c[1]);""!==(l=l.substring(c[0].length));)if(null!==(c=r.key_access.exec(l)))s.push(c[1]);else{if(null===(c=r.index_access.exec(l)))throw new SyntaxError("[sprintf] failed to parse named argument key");s.push(c[1])}e[2]=s}else o|=2;if(3===o)throw new Error("[sprintf] mixing positional and named placeholders is not (yet) supported");n.push({placeholder:e[0],param_no:e[1],keys:e[2],sign:e[3],pad_char:e[4],align:e[5],width:e[6],precision:e[7],type:e[8]})}i=i.substring(e[0].length)}return a[t]=n}(t),arguments)}function s(t,e){return o.apply(null,[t].concat(e||[]))}var a=Object.create(null);o,s,"undefined"!=typeof window&&(window.sprintf=o,window.vsprintf=s,void 0===(n=function(){return{sprintf:o,vsprintf:s}}.call(e,i,e,t))||(t.exports=n))}()},544:function(){!function(){const t=function(){const t=jQuery("#field-video_player").val(),e=jQuery("#field-video_controls").prop("checked"),i=jQuery('#field-video_autoplay_mode option[value="off"]');"cld"!==t||e?i.prop("disabled",!1):(i.prop("disabled",!0),i.prop("selected")&&i.next().prop("selected",!0))};t(),jQuery(document).on("change","#field-video_player",t),jQuery(document).on("change","#field-video_controls",t),jQuery(document).ready((function(t){t.isFunction(t.fn.wpColorPicker)&&t(".regular-color").wpColorPicker(),t(document).on("tabs.init",(function(){const e=t(".settings-tab-trigger"),i=t(".settings-tab-section");t(this).on("click",".settings-tab-trigger",(function(n){const r=t(this),o=t(r.attr("href"));n.preventDefault(),e.removeClass("active"),i.removeClass("active"),r.addClass("active"),o.addClass("active"),t(document).trigger("settings.tabbed",r)})),t(".cld-field").not('[data-condition="false"]').each((function(){const e=t(this),i=e.data("condition");for(const n in i){let r=t("#field-"+n);const o=i[n],s=e.closest("tr");r.length||(r=t(`[id^=field-${n}-]`));let a=!1;r.on("change init",(function(t,e=!1){if(a&&e)return;let i=this.value===o||this.checked;if(Array.isArray(o)&&2===o.length)switch(o[1]){case"neq":i=this.value!==o[0];break;case"gt":i=this.value>o[0];break;case"lt":i=this.value<o[0]}i?s.show():s.hide(),a=!0})),r.trigger("init",!0)}})),t("#field-cloudinary_url").on("input change",(function(){const e=t(this),i=e.val();new RegExp(/^(?:CLOUDINARY_URL=)?(cloudinary:\/\/){1}(\d)*[:]{1}[^:@]*[@]{1}[^@]*$/g).test(i)?(e.addClass("settings-valid-field"),e.removeClass("settings-invalid-field")):(e.removeClass("settings-valid-field"),e.addClass("settings-invalid-field"))})).trigger("change"),t('[name="cloudinary_sync_media[auto_sync]"]').change((function(){"on"===t(this).val()&&t("#auto-sync-alert-btn").click()}))})),t(".render-trigger[data-event]").each((function(){const e=t(this),i=e.data("event");e.trigger(i,this)}))}))}(window,jQuery)},712:function(){const t=document.querySelector(".cloudinary-collapsible__toggle");t&&t.addEventListener("click",(function(){const t=document.querySelector(".cloudinary-collapsible__content"),e="none"===window.getComputedStyle(t,null).getPropertyValue("display"),i=document.querySelector(".cloudinary-collapsible__toggle button i");t.style.display=e?"block":"none";const n="dashicons-arrow-down-alt2",r="dashicons-arrow-up-alt2";i.classList.contains(n)?(i.classList.remove(n),i.classList.add(r)):(i.classList.remove(r),i.classList.add(n))}))},633:function(t,e,i){var n=i(738).default;function r(){"use strict";t.exports=r=function(){return i},t.exports.__esModule=!0,t.exports.default=t.exports;var e,i={},o=Object.prototype,s=o.hasOwnProperty,a=Object.defineProperty||function(t,e,i){t[e]=i.value},l="function"==typeof Symbol?Symbol:{},c=l.iterator||"@@iterator",h=l.asyncIterator||"@@asyncIterator",u=l.toStringTag||"@@toStringTag";function d(t,e,i){return Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{d({},"")}catch(e){d=function(t,e,i){return t[e]=i}}function f(t,e,i,n){var r=e&&e.prototype instanceof x?e:x,o=Object.create(r.prototype),s=new D(n||[]);return a(o,"_invoke",{value:T(t,i,s)}),o}function p(t,e,i){try{return{type:"normal",arg:t.call(e,i)}}catch(t){return{type:"throw",arg:t}}}i.wrap=f;var g="suspendedStart",m="suspendedYield",b="executing",v="completed",y={};function x(){}function _(){}function w(){}var k={};d(k,c,(function(){return this}));var O=Object.getPrototypeOf,S=O&&O(O(j([])));S&&S!==o&&s.call(S,c)&&(k=S);var M=w.prototype=x.prototype=Object.create(k);function E(t){["next","throw","return"].forEach((function(e){d(t,e,(function(t){return this._invoke(e,t)}))}))}function P(t,e){function i(r,o,a,l){var c=p(t[r],t,o);if("throw"!==c.type){var h=c.arg,u=h.value;return u&&"object"==n(u)&&s.call(u,"__await")?e.resolve(u.__await).then((function(t){i("next",t,a,l)}),(function(t){i("throw",t,a,l)})):e.resolve(u).then((function(t){h.value=t,a(h)}),(function(t){return i("throw",t,a,l)}))}l(c.arg)}var r;a(this,"_invoke",{value:function(t,n){function o(){return new e((function(e,r){i(t,n,e,r)}))}return r=r?r.then(o,o):o()}})}function T(t,i,n){var r=g;return function(o,s){if(r===b)throw Error("Generator is already running");if(r===v){if("throw"===o)throw s;return{value:e,done:!0}}for(n.method=o,n.arg=s;;){var a=n.delegate;if(a){var l=L(a,n);if(l){if(l===y)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(r===g)throw r=v,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r=b;var c=p(t,i,n);if("normal"===c.type){if(r=n.done?v:m,c.arg===y)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(r=v,n.method="throw",n.arg=c.arg)}}}function L(t,i){var n=i.method,r=t.iterator[n];if(r===e)return i.delegate=null,"throw"===n&&t.iterator.return&&(i.method="return",i.arg=e,L(t,i),"throw"===i.method)||"return"!==n&&(i.method="throw",i.arg=new TypeError("The iterator does not provide a '"+n+"' method")),y;var o=p(r,t.iterator,i.arg);if("throw"===o.type)return i.method="throw",i.arg=o.arg,i.delegate=null,y;var s=o.arg;return s?s.done?(i[t.resultName]=s.value,i.next=t.nextLoc,"return"!==i.method&&(i.method="next",i.arg=e),i.delegate=null,y):s:(i.method="throw",i.arg=new TypeError("iterator result is not an object"),i.delegate=null,y)}function A(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function C(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function D(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(A,this),this.reset(!0)}function j(t){if(t||""===t){var i=t[c];if(i)return i.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var r=-1,o=function i(){for(;++r<t.length;)if(s.call(t,r))return i.value=t[r],i.done=!1,i;return i.value=e,i.done=!0,i};return o.next=o}}throw new TypeError(n(t)+" is not iterable")}return _.prototype=w,a(M,"constructor",{value:w,configurable:!0}),a(w,"constructor",{value:_,configurable:!0}),_.displayName=d(w,u,"GeneratorFunction"),i.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===_||"GeneratorFunction"===(e.displayName||e.name))},i.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,w):(t.__proto__=w,d(t,u,"GeneratorFunction")),t.prototype=Object.create(M),t},i.awrap=function(t){return{__await:t}},E(P.prototype),d(P.prototype,h,(function(){return this})),i.AsyncIterator=P,i.async=function(t,e,n,r,o){void 0===o&&(o=Promise);var s=new P(f(t,e,n,r),o);return i.isGeneratorFunction(e)?s:s.next().then((function(t){return t.done?t.value:s.next()}))},E(M),d(M,u,"Generator"),d(M,c,(function(){return this})),d(M,"toString",(function(){return"[object Generator]"})),i.keys=function(t){var e=Object(t),i=[];for(var n in e)i.push(n);return i.reverse(),function t(){for(;i.length;){var n=i.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},i.values=j,D.prototype={constructor:D,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(C),!t)for(var i in this)"t"===i.charAt(0)&&s.call(this,i)&&!isNaN(+i.slice(1))&&(this[i]=e)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var i=this;function n(n,r){return a.type="throw",a.arg=t,i.next=n,r&&(i.method="next",i.arg=e),!!r}for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r],a=o.completion;if("root"===o.tryLoc)return n("end");if(o.tryLoc<=this.prev){var l=s.call(o,"catchLoc"),c=s.call(o,"finallyLoc");if(l&&c){if(this.prev<o.catchLoc)return n(o.catchLoc,!0);if(this.prev<o.finallyLoc)return n(o.finallyLoc)}else if(l){if(this.prev<o.catchLoc)return n(o.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return n(o.finallyLoc)}}}},abrupt:function(t,e){for(var i=this.tryEntries.length-1;i>=0;--i){var n=this.tryEntries[i];if(n.tryLoc<=this.prev&&s.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var r=n;break}}r&&("break"===t||"continue"===t)&&r.tryLoc<=e&&e<=r.finallyLoc&&(r=null);var o=r?r.completion:{};return o.type=t,o.arg=e,r?(this.method="next",this.next=r.finallyLoc,y):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),y},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var i=this.tryEntries[e];if(i.finallyLoc===t)return this.complete(i.completion,i.afterLoc),C(i),y}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var i=this.tryEntries[e];if(i.tryLoc===t){var n=i.completion;if("throw"===n.type){var r=n.arg;C(i)}return r}}throw Error("illegal catch attempt")},delegateYield:function(t,i,n){return this.delegate={iterator:j(t),resultName:i,nextLoc:n},"next"===this.method&&(this.arg=e),y}},i}t.exports=r,t.exports.__esModule=!0,t.exports.default=t.exports},738:function(t){function e(i){return t.exports=e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t.exports.__esModule=!0,t.exports.default=t.exports,e(i)}t.exports=e,t.exports.__esModule=!0,t.exports.default=t.exports},756:function(t,e,i){var n=i(633)();t.exports=n;try{regeneratorRuntime=n}catch(t){"object"==typeof globalThis?globalThis.regeneratorRuntime=n:Function("r","regeneratorRuntime = r")(n)}}},e={};function i(n){var r=e[n];if(void 0!==r)return r.exports;var o=e[n]={exports:{}};return t[n].call(o.exports,o,o.exports,i),o.exports}i.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return i.d(e,{a:e}),e},i.d=function(t,e){for(var n in e)i.o(e,n)&&!i.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})},i.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"==typeof window)return window}}(),i.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},function(){var t;i.g.importScripts&&(t=i.g.location+"");var e=i.g.document;if(!t&&e&&(e.currentScript&&"SCRIPT"===e.currentScript.tagName.toUpperCase()&&(t=e.currentScript.src),!t)){var n=e.getElementsByTagName("script");if(n.length)for(var r=n.length-1;r>-1&&(!t||!/^http(s?):/.test(t));)t=n[r--].src}if(!t)throw new Error("Automatic publicPath is not supported in this browser");t=t.replace(/#.*$/,"").replace(/\?.*$/,"").replace(/\/[^\/]+$/,"/"),i.p=t}(),function(){"use strict";i(617),i(712);var t=i(544),e=i.n(t);const n={sample:{image:document.getElementById("transformation-sample-image"),video:document.getElementById("transformation-sample-video")},preview:{image:document.getElementById("sample-image"),video:document.getElementById("sample-video")},fields:document.getElementsByClassName("cld-ui-input"),button:{image:document.getElementById("refresh-image-preview"),video:document.getElementById("refresh-video-preview")},spinner:{image:document.getElementById("image-loader"),video:document.getElementById("video-loader")},optimization:{image:document.getElementById("image_settings.image_optimization"),video:document.getElementById("video_settings.video_optimization")},error_container:document.getElementById("cld-preview-error"),activeItem:null,elements:{image:[],video:[]},_placeItem(t){null!==t&&(t.style.display="block",t.style.visibility="visible",t.style.position="absolute",t.style.top=t.parentElement.clientHeight/2-t.clientHeight/2+"px",t.style.left=t.parentElement.clientWidth/2-t.clientWidth/2+"px")},_setLoading(t){this.sample[t]&&(this.button[t].style.display="block",this._placeItem(this.button[t]),this.preview[t].style.opacity="0.1")},_build(t){if(!this.sample[t])return;this.sample[t].innerHTML="",this.elements[t]=[];for(const e of this.fields){if(t!==e.dataset.context||e.dataset.disabled&&"true"===e.dataset.disabled)continue;let i=e.value.trim();if(i.length){if("select-one"===e.type){if("none"===i||!1===this.optimization[t].checked)continue;i=e.dataset.meta+"_"+i}else t=e.dataset.context,e.dataset.meta&&(i=e.dataset.meta+"_"+i),e.dataset.suffix&&(i+=e.dataset.suffix),i=this._transformations(i,t,!0);i&&this.elements[t].push(i)}}let e="";this.elements[t].length&&(e="/"+this._getGlobalTransformationElements(t).replace(/ /g,"%20")),this.sample[t].textContent=e,this.sample[t].parentElement.href="https://res.cloudinary.com/demo/"+this.sample[t].parentElement.innerText.trim().replace("../","").replace(/ /g,"%20")},_clearLoading(t){this.spinner[t].style.visibility="hidden",this.activeItem=null,this.preview[t].style.opacity=1},_refresh(t,e){if(t&&t.preventDefault(),!this.sample[e])return;const i=this,n=CLD_GLOBAL_TRANSFORMATIONS[e].preview_url+this._getGlobalTransformationElements(e)+CLD_GLOBAL_TRANSFORMATIONS[e].file;if(this.button[e].style.display="none",this._placeItem(this.spinner[e]),"image"===e){const t=new Image;t.onload=function(){i.preview[e].src=this.src,i._clearLoading(e),i.error_container&&(i.error_container.style.display="none"),t.remove()},t.onerror=function(){const t=i.elements[e].includes("f_mp4");i.error_container&&(i.error_container.style.display="block",t?(i.error_container.innerHTML=CLD_GLOBAL_TRANSFORMATIONS[e].warning.replace("%s","f_mp4"),i.error_container.classList.replace("settings-alert-error","settings-alert-warning")):(i.error_container.innerHTML=CLD_GLOBAL_TRANSFORMATIONS[e].error,i.error_container.classList.replace("settings-alert-warning","settings-alert-error"))),i._clearLoading(e)},t.src=n}else{const t=i._transformations(i._getGlobalTransformationElements(e),e);samplePlayer.source({publicId:"dog",transformation:t}),i._clearLoading(e)}},_getGlobalTransformationElements(t){let e=[];return e.push(this.elements[t].slice(0,2).join(",")),e.push(this.elements[t].slice(2).join(",")),e=e.filter((t=>t)).join("/"),e},_transformations(t,e,i=!1){const n=CLD_GLOBAL_TRANSFORMATIONS[e].valid_types;let r=null;const o=t.split("/"),s=[];for(let t=0;t<o.length;t++){const r=o[t].split(",");let a;a=!0===i?[]:{};for(let t=0;t<r.length;t++){const o=r[t].trim().split("_");if(o.length<=1||void 0===n[o[0]])continue;const s=o.shift(),l=o.join("_");if(!0===i){if("f"===s||"q"===s)for(const t in this.elements[e])s+"_"===this.elements[e][t].substr(0,2)&&this.elements[e].splice(t,1);a.push(r[t])}else a[n[s]]=l.trim()}let l=0;l=!0===i?a.length:Object.keys(a).length,l&&(!0===i&&(a=a.join(",")),s.push(a))}return s.length&&(r=!0===i?s.join("/").trim():s),r},_reset(){for(const t of this.fields)t.value=null;for(const t in this.button)this._build(t),this._refresh(null,t)},_input(t){if(void 0!==t.dataset.context&&t.dataset.context.length){const e=t.dataset.context;this._setLoading(e),this._build(e)}},_init(){if("undefined"!=typeof CLD_GLOBAL_TRANSFORMATIONS){const t=this;document.addEventListener("DOMContentLoaded",(function(){for(const e in t.button)t.button[e]&&t.button[e].addEventListener("click",(function(i){t._refresh(i,e)}));for(const e of t.fields)e.addEventListener("input",(function(){t._input(this)})),e.addEventListener("change",(function(){t._input(this)}));for(const e in CLD_GLOBAL_TRANSFORMATIONS)t._build(e),t._refresh(null,e)})),jQuery(document).ajaxComplete((function(e,i,n){-1!==n.data.indexOf("action=add-tag")&&-1===i.responseText.indexOf("wp_error")&&t._reset()}))}}};n._init();function r(t){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},r(t)}function o(t){var e=function(t,e){if("object"!=r(t)||!t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var n=i.call(t,e||"default");if("object"!=r(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==r(e)?e:e+""}function s(t,e,i){return(e=o(e))in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}function a(t,e){if(null==t)return{};var i,n,r=function(t,e){if(null==t)return{};var i={};for(var n in t)if({}.hasOwnProperty.call(t,n)){if(e.includes(n))continue;i[n]=t[n]}return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)i=o[n],e.includes(i)||{}.propertyIsEnumerable.call(t,i)&&(r[i]=t[i])}return r}var l,c,h,u,d=i(616),f=i.n(d);i(604),f()(console.error);l={"(":9,"!":8,"*":7,"/":7,"%":7,"+":6,"-":6,"<":5,"<=":5,">":5,">=":5,"==":4,"!=":4,"&&":3,"||":2,"?":1,"?:":1},c=["(","?"],h={")":["("],":":["?","?:"]},u=/<=|>=|==|!=|&&|\|\||\?:|\(|!|\*|\/|%|\+|-|<|>|\?|\)|:/;var p={"!":function(t){return!t},"*":function(t,e){return t*e},"/":function(t,e){return t/e},"%":function(t,e){return t%e},"+":function(t,e){return t+e},"-":function(t,e){return t-e},"<":function(t,e){return t<e},"<=":function(t,e){return t<=e},">":function(t,e){return t>e},">=":function(t,e){return t>=e},"==":function(t,e){return t===e},"!=":function(t,e){return t!==e},"&&":function(t,e){return t&&e},"||":function(t,e){return t||e},"?:":function(t,e,i){if(t)throw e;return i}};function g(t){var e=function(t){for(var e,i,n,r,o=[],s=[];e=t.match(u);){for(i=e[0],(n=t.substr(0,e.index).trim())&&o.push(n);r=s.pop();){if(h[i]){if(h[i][0]===r){i=h[i][1]||i;break}}else if(c.indexOf(r)>=0||l[r]<l[i]){s.push(r);break}o.push(r)}h[i]||s.push(i),t=t.substr(e.index+i.length)}return(t=t.trim())&&o.push(t),o.concat(s.reverse())}(t);return function(t){return function(t,e){var i,n,r,o,s,a,l=[];for(i=0;i<t.length;i++){if(s=t[i],o=p[s]){for(n=o.length,r=Array(n);n--;)r[n]=l.pop();try{a=o.apply(null,r)}catch(t){return t}}else a=e.hasOwnProperty(s)?e[s]:+s;l.push(a)}return l[0]}(e,t)}}var m={contextDelimiter:"",onMissingKey:null};function b(t,e){var i;for(i in this.data=t,this.pluralForms={},this.options={},m)this.options[i]=void 0!==e&&i in e?e[i]:m[i]}function v(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,n)}return i}function y(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?v(Object(i),!0).forEach((function(e){s(t,e,i[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):v(Object(i)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}b.prototype.getPluralForm=function(t,e){var i,n,r,o=this.pluralForms[t];return o||("function"!=typeof(r=(i=this.data[t][""])["Plural-Forms"]||i["plural-forms"]||i.plural_forms)&&(n=function(t){var e,i,n;for(e=t.split(";"),i=0;i<e.length;i++)if(0===(n=e[i].trim()).indexOf("plural="))return n.substr(7)}(i["Plural-Forms"]||i["plural-forms"]||i.plural_forms),r=function(t){var e=g(t);return function(t){return+e({n:t})}}(n)),o=this.pluralForms[t]=r),o(e)},b.prototype.dcnpgettext=function(t,e,i,n,r){var o,s,a;return o=void 0===r?0:this.getPluralForm(t,r),s=i,e&&(s=e+this.options.contextDelimiter+i),(a=this.data[t][s])&&a[o]?a[o]:(this.options.onMissingKey&&this.options.onMissingKey(i,t),0===o?i:n)};var x={"":{plural_forms:function(t){return 1===t?0:1}}},_=/^i18n\.(n?gettext|has_translation)(_|$)/;var w=function(t){return"string"!=typeof t||""===t?(console.error("The namespace must be a non-empty string."),!1):!!/^[a-zA-Z][a-zA-Z0-9_.\-\/]*$/.test(t)||(console.error("The namespace can only contain numbers, letters, dashes, periods, underscores and slashes."),!1)};var k=function(t){return"string"!=typeof t||""===t?(console.error("The hook name must be a non-empty string."),!1):/^__/.test(t)?(console.error("The hook name cannot begin with `__`."),!1):!!/^[a-zA-Z][a-zA-Z0-9_.-]*$/.test(t)||(console.error("The hook name can only contain numbers, letters, dashes, periods and underscores."),!1)};var O=function(t,e){return function(i,n,r){var o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:10,s=t[e];if(k(i)&&w(n))if("function"==typeof r)if("number"==typeof o){var a={callback:r,priority:o,namespace:n};if(s[i]){var l,c=s[i].handlers;for(l=c.length;l>0&&!(o>=c[l-1].priority);l--);l===c.length?c[l]=a:c.splice(l,0,a),s.__current.forEach((function(t){t.name===i&&t.currentIndex>=l&&t.currentIndex++}))}else s[i]={handlers:[a],runs:0};"hookAdded"!==i&&t.doAction("hookAdded",i,n,r,o)}else console.error("If specified, the hook priority must be a number.");else console.error("The hook callback must be a function.")}};var S=function(t,e){var i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return function(n,r){var o=t[e];if(k(n)&&(i||w(r))){if(!o[n])return 0;var s=0;if(i)s=o[n].handlers.length,o[n]={runs:o[n].runs,handlers:[]};else for(var a=o[n].handlers,l=function(t){a[t].namespace===r&&(a.splice(t,1),s++,o.__current.forEach((function(e){e.name===n&&e.currentIndex>=t&&e.currentIndex--})))},c=a.length-1;c>=0;c--)l(c);return"hookRemoved"!==n&&t.doAction("hookRemoved",n,r),s}}};var M=function(t,e){return function(i,n){var r=t[e];return void 0!==n?i in r&&r[i].handlers.some((function(t){return t.namespace===n})):i in r}};var E=function(t,e){var i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return function(n){var r=t[e];r[n]||(r[n]={handlers:[],runs:0}),r[n].runs++;var o=r[n].handlers;for(var s=arguments.length,a=new Array(s>1?s-1:0),l=1;l<s;l++)a[l-1]=arguments[l];if(!o||!o.length)return i?a[0]:void 0;var c={name:n,currentIndex:0};for(r.__current.push(c);c.currentIndex<o.length;){var h=o[c.currentIndex].callback.apply(null,a);i&&(a[0]=h),c.currentIndex++}return r.__current.pop(),i?a[0]:void 0}};var P=function(t,e){return function(){var i,n,r=t[e];return null!==(i=null===(n=r.__current[r.__current.length-1])||void 0===n?void 0:n.name)&&void 0!==i?i:null}};var T=function(t,e){return function(i){var n=t[e];return void 0===i?void 0!==n.__current[0]:!!n.__current[0]&&i===n.__current[0].name}};var L=function(t,e){return function(i){var n=t[e];if(k(i))return n[i]&&n[i].runs?n[i].runs:0}},A=function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.actions=Object.create(null),this.actions.__current=[],this.filters=Object.create(null),this.filters.__current=[],this.addAction=O(this,"actions"),this.addFilter=O(this,"filters"),this.removeAction=S(this,"actions"),this.removeFilter=S(this,"filters"),this.hasAction=M(this,"actions"),this.hasFilter=M(this,"filters"),this.removeAllActions=S(this,"actions",!0),this.removeAllFilters=S(this,"filters",!0),this.doAction=E(this,"actions"),this.applyFilters=E(this,"filters",!0),this.currentAction=P(this,"actions"),this.currentFilter=P(this,"filters"),this.doingAction=T(this,"actions"),this.doingFilter=T(this,"filters"),this.didAction=L(this,"actions"),this.didFilter=L(this,"filters")};var C=function(){return new A}(),D=(C.addAction,C.addFilter,C.removeAction,C.removeFilter,C.hasAction,C.hasFilter,C.removeAllActions,C.removeAllFilters,C.doAction,C.applyFilters,C.currentAction,C.currentFilter,C.doingAction,C.doingFilter,C.didAction,C.didFilter,C.actions,C.filters,function(t,e,i){var n=new b({}),r=new Set,o=function(){r.forEach((function(t){return t()}))},s=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"default";n.data[e]=y(y(y({},x),n.data[e]),t),n.data[e][""]=y(y({},x[""]),n.data[e][""])},a=function(t,e){s(t,e),o()},l=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"default",e=arguments.length>1?arguments[1]:void 0,i=arguments.length>2?arguments[2]:void 0,r=arguments.length>3?arguments[3]:void 0,o=arguments.length>4?arguments[4]:void 0;return n.data[t]||s(void 0,t),n.dcnpgettext(t,e,i,r,o)},c=function(){return arguments.length>0&&void 0!==arguments[0]?arguments[0]:"default"},h=function(t,e,n){var r=l(n,e,t);return i?(r=i.applyFilters("i18n.gettext_with_context",r,t,e,n),i.applyFilters("i18n.gettext_with_context_"+c(n),r,t,e,n)):r};if(t&&a(t,e),i){var u=function(t){_.test(t)&&o()};i.addAction("hookAdded","core/i18n",u),i.addAction("hookRemoved","core/i18n",u)}return{getLocaleData:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"default";return n.data[t]},setLocaleData:a,resetLocaleData:function(t,e){n.data={},n.pluralForms={},a(t,e)},subscribe:function(t){return r.add(t),function(){return r.delete(t)}},__:function(t,e){var n=l(e,void 0,t);return i?(n=i.applyFilters("i18n.gettext",n,t,e),i.applyFilters("i18n.gettext_"+c(e),n,t,e)):n},_x:h,_n:function(t,e,n,r){var o=l(r,void 0,t,e,n);return i?(o=i.applyFilters("i18n.ngettext",o,t,e,n,r),i.applyFilters("i18n.ngettext_"+c(r),o,t,e,n,r)):o},_nx:function(t,e,n,r,o){var s=l(o,r,t,e,n);return i?(s=i.applyFilters("i18n.ngettext_with_context",s,t,e,n,r,o),i.applyFilters("i18n.ngettext_with_context_"+c(o),s,t,e,n,r,o)):s},isRTL:function(){return"rtl"===h("ltr","text direction")},hasTranslation:function(t,e,r){var o,s,a=e?e+""+t:t,l=!(null===(o=n.data)||void 0===o||null===(s=o[null!=r?r:"default"])||void 0===s||!s[a]);return i&&(l=i.applyFilters("i18n.has_translation",l,t,e,r),l=i.applyFilters("i18n.has_translation_"+c(r),l,t,e,r)),l}}}(void 0,void 0,C)),j=(D.getLocaleData.bind(D),D.setLocaleData.bind(D),D.resetLocaleData.bind(D),D.subscribe.bind(D),D.__.bind(D));D._x.bind(D),D._n.bind(D),D._nx.bind(D),D.isRTL.bind(D),D.hasTranslation.bind(D);function I(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,n)}return i}function R(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?I(Object(i),!0).forEach((function(e){s(t,e,i[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):I(Object(i)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}var F=function(t){var e=function t(e,i){var n=e.headers,r=void 0===n?{}:n;for(var o in r)if("x-wp-nonce"===o.toLowerCase()&&r[o]===t.nonce)return i(e);return i(R(R({},e),{},{headers:R(R({},r),{},{"X-WP-Nonce":t.nonce})}))};return e.nonce=t,e};function z(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,n)}return i}function N(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?z(Object(i),!0).forEach((function(e){s(t,e,i[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):z(Object(i)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}var B=function(t,e){var i,n,r=t.path;return"string"==typeof t.namespace&&"string"==typeof t.endpoint&&(i=t.namespace.replace(/^\/|\/$/g,""),r=(n=t.endpoint.replace(/^\//,""))?i+"/"+n:i),delete t.namespace,delete t.endpoint,e(N(N({},t),{},{path:r}))};function W(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,n)}return i}function V(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?W(Object(i),!0).forEach((function(e){s(t,e,i[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):W(Object(i)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}var H=function(t){return function(e,i){return B(e,(function(e){var n,r=e.url,o=e.path;return"string"==typeof o&&(n=t,-1!==t.indexOf("?")&&(o=o.replace("?","&")),o=o.replace(/^\//,""),"string"==typeof n&&-1!==n.indexOf("?")&&(o=o.replace("?","&")),r=n+o),i(V(V({},e),{},{url:r}))}))}};function $(t){var e=t.split("?"),i=e[1],n=e[0];return i?n+"?"+i.split("&").map((function(t){return t.split("=")})).sort((function(t,e){return t[0].localeCompare(e[0])})).map((function(t){return t.join("=")})).join("&"):n}var U=function(t){var e=Object.keys(t).reduce((function(e,i){return e[$(i)]=t[i],e}),{});return function(t,i){var n=t.parse,r=void 0===n||n;if("string"==typeof t.path){var o=t.method||"GET",s=$(t.path);if("GET"===o&&e[s]){var a=e[s];return delete e[s],Promise.resolve(r?a.body:new window.Response(JSON.stringify(a.body),{status:200,statusText:"OK",headers:a.headers}))}if("OPTIONS"===o&&e[o]&&e[o][s])return Promise.resolve(e[o][s])}return i(t)}};function q(t,e,i,n,r,o,s){try{var a=t[o](s),l=a.value}catch(t){return void i(t)}a.done?e(l):Promise.resolve(l).then(n,r)}var Y=i(756),X=i.n(Y);function G(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,n=Array(e);i<e;i++)n[i]=t[i];return n}function K(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var i=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=i){var n,r,o,s,a=[],l=!0,c=!1;try{if(o=(i=i.call(t)).next,0===e){if(Object(i)!==i)return;l=!1}else for(;!(l=(n=o.call(i)).done)&&(a.push(n.value),a.length!==e);l=!0);}catch(t){c=!0,r=t}finally{try{if(!l&&null!=i.return&&(s=i.return(),Object(s)!==s))return}finally{if(c)throw r}}return a}}(t,e)||function(t,e){if(t){if("string"==typeof t)return G(t,e);var i={}.toString.call(t).slice(8,-1);return"Object"===i&&t.constructor&&(i=t.constructor.name),"Map"===i||"Set"===i?Array.from(t):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?G(t,e):void 0}}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function J(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,n)}return i}function Q(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?J(Object(i),!0).forEach((function(e){s(t,e,i[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):J(Object(i)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}function Z(t){return(function(t){var e;try{e=new URL(t,"http://example.com").search.substring(1)}catch(t){}if(e)return e}(t)||"").replace(/\+/g,"%20").split("&").reduce((function(t,e){var i=K(e.split("=").filter(Boolean).map(decodeURIComponent),2),n=i[0],r=i[1],o=void 0===r?"":r;n&&function(t,e,i){for(var n=e.length,r=n-1,o=0;o<n;o++){var s=e[o];!s&&Array.isArray(t)&&(s=t.length.toString());var a=!isNaN(Number(e[o+1]));t[s]=o===r?i:t[s]||(a?[]:{}),Array.isArray(t[s])&&!a&&(t[s]=Q({},t[s])),t=t[s]}}(t,n.replace(/\]/g,"").split("["),o);return t}),{})}function tt(t,e){var i;if("undefined"==typeof Symbol||null==t[Symbol.iterator]){if(Array.isArray(t)||(i=function(t,e){if(!t)return;if("string"==typeof t)return et(t,e);var i=Object.prototype.toString.call(t).slice(8,-1);"Object"===i&&t.constructor&&(i=t.constructor.name);if("Map"===i||"Set"===i)return Array.from(t);if("Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i))return et(t,e)}(t))||e&&t&&"number"==typeof t.length){i&&(t=i);var n=0,r=function(){};return{s:r,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,s=!0,a=!1;return{s:function(){i=t[Symbol.iterator]()},n:function(){var t=i.next();return s=t.done,t},e:function(t){a=!0,o=t},f:function(){try{s||null==i.return||i.return()}finally{if(a)throw o}}}}function et(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,n=new Array(e);i<e;i++)n[i]=t[i];return n}function it(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=arguments.length>1?arguments[1]:void 0;if(!e||!Object.keys(e).length)return t;var i=t,n=t.indexOf("?");return-1!==n&&(e=Object.assign(Z(t),e),i=i.substr(0,n)),i+"?"+function(t){for(var e,i="",n=Object.entries(t);e=n.shift();){var r=K(e,2),o=r[0],s=r[1];if(Array.isArray(s)||s&&s.constructor===Object){var a,l=tt(Object.entries(s).reverse());try{for(l.s();!(a=l.n()).done;){var c=K(a.value,2),h=c[0],u=c[1];n.unshift(["".concat(o,"[").concat(h,"]"),u])}}catch(t){l.e(t)}finally{l.f()}}else void 0!==s&&(null===s&&(s=""),i+="&"+[o,s].map(encodeURIComponent).join("="))}return i.substr(1)}(e)}function nt(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,n)}return i}function rt(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?nt(Object(i),!0).forEach((function(e){s(t,e,i[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):nt(Object(i)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}var ot=function(t){return t.json?t.json():Promise.reject(t)},st=function(t){return function(t){if(!t)return{};var e=t.match(/<([^>]+)>; rel="next"/);return e?{next:e[1]}:{}}(t.headers.get("link")).next},at=function(t){var e=!!t.path&&-1!==t.path.indexOf("per_page=-1"),i=!!t.url&&-1!==t.url.indexOf("per_page=-1");return e||i},lt=function(){var t,e=(t=X().mark((function t(e,i){var n,r,o,s,l,c;return X().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!1!==e.parse){t.next=2;break}return t.abrupt("return",i(e));case 2:if(at(e)){t.next=4;break}return t.abrupt("return",i(e));case 4:return t.next=6,Et(rt(rt({},(u={per_page:100},d=void 0,f=void 0,d=(h=e).path,f=h.url,rt(rt({},a(h,["path","url"])),{},{url:f&&it(f,u),path:d&&it(d,u)}))),{},{parse:!1}));case 6:return n=t.sent,t.next=9,ot(n);case 9:if(r=t.sent,Array.isArray(r)){t.next=12;break}return t.abrupt("return",r);case 12:if(o=st(n)){t.next=15;break}return t.abrupt("return",r);case 15:s=[].concat(r);case 16:if(!o){t.next=27;break}return t.next=19,Et(rt(rt({},e),{},{path:void 0,url:o,parse:!1}));case 19:return l=t.sent,t.next=22,ot(l);case 22:c=t.sent,s=s.concat(c),o=st(l),t.next=16;break;case 27:return t.abrupt("return",s);case 28:case"end":return t.stop()}var h,u,d,f}),t)})),function(){var e=this,i=arguments;return new Promise((function(n,r){var o=t.apply(e,i);function s(t){q(o,n,r,s,a,"next",t)}function a(t){q(o,n,r,s,a,"throw",t)}s(void 0)}))});return function(t,i){return e.apply(this,arguments)}}(),ct=lt;function ht(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,n)}return i}function ut(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?ht(Object(i),!0).forEach((function(e){s(t,e,i[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):ht(Object(i)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}var dt=new Set(["PATCH","PUT","DELETE"]);function ft(t,e){return void 0!==function(t,e){return Z(t)[e]}(t,e)}var pt=function(t){var e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];return Promise.resolve(function(t){return arguments.length>1&&void 0!==arguments[1]&&!arguments[1]?t:204===t.status?null:t.json?t.json():Promise.reject(t)}(t,e)).catch((function(t){return gt(t,e)}))};function gt(t){if(!(!(arguments.length>1&&void 0!==arguments[1])||arguments[1]))throw t;return function(t){var e={code:"invalid_json",message:j("The response is not a valid JSON response.")};if(!t||!t.json)throw e;return t.json().catch((function(){throw e}))}(t).then((function(t){var e={code:"unknown_error",message:j("An unknown error occurred.")};throw t||e}))}function mt(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,n)}return i}function bt(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?mt(Object(i),!0).forEach((function(e){s(t,e,i[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):mt(Object(i)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}var vt=function(t,e){if(!(t.path&&-1!==t.path.indexOf("/wp/v2/media")||t.url&&-1!==t.url.indexOf("/wp/v2/media")))return e(t);var i=0,n=function t(n){return i++,e({path:"/wp/v2/media/".concat(n,"/post-process"),method:"POST",data:{action:"create-image-subsizes"},parse:!1}).catch((function(){return i<5?t(n):(e({path:"/wp/v2/media/".concat(n,"?force=true"),method:"DELETE"}),Promise.reject())}))};return e(bt(bt({},t),{},{parse:!1})).catch((function(e){var i=e.headers.get("x-wp-upload-attachment-id");return e.status>=500&&e.status<600&&i?n(i).catch((function(){return!1!==t.parse?Promise.reject({code:"post_process",message:j("Media upload failed. If this is a photo or a large image, please scale it down and try again.")}):Promise.reject(e)})):gt(e,t.parse)})).then((function(e){return pt(e,t.parse)}))};function yt(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,n)}return i}function xt(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?yt(Object(i),!0).forEach((function(e){s(t,e,i[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):yt(Object(i)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}var _t={Accept:"application/json, */*;q=0.1"},wt={credentials:"include"},kt=[function(t,e){return"string"!=typeof t.url||ft(t.url,"_locale")||(t.url=it(t.url,{_locale:"user"})),"string"!=typeof t.path||ft(t.path,"_locale")||(t.path=it(t.path,{_locale:"user"})),e(t)},B,function(t,e){var i=t.method,n=void 0===i?"GET":i;return dt.has(n.toUpperCase())&&(t=ut(ut({},t),{},{headers:ut(ut({},t.headers),{},{"X-HTTP-Method-Override":n,"Content-Type":"application/json"}),method:"POST"})),e(t)},ct];var Ot=function(t){if(t.status>=200&&t.status<300)return t;throw t},St=function(t){var e=t.url,i=t.path,n=t.data,r=t.parse,o=void 0===r||r,s=a(t,["url","path","data","parse"]),l=t.body,c=t.headers;return c=xt(xt({},_t),c),n&&(l=JSON.stringify(n),c["Content-Type"]="application/json"),window.fetch(e||i||window.location.href,xt(xt(xt({},wt),s),{},{body:l,headers:c})).then((function(t){return Promise.resolve(t).then(Ot).catch((function(t){return gt(t,o)})).then((function(t){return pt(t,o)}))}),(function(){throw{code:"fetch_error",message:j("You are probably offline.")}}))};function Mt(t){return kt.reduceRight((function(t,e){return function(i){return e(i,t)}}),St)(t).catch((function(e){return"rest_cookie_invalid_nonce"!==e.code?Promise.reject(e):window.fetch(Mt.nonceEndpoint).then(Ot).then((function(t){return t.text()})).then((function(e){return Mt.nonceMiddleware.nonce=e,Mt(t)}))}))}Mt.use=function(t){kt.unshift(t)},Mt.setFetchHandler=function(t){St=t},Mt.createNonceMiddleware=F,Mt.createPreloadingMiddleware=U,Mt.createRootURLMiddleware=H,Mt.fetchAllMiddleware=ct,Mt.mediaUploadMiddleware=vt;var Et=Mt;const Pt={wpWrap:document.getElementById("wpwrap"),adminbar:document.getElementById("wpadminbar"),wpContent:document.getElementById("wpbody-content"),libraryWrap:document.getElementById("cloudinary-dam"),cloudinaryHeader:document.getElementById("cloudinary-header"),wpFooter:document.getElementById("wpfooter"),importStatus:document.getElementById("import-status"),downloading:{},_init(){const t=this,e=this.libraryWrap,i=this.importStatus;"undefined"!=typeof CLDN&&document.querySelector(CLDN.mloptions.inline_container)&&(Et.use(Et.createNonceMiddleware(CLDN.nonce)),cloudinary.openMediaLibrary(CLDN.mloptions,{insertHandler(n){const r=[];for(let o=0;o<n.assets.length;o++){const s=n.assets[o];wp.ajax.post("cloudinary-down-sync",{nonce:CLDN.nonce,asset:s}).done((function(n){e.style.marginRight="220px",i.style.display="block";const o=t.makeProgress(n),s="download-"+n.public_id;r[s]=o,i.appendChild(o),setTimeout((()=>{o.style.opacity=1}),250),Et({path:cldData.dam.fetch_url,data:{src:n.url,filename:n.filename,attachment_id:n.attachment_id,transformations:n.transformations},method:"POST"}).then((t=>{const n=r[s];delete r[s],n.removeChild(n.firstChild),setTimeout((()=>{n.style.opacity=0,setTimeout((()=>{n.parentNode.removeChild(n),Object.keys(r).length||(e.style.marginRight="0px",i.style.display="none")}),1e3)}),500)}))}))}}}),window.addEventListener("resize",(function(){t._resize()})),t._resize())},_resize(){this.libraryWrap.style.height=this.wpFooter.offsetTop-this.libraryWrap.offsetTop-this.adminbar.offsetHeight+"px"},makeProgress(t){const e=document.createElement("div"),i=document.createElement("span"),n=document.createElement("span");return e.classList.add("cld-import-item"),i.classList.add("spinner"),n.classList.add("cld-import-item-id"),n.innerText=t.public_id,e.appendChild(i),e.appendChild(n),e}};window.addEventListener("load",(()=>Pt._init()));const Tt={_init(){const t=this;if("undefined"!=typeof CLDIS){[...document.getElementsByClassName("cld-notice-box")].forEach((e=>{const i=e.getElementsByClassName("notice-dismiss");i.length&&i[0].addEventListener("click",(i=>{e.style.height=e.offsetHeight+"px",i.preventDefault(),setTimeout((function(){t._dismiss(e)}),5)}))}))}},_dismiss(t){const e=t.dataset.dismiss,i=parseInt(t.dataset.duration);t.classList.add("dismissed"),t.style.height="0px",setTimeout((function(){t.remove()}),400),0<i&&wp.ajax.send({url:CLDIS.url,data:{token:e,duration:i,_wpnonce:CLDIS.nonce}})}};window.addEventListener("load",Tt._init());function Lt(t){if(null==t)return window;if("[object Window]"!==t.toString()){var e=t.ownerDocument;return e&&e.defaultView||window}return t}function At(t){return t instanceof Lt(t).Element||t instanceof Element}function Ct(t){return t instanceof Lt(t).HTMLElement||t instanceof HTMLElement}function Dt(t){return"undefined"!=typeof ShadowRoot&&(t instanceof Lt(t).ShadowRoot||t instanceof ShadowRoot)}var jt=Math.max,It=Math.min,Rt=Math.round;function Ft(){var t=navigator.userAgentData;return null!=t&&t.brands?t.brands.map((function(t){return t.brand+"/"+t.version})).join(" "):navigator.userAgent}function zt(){return!/^((?!chrome|android).)*safari/i.test(Ft())}function Nt(t,e,i){void 0===e&&(e=!1),void 0===i&&(i=!1);var n=t.getBoundingClientRect(),r=1,o=1;e&&Ct(t)&&(r=t.offsetWidth>0&&Rt(n.width)/t.offsetWidth||1,o=t.offsetHeight>0&&Rt(n.height)/t.offsetHeight||1);var s=(At(t)?Lt(t):window).visualViewport,a=!zt()&&i,l=(n.left+(a&&s?s.offsetLeft:0))/r,c=(n.top+(a&&s?s.offsetTop:0))/o,h=n.width/r,u=n.height/o;return{width:h,height:u,top:c,right:l+h,bottom:c+u,left:l,x:l,y:c}}function Bt(t){var e=Lt(t);return{scrollLeft:e.pageXOffset,scrollTop:e.pageYOffset}}function Wt(t){return t?(t.nodeName||"").toLowerCase():null}function Vt(t){return((At(t)?t.ownerDocument:t.document)||window.document).documentElement}function Ht(t){return Nt(Vt(t)).left+Bt(t).scrollLeft}function $t(t){return Lt(t).getComputedStyle(t)}function Ut(t){var e=$t(t),i=e.overflow,n=e.overflowX,r=e.overflowY;return/auto|scroll|overlay|hidden/.test(i+r+n)}function qt(t,e,i){void 0===i&&(i=!1);var n,r,o=Ct(e),s=Ct(e)&&function(t){var e=t.getBoundingClientRect(),i=Rt(e.width)/t.offsetWidth||1,n=Rt(e.height)/t.offsetHeight||1;return 1!==i||1!==n}(e),a=Vt(e),l=Nt(t,s,i),c={scrollLeft:0,scrollTop:0},h={x:0,y:0};return(o||!o&&!i)&&(("body"!==Wt(e)||Ut(a))&&(c=(n=e)!==Lt(n)&&Ct(n)?{scrollLeft:(r=n).scrollLeft,scrollTop:r.scrollTop}:Bt(n)),Ct(e)?((h=Nt(e,!0)).x+=e.clientLeft,h.y+=e.clientTop):a&&(h.x=Ht(a))),{x:l.left+c.scrollLeft-h.x,y:l.top+c.scrollTop-h.y,width:l.width,height:l.height}}function Yt(t){var e=Nt(t),i=t.offsetWidth,n=t.offsetHeight;return Math.abs(e.width-i)<=1&&(i=e.width),Math.abs(e.height-n)<=1&&(n=e.height),{x:t.offsetLeft,y:t.offsetTop,width:i,height:n}}function Xt(t){return"html"===Wt(t)?t:t.assignedSlot||t.parentNode||(Dt(t)?t.host:null)||Vt(t)}function Gt(t){return["html","body","#document"].indexOf(Wt(t))>=0?t.ownerDocument.body:Ct(t)&&Ut(t)?t:Gt(Xt(t))}function Kt(t,e){var i;void 0===e&&(e=[]);var n=Gt(t),r=n===(null==(i=t.ownerDocument)?void 0:i.body),o=Lt(n),s=r?[o].concat(o.visualViewport||[],Ut(n)?n:[]):n,a=e.concat(s);return r?a:a.concat(Kt(Xt(s)))}function Jt(t){return["table","td","th"].indexOf(Wt(t))>=0}function Qt(t){return Ct(t)&&"fixed"!==$t(t).position?t.offsetParent:null}function Zt(t){for(var e=Lt(t),i=Qt(t);i&&Jt(i)&&"static"===$t(i).position;)i=Qt(i);return i&&("html"===Wt(i)||"body"===Wt(i)&&"static"===$t(i).position)?e:i||function(t){var e=/firefox/i.test(Ft());if(/Trident/i.test(Ft())&&Ct(t)&&"fixed"===$t(t).position)return null;var i=Xt(t);for(Dt(i)&&(i=i.host);Ct(i)&&["html","body"].indexOf(Wt(i))<0;){var n=$t(i);if("none"!==n.transform||"none"!==n.perspective||"paint"===n.contain||-1!==["transform","perspective"].indexOf(n.willChange)||e&&"filter"===n.willChange||e&&n.filter&&"none"!==n.filter)return i;i=i.parentNode}return null}(t)||e}var te="top",ee="bottom",ie="right",ne="left",re="auto",oe=[te,ee,ie,ne],se="start",ae="end",le="clippingParents",ce="viewport",he="popper",ue="reference",de=oe.reduce((function(t,e){return t.concat([e+"-"+se,e+"-"+ae])}),[]),fe=[].concat(oe,[re]).reduce((function(t,e){return t.concat([e,e+"-"+se,e+"-"+ae])}),[]),pe=["beforeRead","read","afterRead","beforeMain","main","afterMain","beforeWrite","write","afterWrite"];function ge(t){var e=new Map,i=new Set,n=[];function r(t){i.add(t.name),[].concat(t.requires||[],t.requiresIfExists||[]).forEach((function(t){if(!i.has(t)){var n=e.get(t);n&&r(n)}})),n.push(t)}return t.forEach((function(t){e.set(t.name,t)})),t.forEach((function(t){i.has(t.name)||r(t)})),n}var me={placement:"bottom",modifiers:[],strategy:"absolute"};function be(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];return!e.some((function(t){return!(t&&"function"==typeof t.getBoundingClientRect)}))}function ve(t){void 0===t&&(t={});var e=t,i=e.defaultModifiers,n=void 0===i?[]:i,r=e.defaultOptions,o=void 0===r?me:r;return function(t,e,i){void 0===i&&(i=o);var r,s,a={placement:"bottom",orderedModifiers:[],options:Object.assign({},me,o),modifiersData:{},elements:{reference:t,popper:e},attributes:{},styles:{}},l=[],c=!1,h={state:a,setOptions:function(i){var r="function"==typeof i?i(a.options):i;u(),a.options=Object.assign({},o,a.options,r),a.scrollParents={reference:At(t)?Kt(t):t.contextElement?Kt(t.contextElement):[],popper:Kt(e)};var s=function(t){var e=ge(t);return pe.reduce((function(t,i){return t.concat(e.filter((function(t){return t.phase===i})))}),[])}(function(t){var e=t.reduce((function(t,e){var i=t[e.name];return t[e.name]=i?Object.assign({},i,e,{options:Object.assign({},i.options,e.options),data:Object.assign({},i.data,e.data)}):e,t}),{});return Object.keys(e).map((function(t){return e[t]}))}([].concat(n,a.options.modifiers)));return a.orderedModifiers=s.filter((function(t){return t.enabled})),a.orderedModifiers.forEach((function(t){var e=t.name,i=t.options,n=void 0===i?{}:i,r=t.effect;if("function"==typeof r){var o=r({state:a,name:e,instance:h,options:n}),s=function(){};l.push(o||s)}})),h.update()},forceUpdate:function(){if(!c){var t=a.elements,e=t.reference,i=t.popper;if(be(e,i)){a.rects={reference:qt(e,Zt(i),"fixed"===a.options.strategy),popper:Yt(i)},a.reset=!1,a.placement=a.options.placement,a.orderedModifiers.forEach((function(t){return a.modifiersData[t.name]=Object.assign({},t.data)}));for(var n=0;n<a.orderedModifiers.length;n++)if(!0!==a.reset){var r=a.orderedModifiers[n],o=r.fn,s=r.options,l=void 0===s?{}:s,u=r.name;"function"==typeof o&&(a=o({state:a,options:l,name:u,instance:h})||a)}else a.reset=!1,n=-1}}},update:(r=function(){return new Promise((function(t){h.forceUpdate(),t(a)}))},function(){return s||(s=new Promise((function(t){Promise.resolve().then((function(){s=void 0,t(r())}))}))),s}),destroy:function(){u(),c=!0}};if(!be(t,e))return h;function u(){l.forEach((function(t){return t()})),l=[]}return h.setOptions(i).then((function(t){!c&&i.onFirstUpdate&&i.onFirstUpdate(t)})),h}}var ye={passive:!0};function xe(t){return t.split("-")[0]}function _e(t){return t.split("-")[1]}function we(t){return["top","bottom"].indexOf(t)>=0?"x":"y"}function ke(t){var e,i=t.reference,n=t.element,r=t.placement,o=r?xe(r):null,s=r?_e(r):null,a=i.x+i.width/2-n.width/2,l=i.y+i.height/2-n.height/2;switch(o){case te:e={x:a,y:i.y-n.height};break;case ee:e={x:a,y:i.y+i.height};break;case ie:e={x:i.x+i.width,y:l};break;case ne:e={x:i.x-n.width,y:l};break;default:e={x:i.x,y:i.y}}var c=o?we(o):null;if(null!=c){var h="y"===c?"height":"width";switch(s){case se:e[c]=e[c]-(i[h]/2-n[h]/2);break;case ae:e[c]=e[c]+(i[h]/2-n[h]/2)}}return e}var Oe={top:"auto",right:"auto",bottom:"auto",left:"auto"};function Se(t){var e,i=t.popper,n=t.popperRect,r=t.placement,o=t.variation,s=t.offsets,a=t.position,l=t.gpuAcceleration,c=t.adaptive,h=t.roundOffsets,u=t.isFixed,d=s.x,f=void 0===d?0:d,p=s.y,g=void 0===p?0:p,m="function"==typeof h?h({x:f,y:g}):{x:f,y:g};f=m.x,g=m.y;var b=s.hasOwnProperty("x"),v=s.hasOwnProperty("y"),y=ne,x=te,_=window;if(c){var w=Zt(i),k="clientHeight",O="clientWidth";if(w===Lt(i)&&"static"!==$t(w=Vt(i)).position&&"absolute"===a&&(k="scrollHeight",O="scrollWidth"),r===te||(r===ne||r===ie)&&o===ae)x=ee,g-=(u&&w===_&&_.visualViewport?_.visualViewport.height:w[k])-n.height,g*=l?1:-1;if(r===ne||(r===te||r===ee)&&o===ae)y=ie,f-=(u&&w===_&&_.visualViewport?_.visualViewport.width:w[O])-n.width,f*=l?1:-1}var S,M=Object.assign({position:a},c&&Oe),E=!0===h?function(t){var e=t.x,i=t.y,n=window.devicePixelRatio||1;return{x:Rt(e*n)/n||0,y:Rt(i*n)/n||0}}({x:f,y:g}):{x:f,y:g};return f=E.x,g=E.y,l?Object.assign({},M,((S={})[x]=v?"0":"",S[y]=b?"0":"",S.transform=(_.devicePixelRatio||1)<=1?"translate("+f+"px, "+g+"px)":"translate3d("+f+"px, "+g+"px, 0)",S)):Object.assign({},M,((e={})[x]=v?g+"px":"",e[y]=b?f+"px":"",e.transform="",e))}var Me={name:"applyStyles",enabled:!0,phase:"write",fn:function(t){var e=t.state;Object.keys(e.elements).forEach((function(t){var i=e.styles[t]||{},n=e.attributes[t]||{},r=e.elements[t];Ct(r)&&Wt(r)&&(Object.assign(r.style,i),Object.keys(n).forEach((function(t){var e=n[t];!1===e?r.removeAttribute(t):r.setAttribute(t,!0===e?"":e)})))}))},effect:function(t){var e=t.state,i={popper:{position:e.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(e.elements.popper.style,i.popper),e.styles=i,e.elements.arrow&&Object.assign(e.elements.arrow.style,i.arrow),function(){Object.keys(e.elements).forEach((function(t){var n=e.elements[t],r=e.attributes[t]||{},o=Object.keys(e.styles.hasOwnProperty(t)?e.styles[t]:i[t]).reduce((function(t,e){return t[e]="",t}),{});Ct(n)&&Wt(n)&&(Object.assign(n.style,o),Object.keys(r).forEach((function(t){n.removeAttribute(t)})))}))}},requires:["computeStyles"]};var Ee={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(t){var e=t.state,i=t.options,n=t.name,r=i.offset,o=void 0===r?[0,0]:r,s=fe.reduce((function(t,i){return t[i]=function(t,e,i){var n=xe(t),r=[ne,te].indexOf(n)>=0?-1:1,o="function"==typeof i?i(Object.assign({},e,{placement:t})):i,s=o[0],a=o[1];return s=s||0,a=(a||0)*r,[ne,ie].indexOf(n)>=0?{x:a,y:s}:{x:s,y:a}}(i,e.rects,o),t}),{}),a=s[e.placement],l=a.x,c=a.y;null!=e.modifiersData.popperOffsets&&(e.modifiersData.popperOffsets.x+=l,e.modifiersData.popperOffsets.y+=c),e.modifiersData[n]=s}},Pe={left:"right",right:"left",bottom:"top",top:"bottom"};function Te(t){return t.replace(/left|right|bottom|top/g,(function(t){return Pe[t]}))}var Le={start:"end",end:"start"};function Ae(t){return t.replace(/start|end/g,(function(t){return Le[t]}))}function Ce(t,e){var i=e.getRootNode&&e.getRootNode();if(t.contains(e))return!0;if(i&&Dt(i)){var n=e;do{if(n&&t.isSameNode(n))return!0;n=n.parentNode||n.host}while(n)}return!1}function De(t){return Object.assign({},t,{left:t.x,top:t.y,right:t.x+t.width,bottom:t.y+t.height})}function je(t,e,i){return e===ce?De(function(t,e){var i=Lt(t),n=Vt(t),r=i.visualViewport,o=n.clientWidth,s=n.clientHeight,a=0,l=0;if(r){o=r.width,s=r.height;var c=zt();(c||!c&&"fixed"===e)&&(a=r.offsetLeft,l=r.offsetTop)}return{width:o,height:s,x:a+Ht(t),y:l}}(t,i)):At(e)?function(t,e){var i=Nt(t,!1,"fixed"===e);return i.top=i.top+t.clientTop,i.left=i.left+t.clientLeft,i.bottom=i.top+t.clientHeight,i.right=i.left+t.clientWidth,i.width=t.clientWidth,i.height=t.clientHeight,i.x=i.left,i.y=i.top,i}(e,i):De(function(t){var e,i=Vt(t),n=Bt(t),r=null==(e=t.ownerDocument)?void 0:e.body,o=jt(i.scrollWidth,i.clientWidth,r?r.scrollWidth:0,r?r.clientWidth:0),s=jt(i.scrollHeight,i.clientHeight,r?r.scrollHeight:0,r?r.clientHeight:0),a=-n.scrollLeft+Ht(t),l=-n.scrollTop;return"rtl"===$t(r||i).direction&&(a+=jt(i.clientWidth,r?r.clientWidth:0)-o),{width:o,height:s,x:a,y:l}}(Vt(t)))}function Ie(t,e,i,n){var r="clippingParents"===e?function(t){var e=Kt(Xt(t)),i=["absolute","fixed"].indexOf($t(t).position)>=0&&Ct(t)?Zt(t):t;return At(i)?e.filter((function(t){return At(t)&&Ce(t,i)&&"body"!==Wt(t)})):[]}(t):[].concat(e),o=[].concat(r,[i]),s=o[0],a=o.reduce((function(e,i){var r=je(t,i,n);return e.top=jt(r.top,e.top),e.right=It(r.right,e.right),e.bottom=It(r.bottom,e.bottom),e.left=jt(r.left,e.left),e}),je(t,s,n));return a.width=a.right-a.left,a.height=a.bottom-a.top,a.x=a.left,a.y=a.top,a}function Re(t){return Object.assign({},{top:0,right:0,bottom:0,left:0},t)}function Fe(t,e){return e.reduce((function(e,i){return e[i]=t,e}),{})}function ze(t,e){void 0===e&&(e={});var i=e,n=i.placement,r=void 0===n?t.placement:n,o=i.strategy,s=void 0===o?t.strategy:o,a=i.boundary,l=void 0===a?le:a,c=i.rootBoundary,h=void 0===c?ce:c,u=i.elementContext,d=void 0===u?he:u,f=i.altBoundary,p=void 0!==f&&f,g=i.padding,m=void 0===g?0:g,b=Re("number"!=typeof m?m:Fe(m,oe)),v=d===he?ue:he,y=t.rects.popper,x=t.elements[p?v:d],_=Ie(At(x)?x:x.contextElement||Vt(t.elements.popper),l,h,s),w=Nt(t.elements.reference),k=ke({reference:w,element:y,strategy:"absolute",placement:r}),O=De(Object.assign({},y,k)),S=d===he?O:w,M={top:_.top-S.top+b.top,bottom:S.bottom-_.bottom+b.bottom,left:_.left-S.left+b.left,right:S.right-_.right+b.right},E=t.modifiersData.offset;if(d===he&&E){var P=E[r];Object.keys(M).forEach((function(t){var e=[ie,ee].indexOf(t)>=0?1:-1,i=[te,ee].indexOf(t)>=0?"y":"x";M[t]+=P[i]*e}))}return M}function Ne(t,e,i){return jt(t,It(e,i))}var Be={name:"preventOverflow",enabled:!0,phase:"main",fn:function(t){var e=t.state,i=t.options,n=t.name,r=i.mainAxis,o=void 0===r||r,s=i.altAxis,a=void 0!==s&&s,l=i.boundary,c=i.rootBoundary,h=i.altBoundary,u=i.padding,d=i.tether,f=void 0===d||d,p=i.tetherOffset,g=void 0===p?0:p,m=ze(e,{boundary:l,rootBoundary:c,padding:u,altBoundary:h}),b=xe(e.placement),v=_e(e.placement),y=!v,x=we(b),_="x"===x?"y":"x",w=e.modifiersData.popperOffsets,k=e.rects.reference,O=e.rects.popper,S="function"==typeof g?g(Object.assign({},e.rects,{placement:e.placement})):g,M="number"==typeof S?{mainAxis:S,altAxis:S}:Object.assign({mainAxis:0,altAxis:0},S),E=e.modifiersData.offset?e.modifiersData.offset[e.placement]:null,P={x:0,y:0};if(w){if(o){var T,L="y"===x?te:ne,A="y"===x?ee:ie,C="y"===x?"height":"width",D=w[x],j=D+m[L],I=D-m[A],R=f?-O[C]/2:0,F=v===se?k[C]:O[C],z=v===se?-O[C]:-k[C],N=e.elements.arrow,B=f&&N?Yt(N):{width:0,height:0},W=e.modifiersData["arrow#persistent"]?e.modifiersData["arrow#persistent"].padding:{top:0,right:0,bottom:0,left:0},V=W[L],H=W[A],$=Ne(0,k[C],B[C]),U=y?k[C]/2-R-$-V-M.mainAxis:F-$-V-M.mainAxis,q=y?-k[C]/2+R+$+H+M.mainAxis:z+$+H+M.mainAxis,Y=e.elements.arrow&&Zt(e.elements.arrow),X=Y?"y"===x?Y.clientTop||0:Y.clientLeft||0:0,G=null!=(T=null==E?void 0:E[x])?T:0,K=D+q-G,J=Ne(f?It(j,D+U-G-X):j,D,f?jt(I,K):I);w[x]=J,P[x]=J-D}if(a){var Q,Z="x"===x?te:ne,tt="x"===x?ee:ie,et=w[_],it="y"===_?"height":"width",nt=et+m[Z],rt=et-m[tt],ot=-1!==[te,ne].indexOf(b),st=null!=(Q=null==E?void 0:E[_])?Q:0,at=ot?nt:et-k[it]-O[it]-st+M.altAxis,lt=ot?et+k[it]+O[it]-st-M.altAxis:rt,ct=f&&ot?function(t,e,i){var n=Ne(t,e,i);return n>i?i:n}(at,et,lt):Ne(f?at:nt,et,f?lt:rt);w[_]=ct,P[_]=ct-et}e.modifiersData[n]=P}},requiresIfExists:["offset"]};var We={name:"arrow",enabled:!0,phase:"main",fn:function(t){var e,i=t.state,n=t.name,r=t.options,o=i.elements.arrow,s=i.modifiersData.popperOffsets,a=xe(i.placement),l=we(a),c=[ne,ie].indexOf(a)>=0?"height":"width";if(o&&s){var h=function(t,e){return Re("number"!=typeof(t="function"==typeof t?t(Object.assign({},e.rects,{placement:e.placement})):t)?t:Fe(t,oe))}(r.padding,i),u=Yt(o),d="y"===l?te:ne,f="y"===l?ee:ie,p=i.rects.reference[c]+i.rects.reference[l]-s[l]-i.rects.popper[c],g=s[l]-i.rects.reference[l],m=Zt(o),b=m?"y"===l?m.clientHeight||0:m.clientWidth||0:0,v=p/2-g/2,y=h[d],x=b-u[c]-h[f],_=b/2-u[c]/2+v,w=Ne(y,_,x),k=l;i.modifiersData[n]=((e={})[k]=w,e.centerOffset=w-_,e)}},effect:function(t){var e=t.state,i=t.options.element,n=void 0===i?"[data-popper-arrow]":i;null!=n&&("string"!=typeof n||(n=e.elements.popper.querySelector(n)))&&Ce(e.elements.popper,n)&&(e.elements.arrow=n)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function Ve(t,e,i){return void 0===i&&(i={x:0,y:0}),{top:t.top-e.height-i.y,right:t.right-e.width+i.x,bottom:t.bottom-e.height+i.y,left:t.left-e.width-i.x}}function He(t){return[te,ie,ee,ne].some((function(e){return t[e]>=0}))}var $e=ve({defaultModifiers:[{name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(t){var e=t.state,i=t.instance,n=t.options,r=n.scroll,o=void 0===r||r,s=n.resize,a=void 0===s||s,l=Lt(e.elements.popper),c=[].concat(e.scrollParents.reference,e.scrollParents.popper);return o&&c.forEach((function(t){t.addEventListener("scroll",i.update,ye)})),a&&l.addEventListener("resize",i.update,ye),function(){o&&c.forEach((function(t){t.removeEventListener("scroll",i.update,ye)})),a&&l.removeEventListener("resize",i.update,ye)}},data:{}},{name:"popperOffsets",enabled:!0,phase:"read",fn:function(t){var e=t.state,i=t.name;e.modifiersData[i]=ke({reference:e.rects.reference,element:e.rects.popper,strategy:"absolute",placement:e.placement})},data:{}},{name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(t){var e=t.state,i=t.options,n=i.gpuAcceleration,r=void 0===n||n,o=i.adaptive,s=void 0===o||o,a=i.roundOffsets,l=void 0===a||a,c={placement:xe(e.placement),variation:_e(e.placement),popper:e.elements.popper,popperRect:e.rects.popper,gpuAcceleration:r,isFixed:"fixed"===e.options.strategy};null!=e.modifiersData.popperOffsets&&(e.styles.popper=Object.assign({},e.styles.popper,Se(Object.assign({},c,{offsets:e.modifiersData.popperOffsets,position:e.options.strategy,adaptive:s,roundOffsets:l})))),null!=e.modifiersData.arrow&&(e.styles.arrow=Object.assign({},e.styles.arrow,Se(Object.assign({},c,{offsets:e.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:l})))),e.attributes.popper=Object.assign({},e.attributes.popper,{"data-popper-placement":e.placement})},data:{}},Me,Ee,{name:"flip",enabled:!0,phase:"main",fn:function(t){var e=t.state,i=t.options,n=t.name;if(!e.modifiersData[n]._skip){for(var r=i.mainAxis,o=void 0===r||r,s=i.altAxis,a=void 0===s||s,l=i.fallbackPlacements,c=i.padding,h=i.boundary,u=i.rootBoundary,d=i.altBoundary,f=i.flipVariations,p=void 0===f||f,g=i.allowedAutoPlacements,m=e.options.placement,b=xe(m),v=l||(b===m||!p?[Te(m)]:function(t){if(xe(t)===re)return[];var e=Te(t);return[Ae(t),e,Ae(e)]}(m)),y=[m].concat(v).reduce((function(t,i){return t.concat(xe(i)===re?function(t,e){void 0===e&&(e={});var i=e,n=i.placement,r=i.boundary,o=i.rootBoundary,s=i.padding,a=i.flipVariations,l=i.allowedAutoPlacements,c=void 0===l?fe:l,h=_e(n),u=h?a?de:de.filter((function(t){return _e(t)===h})):oe,d=u.filter((function(t){return c.indexOf(t)>=0}));0===d.length&&(d=u);var f=d.reduce((function(e,i){return e[i]=ze(t,{placement:i,boundary:r,rootBoundary:o,padding:s})[xe(i)],e}),{});return Object.keys(f).sort((function(t,e){return f[t]-f[e]}))}(e,{placement:i,boundary:h,rootBoundary:u,padding:c,flipVariations:p,allowedAutoPlacements:g}):i)}),[]),x=e.rects.reference,_=e.rects.popper,w=new Map,k=!0,O=y[0],S=0;S<y.length;S++){var M=y[S],E=xe(M),P=_e(M)===se,T=[te,ee].indexOf(E)>=0,L=T?"width":"height",A=ze(e,{placement:M,boundary:h,rootBoundary:u,altBoundary:d,padding:c}),C=T?P?ie:ne:P?ee:te;x[L]>_[L]&&(C=Te(C));var D=Te(C),j=[];if(o&&j.push(A[E]<=0),a&&j.push(A[C]<=0,A[D]<=0),j.every((function(t){return t}))){O=M,k=!1;break}w.set(M,j)}if(k)for(var I=function(t){var e=y.find((function(e){var i=w.get(e);if(i)return i.slice(0,t).every((function(t){return t}))}));if(e)return O=e,"break"},R=p?3:1;R>0;R--){if("break"===I(R))break}e.placement!==O&&(e.modifiersData[n]._skip=!0,e.placement=O,e.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}},Be,We,{name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(t){var e=t.state,i=t.name,n=e.rects.reference,r=e.rects.popper,o=e.modifiersData.preventOverflow,s=ze(e,{elementContext:"reference"}),a=ze(e,{altBoundary:!0}),l=Ve(s,n),c=Ve(a,r,o),h=He(l),u=He(c);e.modifiersData[i]={referenceClippingOffsets:l,popperEscapeOffsets:c,isReferenceHidden:h,hasPopperEscaped:u},e.attributes.popper=Object.assign({},e.attributes.popper,{"data-popper-reference-hidden":h,"data-popper-escaped":u})}}]}),Ue="tippy-content",qe="tippy-backdrop",Ye="tippy-arrow",Xe="tippy-svg-arrow",Ge={passive:!0,capture:!0},Ke=function(){return document.body};function Je(t,e,i){if(Array.isArray(t)){var n=t[e];return null==n?Array.isArray(i)?i[e]:i:n}return t}function Qe(t,e){var i={}.toString.call(t);return 0===i.indexOf("[object")&&i.indexOf(e+"]")>-1}function Ze(t,e){return"function"==typeof t?t.apply(void 0,e):t}function ti(t,e){return 0===e?t:function(n){clearTimeout(i),i=setTimeout((function(){t(n)}),e)};var i}function ei(t){return[].concat(t)}function ii(t,e){-1===t.indexOf(e)&&t.push(e)}function ni(t){return t.split("-")[0]}function ri(t){return[].slice.call(t)}function oi(t){return Object.keys(t).reduce((function(e,i){return void 0!==t[i]&&(e[i]=t[i]),e}),{})}function si(){return document.createElement("div")}function ai(t){return["Element","Fragment"].some((function(e){return Qe(t,e)}))}function li(t){return Qe(t,"MouseEvent")}function ci(t){return!(!t||!t._tippy||t._tippy.reference!==t)}function hi(t){return ai(t)?[t]:function(t){return Qe(t,"NodeList")}(t)?ri(t):Array.isArray(t)?t:ri(document.querySelectorAll(t))}function ui(t,e){t.forEach((function(t){t&&(t.style.transitionDuration=e+"ms")}))}function di(t,e){t.forEach((function(t){t&&t.setAttribute("data-state",e)}))}function fi(t){var e,i=ei(t)[0];return null!=i&&null!=(e=i.ownerDocument)&&e.body?i.ownerDocument:document}function pi(t,e,i){var n=e+"EventListener";["transitionend","webkitTransitionEnd"].forEach((function(e){t[n](e,i)}))}function gi(t,e){for(var i=e;i;){var n;if(t.contains(i))return!0;i=null==i.getRootNode||null==(n=i.getRootNode())?void 0:n.host}return!1}var mi={isTouch:!1},bi=0;function vi(){mi.isTouch||(mi.isTouch=!0,window.performance&&document.addEventListener("mousemove",yi))}function yi(){var t=performance.now();t-bi<20&&(mi.isTouch=!1,document.removeEventListener("mousemove",yi)),bi=t}function xi(){var t=document.activeElement;if(ci(t)){var e=t._tippy;t.blur&&!e.state.isVisible&&t.blur()}}var _i=!!("undefined"!=typeof window&&"undefined"!=typeof document)&&!!window.msCrypto;var wi={animateFill:!1,followCursor:!1,inlinePositioning:!1,sticky:!1},ki=Object.assign({appendTo:Ke,aria:{content:"auto",expanded:"auto"},delay:0,duration:[300,250],getReferenceClientRect:null,hideOnClick:!0,ignoreAttributes:!1,interactive:!1,interactiveBorder:2,interactiveDebounce:0,moveTransition:"",offset:[0,10],onAfterUpdate:function(){},onBeforeUpdate:function(){},onCreate:function(){},onDestroy:function(){},onHidden:function(){},onHide:function(){},onMount:function(){},onShow:function(){},onShown:function(){},onTrigger:function(){},onUntrigger:function(){},onClickOutside:function(){},placement:"top",plugins:[],popperOptions:{},render:null,showOnCreate:!1,touch:!0,trigger:"mouseenter focus",triggerTarget:null},wi,{allowHTML:!1,animation:"fade",arrow:!0,content:"",inertia:!1,maxWidth:350,role:"tooltip",theme:"",zIndex:9999}),Oi=Object.keys(ki);function Si(t){var e=(t.plugins||[]).reduce((function(e,i){var n,r=i.name,o=i.defaultValue;r&&(e[r]=void 0!==t[r]?t[r]:null!=(n=ki[r])?n:o);return e}),{});return Object.assign({},t,e)}function Mi(t,e){var i=Object.assign({},e,{content:Ze(e.content,[t])},e.ignoreAttributes?{}:function(t,e){return(e?Object.keys(Si(Object.assign({},ki,{plugins:e}))):Oi).reduce((function(e,i){var n=(t.getAttribute("data-tippy-"+i)||"").trim();if(!n)return e;if("content"===i)e[i]=n;else try{e[i]=JSON.parse(n)}catch(t){e[i]=n}return e}),{})}(t,e.plugins));return i.aria=Object.assign({},ki.aria,i.aria),i.aria={expanded:"auto"===i.aria.expanded?e.interactive:i.aria.expanded,content:"auto"===i.aria.content?e.interactive?null:"describedby":i.aria.content},i}var Ei=function(){return"innerHTML"};function Pi(t,e){t[Ei()]=e}function Ti(t){var e=si();return!0===t?e.className=Ye:(e.className=Xe,ai(t)?e.appendChild(t):Pi(e,t)),e}function Li(t,e){ai(e.content)?(Pi(t,""),t.appendChild(e.content)):"function"!=typeof e.content&&(e.allowHTML?Pi(t,e.content):t.textContent=e.content)}function Ai(t){var e=t.firstElementChild,i=ri(e.children);return{box:e,content:i.find((function(t){return t.classList.contains(Ue)})),arrow:i.find((function(t){return t.classList.contains(Ye)||t.classList.contains(Xe)})),backdrop:i.find((function(t){return t.classList.contains(qe)}))}}function Ci(t){var e=si(),i=si();i.className="tippy-box",i.setAttribute("data-state","hidden"),i.setAttribute("tabindex","-1");var n=si();function r(i,n){var r=Ai(e),o=r.box,s=r.content,a=r.arrow;n.theme?o.setAttribute("data-theme",n.theme):o.removeAttribute("data-theme"),"string"==typeof n.animation?o.setAttribute("data-animation",n.animation):o.removeAttribute("data-animation"),n.inertia?o.setAttribute("data-inertia",""):o.removeAttribute("data-inertia"),o.style.maxWidth="number"==typeof n.maxWidth?n.maxWidth+"px":n.maxWidth,n.role?o.setAttribute("role",n.role):o.removeAttribute("role"),i.content===n.content&&i.allowHTML===n.allowHTML||Li(s,t.props),n.arrow?a?i.arrow!==n.arrow&&(o.removeChild(a),o.appendChild(Ti(n.arrow))):o.appendChild(Ti(n.arrow)):a&&o.removeChild(a)}return n.className=Ue,n.setAttribute("data-state","hidden"),Li(n,t.props),e.appendChild(i),i.appendChild(n),r(t.props,t.props),{popper:e,onUpdate:r}}Ci.$$tippy=!0;var Di=1,ji=[],Ii=[];function Ri(t,e){var i,n,r,o,s,a,l,c,h=Mi(t,Object.assign({},ki,Si(oi(e)))),u=!1,d=!1,f=!1,p=!1,g=[],m=ti(Y,h.interactiveDebounce),b=Di++,v=(c=h.plugins).filter((function(t,e){return c.indexOf(t)===e})),y={id:b,reference:t,popper:si(),popperInstance:null,props:h,state:{isEnabled:!0,isVisible:!1,isDestroyed:!1,isMounted:!1,isShown:!1},plugins:v,clearDelayTimeouts:function(){clearTimeout(i),clearTimeout(n),cancelAnimationFrame(r)},setProps:function(e){0;if(y.state.isDestroyed)return;D("onBeforeUpdate",[y,e]),U();var i=y.props,n=Mi(t,Object.assign({},i,oi(e),{ignoreAttributes:!0}));y.props=n,$(),i.interactiveDebounce!==n.interactiveDebounce&&(R(),m=ti(Y,n.interactiveDebounce));i.triggerTarget&&!n.triggerTarget?ei(i.triggerTarget).forEach((function(t){t.removeAttribute("aria-expanded")})):n.triggerTarget&&t.removeAttribute("aria-expanded");I(),C(),w&&w(i,n);y.popperInstance&&(J(),Z().forEach((function(t){requestAnimationFrame(t._tippy.popperInstance.forceUpdate)})));D("onAfterUpdate",[y,e])},setContent:function(t){y.setProps({content:t})},show:function(){0;var t=y.state.isVisible,e=y.state.isDestroyed,i=!y.state.isEnabled,n=mi.isTouch&&!y.props.touch,r=Je(y.props.duration,0,ki.duration);if(t||e||i||n)return;if(P().hasAttribute("disabled"))return;if(D("onShow",[y],!1),!1===y.props.onShow(y))return;y.state.isVisible=!0,E()&&(_.style.visibility="visible");C(),B(),y.state.isMounted||(_.style.transition="none");if(E()){var o=L();ui([o.box,o.content],0)}a=function(){var t;if(y.state.isVisible&&!p){if(p=!0,_.offsetHeight,_.style.transition=y.props.moveTransition,E()&&y.props.animation){var e=L(),i=e.box,n=e.content;ui([i,n],r),di([i,n],"visible")}j(),I(),ii(Ii,y),null==(t=y.popperInstance)||t.forceUpdate(),D("onMount",[y]),y.props.animation&&E()&&function(t,e){V(t,e)}(r,(function(){y.state.isShown=!0,D("onShown",[y])}))}},function(){var t,e=y.props.appendTo,i=P();t=y.props.interactive&&e===Ke||"parent"===e?i.parentNode:Ze(e,[i]);t.contains(_)||t.appendChild(_);y.state.isMounted=!0,J(),!1}()},hide:function(){0;var t=!y.state.isVisible,e=y.state.isDestroyed,i=!y.state.isEnabled,n=Je(y.props.duration,1,ki.duration);if(t||e||i)return;if(D("onHide",[y],!1),!1===y.props.onHide(y))return;y.state.isVisible=!1,y.state.isShown=!1,p=!1,u=!1,E()&&(_.style.visibility="hidden");if(R(),W(),C(!0),E()){var r=L(),o=r.box,s=r.content;y.props.animation&&(ui([o,s],n),di([o,s],"hidden"))}j(),I(),y.props.animation?E()&&function(t,e){V(t,(function(){!y.state.isVisible&&_.parentNode&&_.parentNode.contains(_)&&e()}))}(n,y.unmount):y.unmount()},hideWithInteractivity:function(t){0;T().addEventListener("mousemove",m),ii(ji,m),m(t)},enable:function(){y.state.isEnabled=!0},disable:function(){y.hide(),y.state.isEnabled=!1},unmount:function(){0;y.state.isVisible&&y.hide();if(!y.state.isMounted)return;Q(),Z().forEach((function(t){t._tippy.unmount()})),_.parentNode&&_.parentNode.removeChild(_);Ii=Ii.filter((function(t){return t!==y})),y.state.isMounted=!1,D("onHidden",[y])},destroy:function(){0;if(y.state.isDestroyed)return;y.clearDelayTimeouts(),y.unmount(),U(),delete t._tippy,y.state.isDestroyed=!0,D("onDestroy",[y])}};if(!h.render)return y;var x=h.render(y),_=x.popper,w=x.onUpdate;_.setAttribute("data-tippy-root",""),_.id="tippy-"+y.id,y.popper=_,t._tippy=y,_._tippy=y;var k=v.map((function(t){return t.fn(y)})),O=t.hasAttribute("aria-expanded");return $(),I(),C(),D("onCreate",[y]),h.showOnCreate&&tt(),_.addEventListener("mouseenter",(function(){y.props.interactive&&y.state.isVisible&&y.clearDelayTimeouts()})),_.addEventListener("mouseleave",(function(){y.props.interactive&&y.props.trigger.indexOf("mouseenter")>=0&&T().addEventListener("mousemove",m)})),y;function S(){var t=y.props.touch;return Array.isArray(t)?t:[t,0]}function M(){return"hold"===S()[0]}function E(){var t;return!(null==(t=y.props.render)||!t.$$tippy)}function P(){return l||t}function T(){var t=P().parentNode;return t?fi(t):document}function L(){return Ai(_)}function A(t){return y.state.isMounted&&!y.state.isVisible||mi.isTouch||o&&"focus"===o.type?0:Je(y.props.delay,t?0:1,ki.delay)}function C(t){void 0===t&&(t=!1),_.style.pointerEvents=y.props.interactive&&!t?"":"none",_.style.zIndex=""+y.props.zIndex}function D(t,e,i){var n;(void 0===i&&(i=!0),k.forEach((function(i){i[t]&&i[t].apply(i,e)})),i)&&(n=y.props)[t].apply(n,e)}function j(){var e=y.props.aria;if(e.content){var i="aria-"+e.content,n=_.id;ei(y.props.triggerTarget||t).forEach((function(t){var e=t.getAttribute(i);if(y.state.isVisible)t.setAttribute(i,e?e+" "+n:n);else{var r=e&&e.replace(n,"").trim();r?t.setAttribute(i,r):t.removeAttribute(i)}}))}}function I(){!O&&y.props.aria.expanded&&ei(y.props.triggerTarget||t).forEach((function(t){y.props.interactive?t.setAttribute("aria-expanded",y.state.isVisible&&t===P()?"true":"false"):t.removeAttribute("aria-expanded")}))}function R(){T().removeEventListener("mousemove",m),ji=ji.filter((function(t){return t!==m}))}function F(e){if(!mi.isTouch||!f&&"mousedown"!==e.type){var i=e.composedPath&&e.composedPath()[0]||e.target;if(!y.props.interactive||!gi(_,i)){if(ei(y.props.triggerTarget||t).some((function(t){return gi(t,i)}))){if(mi.isTouch)return;if(y.state.isVisible&&y.props.trigger.indexOf("click")>=0)return}else D("onClickOutside",[y,e]);!0===y.props.hideOnClick&&(y.clearDelayTimeouts(),y.hide(),d=!0,setTimeout((function(){d=!1})),y.state.isMounted||W())}}}function z(){f=!0}function N(){f=!1}function B(){var t=T();t.addEventListener("mousedown",F,!0),t.addEventListener("touchend",F,Ge),t.addEventListener("touchstart",N,Ge),t.addEventListener("touchmove",z,Ge)}function W(){var t=T();t.removeEventListener("mousedown",F,!0),t.removeEventListener("touchend",F,Ge),t.removeEventListener("touchstart",N,Ge),t.removeEventListener("touchmove",z,Ge)}function V(t,e){var i=L().box;function n(t){t.target===i&&(pi(i,"remove",n),e())}if(0===t)return e();pi(i,"remove",s),pi(i,"add",n),s=n}function H(e,i,n){void 0===n&&(n=!1),ei(y.props.triggerTarget||t).forEach((function(t){t.addEventListener(e,i,n),g.push({node:t,eventType:e,handler:i,options:n})}))}function $(){var t;M()&&(H("touchstart",q,{passive:!0}),H("touchend",X,{passive:!0})),(t=y.props.trigger,t.split(/\s+/).filter(Boolean)).forEach((function(t){if("manual"!==t)switch(H(t,q),t){case"mouseenter":H("mouseleave",X);break;case"focus":H(_i?"focusout":"blur",G);break;case"focusin":H("focusout",G)}}))}function U(){g.forEach((function(t){var e=t.node,i=t.eventType,n=t.handler,r=t.options;e.removeEventListener(i,n,r)})),g=[]}function q(t){var e,i=!1;if(y.state.isEnabled&&!K(t)&&!d){var n="focus"===(null==(e=o)?void 0:e.type);o=t,l=t.currentTarget,I(),!y.state.isVisible&&li(t)&&ji.forEach((function(e){return e(t)})),"click"===t.type&&(y.props.trigger.indexOf("mouseenter")<0||u)&&!1!==y.props.hideOnClick&&y.state.isVisible?i=!0:tt(t),"click"===t.type&&(u=!i),i&&!n&&et(t)}}function Y(t){var e=t.target,i=P().contains(e)||_.contains(e);if("mousemove"!==t.type||!i){var n=Z().concat(_).map((function(t){var e,i=null==(e=t._tippy.popperInstance)?void 0:e.state;return i?{popperRect:t.getBoundingClientRect(),popperState:i,props:h}:null})).filter(Boolean);(function(t,e){var i=e.clientX,n=e.clientY;return t.every((function(t){var e=t.popperRect,r=t.popperState,o=t.props.interactiveBorder,s=ni(r.placement),a=r.modifiersData.offset;if(!a)return!0;var l="bottom"===s?a.top.y:0,c="top"===s?a.bottom.y:0,h="right"===s?a.left.x:0,u="left"===s?a.right.x:0,d=e.top-n+l>o,f=n-e.bottom-c>o,p=e.left-i+h>o,g=i-e.right-u>o;return d||f||p||g}))})(n,t)&&(R(),et(t))}}function X(t){K(t)||y.props.trigger.indexOf("click")>=0&&u||(y.props.interactive?y.hideWithInteractivity(t):et(t))}function G(t){y.props.trigger.indexOf("focusin")<0&&t.target!==P()||y.props.interactive&&t.relatedTarget&&_.contains(t.relatedTarget)||et(t)}function K(t){return!!mi.isTouch&&M()!==t.type.indexOf("touch")>=0}function J(){Q();var e=y.props,i=e.popperOptions,n=e.placement,r=e.offset,o=e.getReferenceClientRect,s=e.moveTransition,l=E()?Ai(_).arrow:null,c=o?{getBoundingClientRect:o,contextElement:o.contextElement||P()}:t,h={name:"$$tippy",enabled:!0,phase:"beforeWrite",requires:["computeStyles"],fn:function(t){var e=t.state;if(E()){var i=L().box;["placement","reference-hidden","escaped"].forEach((function(t){"placement"===t?i.setAttribute("data-placement",e.placement):e.attributes.popper["data-popper-"+t]?i.setAttribute("data-"+t,""):i.removeAttribute("data-"+t)})),e.attributes.popper={}}}},u=[{name:"offset",options:{offset:r}},{name:"preventOverflow",options:{padding:{top:2,bottom:2,left:5,right:5}}},{name:"flip",options:{padding:5}},{name:"computeStyles",options:{adaptive:!s}},h];E()&&l&&u.push({name:"arrow",options:{element:l,padding:3}}),u.push.apply(u,(null==i?void 0:i.modifiers)||[]),y.popperInstance=$e(c,_,Object.assign({},i,{placement:n,onFirstUpdate:a,modifiers:u}))}function Q(){y.popperInstance&&(y.popperInstance.destroy(),y.popperInstance=null)}function Z(){return ri(_.querySelectorAll("[data-tippy-root]"))}function tt(t){y.clearDelayTimeouts(),t&&D("onTrigger",[y,t]),B();var e=A(!0),n=S(),r=n[0],o=n[1];mi.isTouch&&"hold"===r&&o&&(e=o),e?i=setTimeout((function(){y.show()}),e):y.show()}function et(t){if(y.clearDelayTimeouts(),D("onUntrigger",[y,t]),y.state.isVisible){if(!(y.props.trigger.indexOf("mouseenter")>=0&&y.props.trigger.indexOf("click")>=0&&["mouseleave","mousemove"].indexOf(t.type)>=0&&u)){var e=A(!1);e?n=setTimeout((function(){y.state.isVisible&&y.hide()}),e):r=requestAnimationFrame((function(){y.hide()}))}}else W()}}function Fi(t,e){void 0===e&&(e={});var i=ki.plugins.concat(e.plugins||[]);document.addEventListener("touchstart",vi,Ge),window.addEventListener("blur",xi);var n=Object.assign({},e,{plugins:i}),r=hi(t).reduce((function(t,e){var i=e&&Ri(e,n);return i&&t.push(i),t}),[]);return ai(t)?r[0]:r}Fi.defaultProps=ki,Fi.setDefaultProps=function(t){Object.keys(t).forEach((function(e){ki[e]=t[e]}))},Fi.currentInput=mi;Object.assign({},Me,{effect:function(t){var e=t.state,i={popper:{position:e.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};Object.assign(e.elements.popper.style,i.popper),e.styles=i,e.elements.arrow&&Object.assign(e.elements.arrow.style,i.arrow)}});Fi.setDefaultProps({render:Ci});var zi=Fi,Ni=i(951),Bi=i.n(Ni);const Wi={controlled:null,bind(t){this.controlled=t,this.controlled.forEach((t=>{this._main(t)})),this._init()},_init(){this.controlled.forEach((t=>{this._checkUp(t)}))},_main(t){const e=JSON.parse(t.dataset.main);t.dataset.size&&(t.filesize=parseInt(t.dataset.size,10)),t.mains=e.map((e=>{const i=document.getElementById(e),n=document.getElementById(e+"_size_wrapper");return n&&(i.filesize=0,i.sizespan=n),this._addChild(i,t),i})),this._bindEvents(t),t.mains.forEach((t=>{this._bindEvents(t)}))},_bindEvents(t){t.eventBound||(t.addEventListener("click",(e=>{const i=e.target;i.elements&&(this._checkDown(i),this._evaluateSize(i)),i.mains&&this._checkUp(t)})),t.eventBound=!0)},_addChild(t,e){const i=t.elements?t.elements:[];-1===i.indexOf(e)&&(i.push(e),t.elements=i)},_removeChild(t,e){const i=t.elements.indexOf(e);-1<i&&t.elements.splice(i,1)},_checkDown(t){t.elements&&(t.classList.remove("partial"),t.elements.forEach((e=>{e.checked!==t.checked&&(e.checked=t.checked,e.disabled&&(e.checked=!1),e.dispatchEvent(new Event("change")))})),t.elements.forEach((e=>{this._checkDown(e),e.elements||this._checkUp(e,t)})))},_checkUp(t,e){t.mains&&[...t.mains].forEach((t=>{t!==e&&this._evaluateCheckStatus(t),this._checkUp(t),this._evaluateSize(t)}))},_evaluateCheckStatus(t){let e=0,i=t.classList.contains("partial");i&&(t.classList.remove("partial"),i=!1),t.elements.forEach((n=>{null!==n.parentNode?(e+=n.checked,n.classList.contains("partial")&&(i=!0)):this._removeChild(t,n)}));let n="some";e===t.elements.length?n="on":0===e?n="off":i=!0,i&&t.classList.add("partial");const r="off"!==n;t.checked===r&&t.value===n||(t.value=n,t.checked=r,t.dispatchEvent(new Event("change")))},_evaluateSize(t){if(t.sizespan&&t.elements){t.filesize=0,t.elements.forEach((e=>{e.checked&&(t.filesize+=e.filesize)}));let e=null;0<t.filesize&&(e=Bi()(t.filesize,{spacer:" "}).human("jedec")),t.sizespan.innerText=e}}};var Vi=Wi,Hi=i(941),$i=i.n(Hi);function Ui(){}const qi=function(){let t=0;return function(){return t++}}();function Yi(t){return null==t}function Xi(t){if(Array.isArray&&Array.isArray(t))return!0;const e=Object.prototype.toString.call(t);return"[object"===e.slice(0,7)&&"Array]"===e.slice(-6)}function Gi(t){return null!==t&&"[object Object]"===Object.prototype.toString.call(t)}const Ki=t=>("number"==typeof t||t instanceof Number)&&isFinite(+t);function Ji(t,e){return Ki(t)?t:e}function Qi(t,e){return void 0===t?e:t}const Zi=(t,e)=>"string"==typeof t&&t.endsWith("%")?parseFloat(t)/100*e:+t;function tn(t,e,i){if(t&&"function"==typeof t.call)return t.apply(i,e)}function en(t,e,i,n){let r,o,s;if(Xi(t))if(o=t.length,n)for(r=o-1;r>=0;r--)e.call(i,t[r],r);else for(r=0;r<o;r++)e.call(i,t[r],r);else if(Gi(t))for(s=Object.keys(t),o=s.length,r=0;r<o;r++)e.call(i,t[s[r]],s[r])}function nn(t,e){let i,n,r,o;if(!t||!e||t.length!==e.length)return!1;for(i=0,n=t.length;i<n;++i)if(r=t[i],o=e[i],r.datasetIndex!==o.datasetIndex||r.index!==o.index)return!1;return!0}function rn(t){if(Xi(t))return t.map(rn);if(Gi(t)){const e=Object.create(null),i=Object.keys(t),n=i.length;let r=0;for(;r<n;++r)e[i[r]]=rn(t[i[r]]);return e}return t}function on(t){return-1===["__proto__","prototype","constructor"].indexOf(t)}function sn(t,e,i,n){if(!on(t))return;const r=e[t],o=i[t];Gi(r)&&Gi(o)?an(r,o,n):e[t]=rn(o)}function an(t,e,i){const n=Xi(e)?e:[e],r=n.length;if(!Gi(t))return t;const o=(i=i||{}).merger||sn;for(let s=0;s<r;++s){if(!Gi(e=n[s]))continue;const r=Object.keys(e);for(let n=0,s=r.length;n<s;++n)o(r[n],t,e,i)}return t}function ln(t,e){return an(t,e,{merger:cn})}function cn(t,e,i){if(!on(t))return;const n=e[t],r=i[t];Gi(n)&&Gi(r)?ln(n,r):Object.prototype.hasOwnProperty.call(e,t)||(e[t]=rn(r))}const hn={"":t=>t,x:t=>t.x,y:t=>t.y};function un(t,e){const i=hn[e]||(hn[e]=function(t){const e=function(t){const e=t.split("."),i=[];let n="";for(const t of e)n+=t,n.endsWith("\\")?n=n.slice(0,-1)+".":(i.push(n),n="");return i}(t);return t=>{for(const i of e){if(""===i)break;t=t&&t[i]}return t}}(e));return i(t)}function dn(t){return t.charAt(0).toUpperCase()+t.slice(1)}const fn=t=>void 0!==t,pn=t=>"function"==typeof t,gn=(t,e)=>{if(t.size!==e.size)return!1;for(const i of t)if(!e.has(i))return!1;return!0};const mn=Math.PI,bn=2*mn,vn=bn+mn,yn=Number.POSITIVE_INFINITY,xn=mn/180,_n=mn/2,wn=mn/4,kn=2*mn/3,On=Math.log10,Sn=Math.sign;function Mn(t){const e=Math.round(t);t=Pn(t,e,t/1e3)?e:t;const i=Math.pow(10,Math.floor(On(t))),n=t/i;return(n<=1?1:n<=2?2:n<=5?5:10)*i}function En(t){return!isNaN(parseFloat(t))&&isFinite(t)}function Pn(t,e,i){return Math.abs(t-e)<i}function Tn(t,e,i){let n,r,o;for(n=0,r=t.length;n<r;n++)o=t[n][i],isNaN(o)||(e.min=Math.min(e.min,o),e.max=Math.max(e.max,o))}function Ln(t){return t*(mn/180)}function An(t){return t*(180/mn)}function Cn(t){if(!Ki(t))return;let e=1,i=0;for(;Math.round(t*e)/e!==t;)e*=10,i++;return i}function Dn(t,e){const i=e.x-t.x,n=e.y-t.y,r=Math.sqrt(i*i+n*n);let o=Math.atan2(n,i);return o<-.5*mn&&(o+=bn),{angle:o,distance:r}}function jn(t,e){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))}function In(t,e){return(t-e+vn)%bn-mn}function Rn(t){return(t%bn+bn)%bn}function Fn(t,e,i,n){const r=Rn(t),o=Rn(e),s=Rn(i),a=Rn(o-r),l=Rn(s-r),c=Rn(r-o),h=Rn(r-s);return r===o||r===s||n&&o===s||a>l&&c<h}function zn(t,e,i){return Math.max(e,Math.min(i,t))}function Nn(t,e,i,n=1e-6){return t>=Math.min(e,i)-n&&t<=Math.max(e,i)+n}function Bn(t,e,i){i=i||(i=>t[i]<e);let n,r=t.length-1,o=0;for(;r-o>1;)n=o+r>>1,i(n)?o=n:r=n;return{lo:o,hi:r}}const Wn=(t,e,i,n)=>Bn(t,i,n?n=>t[n][e]<=i:n=>t[n][e]<i),Vn=(t,e,i)=>Bn(t,i,(n=>t[n][e]>=i));const Hn=["push","pop","shift","splice","unshift"];function $n(t,e){const i=t._chartjs;if(!i)return;const n=i.listeners,r=n.indexOf(e);-1!==r&&n.splice(r,1),n.length>0||(Hn.forEach((e=>{delete t[e]})),delete t._chartjs)}function Un(t){const e=new Set;let i,n;for(i=0,n=t.length;i<n;++i)e.add(t[i]);return e.size===n?t:Array.from(e)}const qn="undefined"==typeof window?function(t){return t()}:window.requestAnimationFrame;function Yn(t,e,i){const n=i||(t=>Array.prototype.slice.call(t));let r=!1,o=[];return function(...i){o=n(i),r||(r=!0,qn.call(window,(()=>{r=!1,t.apply(e,o)})))}}const Xn=t=>"start"===t?"left":"end"===t?"right":"center",Gn=(t,e,i)=>"start"===t?e:"end"===t?i:(e+i)/2;function Kn(t,e,i){const n=e.length;let r=0,o=n;if(t._sorted){const{iScale:s,_parsed:a}=t,l=s.axis,{min:c,max:h,minDefined:u,maxDefined:d}=s.getUserBounds();u&&(r=zn(Math.min(Wn(a,s.axis,c).lo,i?n:Wn(e,l,s.getPixelForValue(c)).lo),0,n-1)),o=d?zn(Math.max(Wn(a,s.axis,h,!0).hi+1,i?0:Wn(e,l,s.getPixelForValue(h),!0).hi+1),r,n)-r:n-r}return{start:r,count:o}}function Jn(t){const{xScale:e,yScale:i,_scaleRanges:n}=t,r={xmin:e.min,xmax:e.max,ymin:i.min,ymax:i.max};if(!n)return t._scaleRanges=r,!0;const o=n.xmin!==e.min||n.xmax!==e.max||n.ymin!==i.min||n.ymax!==i.max;return Object.assign(n,r),o}const Qn=t=>0===t||1===t,Zn=(t,e,i)=>-Math.pow(2,10*(t-=1))*Math.sin((t-e)*bn/i),tr=(t,e,i)=>Math.pow(2,-10*t)*Math.sin((t-e)*bn/i)+1,er={linear:t=>t,easeInQuad:t=>t*t,easeOutQuad:t=>-t*(t-2),easeInOutQuad:t=>(t/=.5)<1?.5*t*t:-.5*(--t*(t-2)-1),easeInCubic:t=>t*t*t,easeOutCubic:t=>(t-=1)*t*t+1,easeInOutCubic:t=>(t/=.5)<1?.5*t*t*t:.5*((t-=2)*t*t+2),easeInQuart:t=>t*t*t*t,easeOutQuart:t=>-((t-=1)*t*t*t-1),easeInOutQuart:t=>(t/=.5)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2),easeInQuint:t=>t*t*t*t*t,easeOutQuint:t=>(t-=1)*t*t*t*t+1,easeInOutQuint:t=>(t/=.5)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2),easeInSine:t=>1-Math.cos(t*_n),easeOutSine:t=>Math.sin(t*_n),easeInOutSine:t=>-.5*(Math.cos(mn*t)-1),easeInExpo:t=>0===t?0:Math.pow(2,10*(t-1)),easeOutExpo:t=>1===t?1:1-Math.pow(2,-10*t),easeInOutExpo:t=>Qn(t)?t:t<.5?.5*Math.pow(2,10*(2*t-1)):.5*(2-Math.pow(2,-10*(2*t-1))),easeInCirc:t=>t>=1?t:-(Math.sqrt(1-t*t)-1),easeOutCirc:t=>Math.sqrt(1-(t-=1)*t),easeInOutCirc:t=>(t/=.5)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1),easeInElastic:t=>Qn(t)?t:Zn(t,.075,.3),easeOutElastic:t=>Qn(t)?t:tr(t,.075,.3),easeInOutElastic(t){const e=.1125;return Qn(t)?t:t<.5?.5*Zn(2*t,e,.45):.5+.5*tr(2*t-1,e,.45)},easeInBack(t){const e=1.70158;return t*t*((e+1)*t-e)},easeOutBack(t){const e=1.70158;return(t-=1)*t*((e+1)*t+e)+1},easeInOutBack(t){let e=1.70158;return(t/=.5)<1?t*t*((1+(e*=1.525))*t-e)*.5:.5*((t-=2)*t*((1+(e*=1.525))*t+e)+2)},easeInBounce:t=>1-er.easeOutBounce(1-t),easeOutBounce(t){const e=7.5625,i=2.75;return t<1/i?e*t*t:t<2/i?e*(t-=1.5/i)*t+.75:t<2.5/i?e*(t-=2.25/i)*t+.9375:e*(t-=2.625/i)*t+.984375},easeInOutBounce:t=>t<.5?.5*er.easeInBounce(2*t):.5*er.easeOutBounce(2*t-1)+.5};function ir(t){return t+.5|0}const nr=(t,e,i)=>Math.max(Math.min(t,i),e);function rr(t){return nr(ir(2.55*t),0,255)}function or(t){return nr(ir(255*t),0,255)}function sr(t){return nr(ir(t/2.55)/100,0,1)}function ar(t){return nr(ir(100*t),0,100)}const lr={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,A:10,B:11,C:12,D:13,E:14,F:15,a:10,b:11,c:12,d:13,e:14,f:15},cr=[..."0123456789ABCDEF"],hr=t=>cr[15&t],ur=t=>cr[(240&t)>>4]+cr[15&t],dr=t=>(240&t)>>4==(15&t);function fr(t){var e=(t=>dr(t.r)&&dr(t.g)&&dr(t.b)&&dr(t.a))(t)?hr:ur;return t?"#"+e(t.r)+e(t.g)+e(t.b)+((t,e)=>t<255?e(t):"")(t.a,e):void 0}const pr=/^(hsla?|hwb|hsv)\(\s*([-+.e\d]+)(?:deg)?[\s,]+([-+.e\d]+)%[\s,]+([-+.e\d]+)%(?:[\s,]+([-+.e\d]+)(%)?)?\s*\)$/;function gr(t,e,i){const n=e*Math.min(i,1-i),r=(e,r=(e+t/30)%12)=>i-n*Math.max(Math.min(r-3,9-r,1),-1);return[r(0),r(8),r(4)]}function mr(t,e,i){const n=(n,r=(n+t/60)%6)=>i-i*e*Math.max(Math.min(r,4-r,1),0);return[n(5),n(3),n(1)]}function br(t,e,i){const n=gr(t,1,.5);let r;for(e+i>1&&(r=1/(e+i),e*=r,i*=r),r=0;r<3;r++)n[r]*=1-e-i,n[r]+=e;return n}function vr(t){const e=t.r/255,i=t.g/255,n=t.b/255,r=Math.max(e,i,n),o=Math.min(e,i,n),s=(r+o)/2;let a,l,c;return r!==o&&(c=r-o,l=s>.5?c/(2-r-o):c/(r+o),a=function(t,e,i,n,r){return t===r?(e-i)/n+(e<i?6:0):e===r?(i-t)/n+2:(t-e)/n+4}(e,i,n,c,r),a=60*a+.5),[0|a,l||0,s]}function yr(t,e,i,n){return(Array.isArray(e)?t(e[0],e[1],e[2]):t(e,i,n)).map(or)}function xr(t,e,i){return yr(gr,t,e,i)}function _r(t){return(t%360+360)%360}function wr(t){const e=pr.exec(t);let i,n=255;if(!e)return;e[5]!==i&&(n=e[6]?rr(+e[5]):or(+e[5]));const r=_r(+e[2]),o=+e[3]/100,s=+e[4]/100;return i="hwb"===e[1]?function(t,e,i){return yr(br,t,e,i)}(r,o,s):"hsv"===e[1]?function(t,e,i){return yr(mr,t,e,i)}(r,o,s):xr(r,o,s),{r:i[0],g:i[1],b:i[2],a:n}}const kr={x:"dark",Z:"light",Y:"re",X:"blu",W:"gr",V:"medium",U:"slate",A:"ee",T:"ol",S:"or",B:"ra",C:"lateg",D:"ights",R:"in",Q:"turquois",E:"hi",P:"ro",O:"al",N:"le",M:"de",L:"yello",F:"en",K:"ch",G:"arks",H:"ea",I:"ightg",J:"wh"},Or={OiceXe:"f0f8ff",antiquewEte:"faebd7",aqua:"ffff",aquamarRe:"7fffd4",azuY:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"0",blanKedOmond:"ffebcd",Xe:"ff",XeviTet:"8a2be2",bPwn:"a52a2a",burlywood:"deb887",caMtXe:"5f9ea0",KartYuse:"7fff00",KocTate:"d2691e",cSO:"ff7f50",cSnflowerXe:"6495ed",cSnsilk:"fff8dc",crimson:"dc143c",cyan:"ffff",xXe:"8b",xcyan:"8b8b",xgTMnPd:"b8860b",xWay:"a9a9a9",xgYF:"6400",xgYy:"a9a9a9",xkhaki:"bdb76b",xmagFta:"8b008b",xTivegYF:"556b2f",xSange:"ff8c00",xScEd:"9932cc",xYd:"8b0000",xsOmon:"e9967a",xsHgYF:"8fbc8f",xUXe:"483d8b",xUWay:"2f4f4f",xUgYy:"2f4f4f",xQe:"ced1",xviTet:"9400d3",dAppRk:"ff1493",dApskyXe:"bfff",dimWay:"696969",dimgYy:"696969",dodgerXe:"1e90ff",fiYbrick:"b22222",flSOwEte:"fffaf0",foYstWAn:"228b22",fuKsia:"ff00ff",gaRsbSo:"dcdcdc",ghostwEte:"f8f8ff",gTd:"ffd700",gTMnPd:"daa520",Way:"808080",gYF:"8000",gYFLw:"adff2f",gYy:"808080",honeyMw:"f0fff0",hotpRk:"ff69b4",RdianYd:"cd5c5c",Rdigo:"4b0082",ivSy:"fffff0",khaki:"f0e68c",lavFMr:"e6e6fa",lavFMrXsh:"fff0f5",lawngYF:"7cfc00",NmoncEffon:"fffacd",ZXe:"add8e6",ZcSO:"f08080",Zcyan:"e0ffff",ZgTMnPdLw:"fafad2",ZWay:"d3d3d3",ZgYF:"90ee90",ZgYy:"d3d3d3",ZpRk:"ffb6c1",ZsOmon:"ffa07a",ZsHgYF:"20b2aa",ZskyXe:"87cefa",ZUWay:"778899",ZUgYy:"778899",ZstAlXe:"b0c4de",ZLw:"ffffe0",lime:"ff00",limegYF:"32cd32",lRF:"faf0e6",magFta:"ff00ff",maPon:"800000",VaquamarRe:"66cdaa",VXe:"cd",VScEd:"ba55d3",VpurpN:"9370db",VsHgYF:"3cb371",VUXe:"7b68ee",VsprRggYF:"fa9a",VQe:"48d1cc",VviTetYd:"c71585",midnightXe:"191970",mRtcYam:"f5fffa",mistyPse:"ffe4e1",moccasR:"ffe4b5",navajowEte:"ffdead",navy:"80",Tdlace:"fdf5e6",Tive:"808000",TivedBb:"6b8e23",Sange:"ffa500",SangeYd:"ff4500",ScEd:"da70d6",pOegTMnPd:"eee8aa",pOegYF:"98fb98",pOeQe:"afeeee",pOeviTetYd:"db7093",papayawEp:"ffefd5",pHKpuff:"ffdab9",peru:"cd853f",pRk:"ffc0cb",plum:"dda0dd",powMrXe:"b0e0e6",purpN:"800080",YbeccapurpN:"663399",Yd:"ff0000",Psybrown:"bc8f8f",PyOXe:"4169e1",saddNbPwn:"8b4513",sOmon:"fa8072",sandybPwn:"f4a460",sHgYF:"2e8b57",sHshell:"fff5ee",siFna:"a0522d",silver:"c0c0c0",skyXe:"87ceeb",UXe:"6a5acd",UWay:"708090",UgYy:"708090",snow:"fffafa",sprRggYF:"ff7f",stAlXe:"4682b4",tan:"d2b48c",teO:"8080",tEstN:"d8bfd8",tomato:"ff6347",Qe:"40e0d0",viTet:"ee82ee",JHt:"f5deb3",wEte:"ffffff",wEtesmoke:"f5f5f5",Lw:"ffff00",LwgYF:"9acd32"};let Sr;function Mr(t){Sr||(Sr=function(){const t={},e=Object.keys(Or),i=Object.keys(kr);let n,r,o,s,a;for(n=0;n<e.length;n++){for(s=a=e[n],r=0;r<i.length;r++)o=i[r],a=a.replace(o,kr[o]);o=parseInt(Or[s],16),t[a]=[o>>16&255,o>>8&255,255&o]}return t}(),Sr.transparent=[0,0,0,0]);const e=Sr[t.toLowerCase()];return e&&{r:e[0],g:e[1],b:e[2],a:4===e.length?e[3]:255}}const Er=/^rgba?\(\s*([-+.\d]+)(%)?[\s,]+([-+.e\d]+)(%)?[\s,]+([-+.e\d]+)(%)?(?:[\s,/]+([-+.e\d]+)(%)?)?\s*\)$/;const Pr=t=>t<=.0031308?12.92*t:1.055*Math.pow(t,1/2.4)-.055,Tr=t=>t<=.04045?t/12.92:Math.pow((t+.055)/1.055,2.4);function Lr(t,e,i){if(t){let n=vr(t);n[e]=Math.max(0,Math.min(n[e]+n[e]*i,0===e?360:1)),n=xr(n),t.r=n[0],t.g=n[1],t.b=n[2]}}function Ar(t,e){return t?Object.assign(e||{},t):t}function Cr(t){var e={r:0,g:0,b:0,a:255};return Array.isArray(t)?t.length>=3&&(e={r:t[0],g:t[1],b:t[2],a:255},t.length>3&&(e.a=or(t[3]))):(e=Ar(t,{r:0,g:0,b:0,a:1})).a=or(e.a),e}function Dr(t){return"r"===t.charAt(0)?function(t){const e=Er.exec(t);let i,n,r,o=255;if(e){if(e[7]!==i){const t=+e[7];o=e[8]?rr(t):nr(255*t,0,255)}return i=+e[1],n=+e[3],r=+e[5],i=255&(e[2]?rr(i):nr(i,0,255)),n=255&(e[4]?rr(n):nr(n,0,255)),r=255&(e[6]?rr(r):nr(r,0,255)),{r:i,g:n,b:r,a:o}}}(t):wr(t)}class jr{constructor(t){if(t instanceof jr)return t;const e=typeof t;let i;var n,r,o;"object"===e?i=Cr(t):"string"===e&&(o=(n=t).length,"#"===n[0]&&(4===o||5===o?r={r:255&17*lr[n[1]],g:255&17*lr[n[2]],b:255&17*lr[n[3]],a:5===o?17*lr[n[4]]:255}:7!==o&&9!==o||(r={r:lr[n[1]]<<4|lr[n[2]],g:lr[n[3]]<<4|lr[n[4]],b:lr[n[5]]<<4|lr[n[6]],a:9===o?lr[n[7]]<<4|lr[n[8]]:255})),i=r||Mr(t)||Dr(t)),this._rgb=i,this._valid=!!i}get valid(){return this._valid}get rgb(){var t=Ar(this._rgb);return t&&(t.a=sr(t.a)),t}set rgb(t){this._rgb=Cr(t)}rgbString(){return this._valid?(t=this._rgb)&&(t.a<255?`rgba(${t.r}, ${t.g}, ${t.b}, ${sr(t.a)})`:`rgb(${t.r}, ${t.g}, ${t.b})`):void 0;var t}hexString(){return this._valid?fr(this._rgb):void 0}hslString(){return this._valid?function(t){if(!t)return;const e=vr(t),i=e[0],n=ar(e[1]),r=ar(e[2]);return t.a<255?`hsla(${i}, ${n}%, ${r}%, ${sr(t.a)})`:`hsl(${i}, ${n}%, ${r}%)`}(this._rgb):void 0}mix(t,e){if(t){const i=this.rgb,n=t.rgb;let r;const o=e===r?.5:e,s=2*o-1,a=i.a-n.a,l=((s*a==-1?s:(s+a)/(1+s*a))+1)/2;r=1-l,i.r=255&l*i.r+r*n.r+.5,i.g=255&l*i.g+r*n.g+.5,i.b=255&l*i.b+r*n.b+.5,i.a=o*i.a+(1-o)*n.a,this.rgb=i}return this}interpolate(t,e){return t&&(this._rgb=function(t,e,i){const n=Tr(sr(t.r)),r=Tr(sr(t.g)),o=Tr(sr(t.b));return{r:or(Pr(n+i*(Tr(sr(e.r))-n))),g:or(Pr(r+i*(Tr(sr(e.g))-r))),b:or(Pr(o+i*(Tr(sr(e.b))-o))),a:t.a+i*(e.a-t.a)}}(this._rgb,t._rgb,e)),this}clone(){return new jr(this.rgb)}alpha(t){return this._rgb.a=or(t),this}clearer(t){return this._rgb.a*=1-t,this}greyscale(){const t=this._rgb,e=ir(.3*t.r+.59*t.g+.11*t.b);return t.r=t.g=t.b=e,this}opaquer(t){return this._rgb.a*=1+t,this}negate(){const t=this._rgb;return t.r=255-t.r,t.g=255-t.g,t.b=255-t.b,this}lighten(t){return Lr(this._rgb,2,t),this}darken(t){return Lr(this._rgb,2,-t),this}saturate(t){return Lr(this._rgb,1,t),this}desaturate(t){return Lr(this._rgb,1,-t),this}rotate(t){return function(t,e){var i=vr(t);i[0]=_r(i[0]+e),i=xr(i),t.r=i[0],t.g=i[1],t.b=i[2]}(this._rgb,t),this}}function Ir(t){return new jr(t)}function Rr(t){if(t&&"object"==typeof t){const e=t.toString();return"[object CanvasPattern]"===e||"[object CanvasGradient]"===e}return!1}function Fr(t){return Rr(t)?t:Ir(t)}function zr(t){return Rr(t)?t:Ir(t).saturate(.5).darken(.1).hexString()}const Nr=Object.create(null),Br=Object.create(null);function Wr(t,e){if(!e)return t;const i=e.split(".");for(let e=0,n=i.length;e<n;++e){const n=i[e];t=t[n]||(t[n]=Object.create(null))}return t}function Vr(t,e,i){return"string"==typeof e?an(Wr(t,e),i):an(Wr(t,""),e)}var Hr=new class{constructor(t){this.animation=void 0,this.backgroundColor="rgba(0,0,0,0.1)",this.borderColor="rgba(0,0,0,0.1)",this.color="#666",this.datasets={},this.devicePixelRatio=t=>t.chart.platform.getDevicePixelRatio(),this.elements={},this.events=["mousemove","mouseout","click","touchstart","touchmove"],this.font={family:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",size:12,style:"normal",lineHeight:1.2,weight:null},this.hover={},this.hoverBackgroundColor=(t,e)=>zr(e.backgroundColor),this.hoverBorderColor=(t,e)=>zr(e.borderColor),this.hoverColor=(t,e)=>zr(e.color),this.indexAxis="x",this.interaction={mode:"nearest",intersect:!0,includeInvisible:!1},this.maintainAspectRatio=!0,this.onHover=null,this.onClick=null,this.parsing=!0,this.plugins={},this.responsive=!0,this.scale=void 0,this.scales={},this.showLine=!0,this.drawActiveElementsOnTop=!0,this.describe(t)}set(t,e){return Vr(this,t,e)}get(t){return Wr(this,t)}describe(t,e){return Vr(Br,t,e)}override(t,e){return Vr(Nr,t,e)}route(t,e,i,n){const r=Wr(this,t),o=Wr(this,i),s="_"+e;Object.defineProperties(r,{[s]:{value:r[e],writable:!0},[e]:{enumerable:!0,get(){const t=this[s],e=o[n];return Gi(t)?Object.assign({},e,t):Qi(t,e)},set(t){this[s]=t}}})}}({_scriptable:t=>!t.startsWith("on"),_indexable:t=>"events"!==t,hover:{_fallback:"interaction"},interaction:{_scriptable:!1,_indexable:!1}});function $r(t,e,i,n,r){let o=e[r];return o||(o=e[r]=t.measureText(r).width,i.push(r)),o>n&&(n=o),n}function Ur(t,e,i,n){let r=(n=n||{}).data=n.data||{},o=n.garbageCollect=n.garbageCollect||[];n.font!==e&&(r=n.data={},o=n.garbageCollect=[],n.font=e),t.save(),t.font=e;let s=0;const a=i.length;let l,c,h,u,d;for(l=0;l<a;l++)if(u=i[l],null!=u&&!0!==Xi(u))s=$r(t,r,o,s,u);else if(Xi(u))for(c=0,h=u.length;c<h;c++)d=u[c],null==d||Xi(d)||(s=$r(t,r,o,s,d));t.restore();const f=o.length/2;if(f>i.length){for(l=0;l<f;l++)delete r[o[l]];o.splice(0,f)}return s}function qr(t,e,i){const n=t.currentDevicePixelRatio,r=0!==i?Math.max(i/2,.5):0;return Math.round((e-r)*n)/n+r}function Yr(t,e){(e=e||t.getContext("2d")).save(),e.resetTransform(),e.clearRect(0,0,t.width,t.height),e.restore()}function Xr(t,e,i,n){Gr(t,e,i,n,null)}function Gr(t,e,i,n,r){let o,s,a,l,c,h;const u=e.pointStyle,d=e.rotation,f=e.radius;let p=(d||0)*xn;if(u&&"object"==typeof u&&(o=u.toString(),"[object HTMLImageElement]"===o||"[object HTMLCanvasElement]"===o))return t.save(),t.translate(i,n),t.rotate(p),t.drawImage(u,-u.width/2,-u.height/2,u.width,u.height),void t.restore();if(!(isNaN(f)||f<=0)){switch(t.beginPath(),u){default:r?t.ellipse(i,n,r/2,f,0,0,bn):t.arc(i,n,f,0,bn),t.closePath();break;case"triangle":t.moveTo(i+Math.sin(p)*f,n-Math.cos(p)*f),p+=kn,t.lineTo(i+Math.sin(p)*f,n-Math.cos(p)*f),p+=kn,t.lineTo(i+Math.sin(p)*f,n-Math.cos(p)*f),t.closePath();break;case"rectRounded":c=.516*f,l=f-c,s=Math.cos(p+wn)*l,a=Math.sin(p+wn)*l,t.arc(i-s,n-a,c,p-mn,p-_n),t.arc(i+a,n-s,c,p-_n,p),t.arc(i+s,n+a,c,p,p+_n),t.arc(i-a,n+s,c,p+_n,p+mn),t.closePath();break;case"rect":if(!d){l=Math.SQRT1_2*f,h=r?r/2:l,t.rect(i-h,n-l,2*h,2*l);break}p+=wn;case"rectRot":s=Math.cos(p)*f,a=Math.sin(p)*f,t.moveTo(i-s,n-a),t.lineTo(i+a,n-s),t.lineTo(i+s,n+a),t.lineTo(i-a,n+s),t.closePath();break;case"crossRot":p+=wn;case"cross":s=Math.cos(p)*f,a=Math.sin(p)*f,t.moveTo(i-s,n-a),t.lineTo(i+s,n+a),t.moveTo(i+a,n-s),t.lineTo(i-a,n+s);break;case"star":s=Math.cos(p)*f,a=Math.sin(p)*f,t.moveTo(i-s,n-a),t.lineTo(i+s,n+a),t.moveTo(i+a,n-s),t.lineTo(i-a,n+s),p+=wn,s=Math.cos(p)*f,a=Math.sin(p)*f,t.moveTo(i-s,n-a),t.lineTo(i+s,n+a),t.moveTo(i+a,n-s),t.lineTo(i-a,n+s);break;case"line":s=r?r/2:Math.cos(p)*f,a=Math.sin(p)*f,t.moveTo(i-s,n-a),t.lineTo(i+s,n+a);break;case"dash":t.moveTo(i,n),t.lineTo(i+Math.cos(p)*f,n+Math.sin(p)*f)}t.fill(),e.borderWidth>0&&t.stroke()}}function Kr(t,e,i){return i=i||.5,!e||t&&t.x>e.left-i&&t.x<e.right+i&&t.y>e.top-i&&t.y<e.bottom+i}function Jr(t,e){t.save(),t.beginPath(),t.rect(e.left,e.top,e.right-e.left,e.bottom-e.top),t.clip()}function Qr(t){t.restore()}function Zr(t,e,i,n,r){if(!e)return t.lineTo(i.x,i.y);if("middle"===r){const n=(e.x+i.x)/2;t.lineTo(n,e.y),t.lineTo(n,i.y)}else"after"===r!=!!n?t.lineTo(e.x,i.y):t.lineTo(i.x,e.y);t.lineTo(i.x,i.y)}function to(t,e,i,n){if(!e)return t.lineTo(i.x,i.y);t.bezierCurveTo(n?e.cp1x:e.cp2x,n?e.cp1y:e.cp2y,n?i.cp2x:i.cp1x,n?i.cp2y:i.cp1y,i.x,i.y)}function eo(t,e,i,n,r,o={}){const s=Xi(e)?e:[e],a=o.strokeWidth>0&&""!==o.strokeColor;let l,c;for(t.save(),t.font=r.string,function(t,e){e.translation&&t.translate(e.translation[0],e.translation[1]);Yi(e.rotation)||t.rotate(e.rotation);e.color&&(t.fillStyle=e.color);e.textAlign&&(t.textAlign=e.textAlign);e.textBaseline&&(t.textBaseline=e.textBaseline)}(t,o),l=0;l<s.length;++l)c=s[l],a&&(o.strokeColor&&(t.strokeStyle=o.strokeColor),Yi(o.strokeWidth)||(t.lineWidth=o.strokeWidth),t.strokeText(c,i,n,o.maxWidth)),t.fillText(c,i,n,o.maxWidth),io(t,i,n,c,o),n+=r.lineHeight;t.restore()}function io(t,e,i,n,r){if(r.strikethrough||r.underline){const o=t.measureText(n),s=e-o.actualBoundingBoxLeft,a=e+o.actualBoundingBoxRight,l=i-o.actualBoundingBoxAscent,c=i+o.actualBoundingBoxDescent,h=r.strikethrough?(l+c)/2:c;t.strokeStyle=t.fillStyle,t.beginPath(),t.lineWidth=r.decorationWidth||2,t.moveTo(s,h),t.lineTo(a,h),t.stroke()}}function no(t,e){const{x:i,y:n,w:r,h:o,radius:s}=e;t.arc(i+s.topLeft,n+s.topLeft,s.topLeft,-_n,mn,!0),t.lineTo(i,n+o-s.bottomLeft),t.arc(i+s.bottomLeft,n+o-s.bottomLeft,s.bottomLeft,mn,_n,!0),t.lineTo(i+r-s.bottomRight,n+o),t.arc(i+r-s.bottomRight,n+o-s.bottomRight,s.bottomRight,_n,0,!0),t.lineTo(i+r,n+s.topRight),t.arc(i+r-s.topRight,n+s.topRight,s.topRight,0,-_n,!0),t.lineTo(i+s.topLeft,n)}const ro=new RegExp(/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/),oo=new RegExp(/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/);function so(t,e){const i=(""+t).match(ro);if(!i||"normal"===i[1])return 1.2*e;switch(t=+i[2],i[3]){case"px":return t;case"%":t/=100}return e*t}const ao=t=>+t||0;function lo(t,e){const i={},n=Gi(e),r=n?Object.keys(e):e,o=Gi(t)?n?i=>Qi(t[i],t[e[i]]):e=>t[e]:()=>t;for(const t of r)i[t]=ao(o(t));return i}function co(t){return lo(t,{top:"y",right:"x",bottom:"y",left:"x"})}function ho(t){return lo(t,["topLeft","topRight","bottomLeft","bottomRight"])}function uo(t){const e=co(t);return e.width=e.left+e.right,e.height=e.top+e.bottom,e}function fo(t,e){t=t||{},e=e||Hr.font;let i=Qi(t.size,e.size);"string"==typeof i&&(i=parseInt(i,10));let n=Qi(t.style,e.style);n&&!(""+n).match(oo)&&(console.warn('Invalid font style specified: "'+n+'"'),n="");const r={family:Qi(t.family,e.family),lineHeight:so(Qi(t.lineHeight,e.lineHeight),i),size:i,style:n,weight:Qi(t.weight,e.weight),string:""};return r.string=function(t){return!t||Yi(t.size)||Yi(t.family)?null:(t.style?t.style+" ":"")+(t.weight?t.weight+" ":"")+t.size+"px "+t.family}(r),r}function po(t,e,i,n){let r,o,s,a=!0;for(r=0,o=t.length;r<o;++r)if(s=t[r],void 0!==s&&(void 0!==e&&"function"==typeof s&&(s=s(e),a=!1),void 0!==i&&Xi(s)&&(s=s[i%s.length],a=!1),void 0!==s))return n&&!a&&(n.cacheable=!1),s}function go(t,e){return Object.assign(Object.create(t),e)}function mo(t,e=[""],i=t,n,r=(()=>t[0])){fn(n)||(n=Eo("_fallback",t));const o={[Symbol.toStringTag]:"Object",_cacheable:!0,_scopes:t,_rootScopes:i,_fallback:n,_getTarget:r,override:r=>mo([r,...t],e,i,n)};return new Proxy(o,{deleteProperty:(e,i)=>(delete e[i],delete e._keys,delete t[0][i],!0),get:(i,n)=>_o(i,n,(()=>function(t,e,i,n){let r;for(const o of e)if(r=Eo(yo(o,t),i),fn(r))return xo(t,r)?So(i,n,t,r):r}(n,e,t,i))),getOwnPropertyDescriptor:(t,e)=>Reflect.getOwnPropertyDescriptor(t._scopes[0],e),getPrototypeOf:()=>Reflect.getPrototypeOf(t[0]),has:(t,e)=>Po(t).includes(e),ownKeys:t=>Po(t),set(t,e,i){const n=t._storage||(t._storage=r());return t[e]=n[e]=i,delete t._keys,!0}})}function bo(t,e,i,n){const r={_cacheable:!1,_proxy:t,_context:e,_subProxy:i,_stack:new Set,_descriptors:vo(t,n),setContext:e=>bo(t,e,i,n),override:r=>bo(t.override(r),e,i,n)};return new Proxy(r,{deleteProperty:(e,i)=>(delete e[i],delete t[i],!0),get:(t,e,i)=>_o(t,e,(()=>function(t,e,i){const{_proxy:n,_context:r,_subProxy:o,_descriptors:s}=t;let a=n[e];pn(a)&&s.isScriptable(e)&&(a=function(t,e,i,n){const{_proxy:r,_context:o,_subProxy:s,_stack:a}=i;if(a.has(t))throw new Error("Recursion detected: "+Array.from(a).join("->")+"->"+t);a.add(t),e=e(o,s||n),a.delete(t),xo(t,e)&&(e=So(r._scopes,r,t,e));return e}(e,a,t,i));Xi(a)&&a.length&&(a=function(t,e,i,n){const{_proxy:r,_context:o,_subProxy:s,_descriptors:a}=i;if(fn(o.index)&&n(t))e=e[o.index%e.length];else if(Gi(e[0])){const i=e,n=r._scopes.filter((t=>t!==i));e=[];for(const l of i){const i=So(n,r,t,l);e.push(bo(i,o,s&&s[t],a))}}return e}(e,a,t,s.isIndexable));xo(e,a)&&(a=bo(a,r,o&&o[e],s));return a}(t,e,i))),getOwnPropertyDescriptor:(e,i)=>e._descriptors.allKeys?Reflect.has(t,i)?{enumerable:!0,configurable:!0}:void 0:Reflect.getOwnPropertyDescriptor(t,i),getPrototypeOf:()=>Reflect.getPrototypeOf(t),has:(e,i)=>Reflect.has(t,i),ownKeys:()=>Reflect.ownKeys(t),set:(e,i,n)=>(t[i]=n,delete e[i],!0)})}function vo(t,e={scriptable:!0,indexable:!0}){const{_scriptable:i=e.scriptable,_indexable:n=e.indexable,_allKeys:r=e.allKeys}=t;return{allKeys:r,scriptable:i,indexable:n,isScriptable:pn(i)?i:()=>i,isIndexable:pn(n)?n:()=>n}}const yo=(t,e)=>t?t+dn(e):e,xo=(t,e)=>Gi(e)&&"adapters"!==t&&(null===Object.getPrototypeOf(e)||e.constructor===Object);function _o(t,e,i){if(Object.prototype.hasOwnProperty.call(t,e))return t[e];const n=i();return t[e]=n,n}function wo(t,e,i){return pn(t)?t(e,i):t}const ko=(t,e)=>!0===t?e:"string"==typeof t?un(e,t):void 0;function Oo(t,e,i,n,r){for(const o of e){const e=ko(i,o);if(e){t.add(e);const o=wo(e._fallback,i,r);if(fn(o)&&o!==i&&o!==n)return o}else if(!1===e&&fn(n)&&i!==n)return null}return!1}function So(t,e,i,n){const r=e._rootScopes,o=wo(e._fallback,i,n),s=[...t,...r],a=new Set;a.add(n);let l=Mo(a,s,i,o||i,n);return null!==l&&((!fn(o)||o===i||(l=Mo(a,s,o,l,n),null!==l))&&mo(Array.from(a),[""],r,o,(()=>function(t,e,i){const n=t._getTarget();e in n||(n[e]={});const r=n[e];if(Xi(r)&&Gi(i))return i;return r}(e,i,n))))}function Mo(t,e,i,n,r){for(;i;)i=Oo(t,e,i,n,r);return i}function Eo(t,e){for(const i of e){if(!i)continue;const e=i[t];if(fn(e))return e}}function Po(t){let e=t._keys;return e||(e=t._keys=function(t){const e=new Set;for(const i of t)for(const t of Object.keys(i).filter((t=>!t.startsWith("_"))))e.add(t);return Array.from(e)}(t._scopes)),e}function To(t,e,i,n){const{iScale:r}=t,{key:o="r"}=this._parsing,s=new Array(n);let a,l,c,h;for(a=0,l=n;a<l;++a)c=a+i,h=e[c],s[a]={r:r.parse(un(h,o),c)};return s}const Lo=Number.EPSILON||1e-14,Ao=(t,e)=>e<t.length&&!t[e].skip&&t[e],Co=t=>"x"===t?"y":"x";function Do(t,e,i,n){const r=t.skip?e:t,o=e,s=i.skip?e:i,a=jn(o,r),l=jn(s,o);let c=a/(a+l),h=l/(a+l);c=isNaN(c)?0:c,h=isNaN(h)?0:h;const u=n*c,d=n*h;return{previous:{x:o.x-u*(s.x-r.x),y:o.y-u*(s.y-r.y)},next:{x:o.x+d*(s.x-r.x),y:o.y+d*(s.y-r.y)}}}function jo(t,e="x"){const i=Co(e),n=t.length,r=Array(n).fill(0),o=Array(n);let s,a,l,c=Ao(t,0);for(s=0;s<n;++s)if(a=l,l=c,c=Ao(t,s+1),l){if(c){const t=c[e]-l[e];r[s]=0!==t?(c[i]-l[i])/t:0}o[s]=a?c?Sn(r[s-1])!==Sn(r[s])?0:(r[s-1]+r[s])/2:r[s-1]:r[s]}!function(t,e,i){const n=t.length;let r,o,s,a,l,c=Ao(t,0);for(let h=0;h<n-1;++h)l=c,c=Ao(t,h+1),l&&c&&(Pn(e[h],0,Lo)?i[h]=i[h+1]=0:(r=i[h]/e[h],o=i[h+1]/e[h],a=Math.pow(r,2)+Math.pow(o,2),a<=9||(s=3/Math.sqrt(a),i[h]=r*s*e[h],i[h+1]=o*s*e[h])))}(t,r,o),function(t,e,i="x"){const n=Co(i),r=t.length;let o,s,a,l=Ao(t,0);for(let c=0;c<r;++c){if(s=a,a=l,l=Ao(t,c+1),!a)continue;const r=a[i],h=a[n];s&&(o=(r-s[i])/3,a[`cp1${i}`]=r-o,a[`cp1${n}`]=h-o*e[c]),l&&(o=(l[i]-r)/3,a[`cp2${i}`]=r+o,a[`cp2${n}`]=h+o*e[c])}}(t,o,e)}function Io(t,e,i){return Math.max(Math.min(t,i),e)}function Ro(t,e,i,n,r){let o,s,a,l;if(e.spanGaps&&(t=t.filter((t=>!t.skip))),"monotone"===e.cubicInterpolationMode)jo(t,r);else{let i=n?t[t.length-1]:t[0];for(o=0,s=t.length;o<s;++o)a=t[o],l=Do(i,a,t[Math.min(o+1,s-(n?0:1))%s],e.tension),a.cp1x=l.previous.x,a.cp1y=l.previous.y,a.cp2x=l.next.x,a.cp2y=l.next.y,i=a}e.capBezierPoints&&function(t,e){let i,n,r,o,s,a=Kr(t[0],e);for(i=0,n=t.length;i<n;++i)s=o,o=a,a=i<n-1&&Kr(t[i+1],e),o&&(r=t[i],s&&(r.cp1x=Io(r.cp1x,e.left,e.right),r.cp1y=Io(r.cp1y,e.top,e.bottom)),a&&(r.cp2x=Io(r.cp2x,e.left,e.right),r.cp2y=Io(r.cp2y,e.top,e.bottom)))}(t,i)}function Fo(){return"undefined"!=typeof window&&"undefined"!=typeof document}function zo(t){let e=t.parentNode;return e&&"[object ShadowRoot]"===e.toString()&&(e=e.host),e}function No(t,e,i){let n;return"string"==typeof t?(n=parseInt(t,10),-1!==t.indexOf("%")&&(n=n/100*e.parentNode[i])):n=t,n}const Bo=t=>window.getComputedStyle(t,null);const Wo=["top","right","bottom","left"];function Vo(t,e,i){const n={};i=i?"-"+i:"";for(let r=0;r<4;r++){const o=Wo[r];n[o]=parseFloat(t[e+"-"+o+i])||0}return n.width=n.left+n.right,n.height=n.top+n.bottom,n}const Ho=(t,e,i)=>(t>0||e>0)&&(!i||!i.shadowRoot);function $o(t,e){if("native"in t)return t;const{canvas:i,currentDevicePixelRatio:n}=e,r=Bo(i),o="border-box"===r.boxSizing,s=Vo(r,"padding"),a=Vo(r,"border","width"),{x:l,y:c,box:h}=function(t,e){const i=t.touches,n=i&&i.length?i[0]:t,{offsetX:r,offsetY:o}=n;let s,a,l=!1;if(Ho(r,o,t.target))s=r,a=o;else{const t=e.getBoundingClientRect();s=n.clientX-t.left,a=n.clientY-t.top,l=!0}return{x:s,y:a,box:l}}(t,i),u=s.left+(h&&a.left),d=s.top+(h&&a.top);let{width:f,height:p}=e;return o&&(f-=s.width+a.width,p-=s.height+a.height),{x:Math.round((l-u)/f*i.width/n),y:Math.round((c-d)/p*i.height/n)}}const Uo=t=>Math.round(10*t)/10;function qo(t,e,i,n){const r=Bo(t),o=Vo(r,"margin"),s=No(r.maxWidth,t,"clientWidth")||yn,a=No(r.maxHeight,t,"clientHeight")||yn,l=function(t,e,i){let n,r;if(void 0===e||void 0===i){const o=zo(t);if(o){const t=o.getBoundingClientRect(),s=Bo(o),a=Vo(s,"border","width"),l=Vo(s,"padding");e=t.width-l.width-a.width,i=t.height-l.height-a.height,n=No(s.maxWidth,o,"clientWidth"),r=No(s.maxHeight,o,"clientHeight")}else e=t.clientWidth,i=t.clientHeight}return{width:e,height:i,maxWidth:n||yn,maxHeight:r||yn}}(t,e,i);let{width:c,height:h}=l;if("content-box"===r.boxSizing){const t=Vo(r,"border","width"),e=Vo(r,"padding");c-=e.width+t.width,h-=e.height+t.height}return c=Math.max(0,c-o.width),h=Math.max(0,n?Math.floor(c/n):h-o.height),c=Uo(Math.min(c,s,l.maxWidth)),h=Uo(Math.min(h,a,l.maxHeight)),c&&!h&&(h=Uo(c/2)),{width:c,height:h}}function Yo(t,e,i){const n=e||1,r=Math.floor(t.height*n),o=Math.floor(t.width*n);t.height=r/n,t.width=o/n;const s=t.canvas;return s.style&&(i||!s.style.height&&!s.style.width)&&(s.style.height=`${t.height}px`,s.style.width=`${t.width}px`),(t.currentDevicePixelRatio!==n||s.height!==r||s.width!==o)&&(t.currentDevicePixelRatio=n,s.height=r,s.width=o,t.ctx.setTransform(n,0,0,n,0,0),!0)}const Xo=function(){let t=!1;try{const e={get passive(){return t=!0,!1}};window.addEventListener("test",null,e),window.removeEventListener("test",null,e)}catch(t){}return t}();function Go(t,e){const i=function(t,e){return Bo(t).getPropertyValue(e)}(t,e),n=i&&i.match(/^(\d+)(\.\d+)?px$/);return n?+n[1]:void 0}function Ko(t,e,i,n){return{x:t.x+i*(e.x-t.x),y:t.y+i*(e.y-t.y)}}function Jo(t,e,i,n){return{x:t.x+i*(e.x-t.x),y:"middle"===n?i<.5?t.y:e.y:"after"===n?i<1?t.y:e.y:i>0?e.y:t.y}}function Qo(t,e,i,n){const r={x:t.cp2x,y:t.cp2y},o={x:e.cp1x,y:e.cp1y},s=Ko(t,r,i),a=Ko(r,o,i),l=Ko(o,e,i),c=Ko(s,a,i),h=Ko(a,l,i);return Ko(c,h,i)}const Zo=new Map;function ts(t,e,i){return function(t,e){e=e||{};const i=t+JSON.stringify(e);let n=Zo.get(i);return n||(n=new Intl.NumberFormat(t,e),Zo.set(i,n)),n}(e,i).format(t)}function es(t,e,i){return t?function(t,e){return{x:i=>t+t+e-i,setWidth(t){e=t},textAlign:t=>"center"===t?t:"right"===t?"left":"right",xPlus:(t,e)=>t-e,leftForLtr:(t,e)=>t-e}}(e,i):{x:t=>t,setWidth(t){},textAlign:t=>t,xPlus:(t,e)=>t+e,leftForLtr:(t,e)=>t}}function is(t,e){let i,n;"ltr"!==e&&"rtl"!==e||(i=t.canvas.style,n=[i.getPropertyValue("direction"),i.getPropertyPriority("direction")],i.setProperty("direction",e,"important"),t.prevTextDirection=n)}function ns(t,e){void 0!==e&&(delete t.prevTextDirection,t.canvas.style.setProperty("direction",e[0],e[1]))}function rs(t){return"angle"===t?{between:Fn,compare:In,normalize:Rn}:{between:Nn,compare:(t,e)=>t-e,normalize:t=>t}}function os({start:t,end:e,count:i,loop:n,style:r}){return{start:t%i,end:e%i,loop:n&&(e-t+1)%i==0,style:r}}function ss(t,e,i){if(!i)return[t];const{property:n,start:r,end:o}=i,s=e.length,{compare:a,between:l,normalize:c}=rs(n),{start:h,end:u,loop:d,style:f}=function(t,e,i){const{property:n,start:r,end:o}=i,{between:s,normalize:a}=rs(n),l=e.length;let c,h,{start:u,end:d,loop:f}=t;if(f){for(u+=l,d+=l,c=0,h=l;c<h&&s(a(e[u%l][n]),r,o);++c)u--,d--;u%=l,d%=l}return d<u&&(d+=l),{start:u,end:d,loop:f,style:t.style}}(t,e,i),p=[];let g,m,b,v=!1,y=null;const x=()=>v||l(r,b,g)&&0!==a(r,b),_=()=>!v||0===a(o,g)||l(o,b,g);for(let t=h,i=h;t<=u;++t)m=e[t%s],m.skip||(g=c(m[n]),g!==b&&(v=l(g,r,o),null===y&&x()&&(y=0===a(g,r)?t:i),null!==y&&_()&&(p.push(os({start:y,end:t,loop:d,count:s,style:f})),y=null),i=t,b=g));return null!==y&&p.push(os({start:y,end:u,loop:d,count:s,style:f})),p}function as(t,e){const i=[],n=t.segments;for(let r=0;r<n.length;r++){const o=ss(n[r],t.points,e);o.length&&i.push(...o)}return i}function ls(t,e,i,n){return n&&n.setContext&&i?function(t,e,i,n){const r=t._chart.getContext(),o=cs(t.options),{_datasetIndex:s,options:{spanGaps:a}}=t,l=i.length,c=[];let h=o,u=e[0].start,d=u;function f(t,e,n,r){const o=a?-1:1;if(t!==e){for(t+=l;i[t%l].skip;)t-=o;for(;i[e%l].skip;)e+=o;t%l!=e%l&&(c.push({start:t%l,end:e%l,loop:n,style:r}),h=r,u=e%l)}}for(const t of e){u=a?u:t.start;let e,o=i[u%l];for(d=u+1;d<=t.end;d++){const a=i[d%l];e=cs(n.setContext(go(r,{type:"segment",p0:o,p1:a,p0DataIndex:(d-1)%l,p1DataIndex:d%l,datasetIndex:s}))),hs(e,h)&&f(u,d-1,t.loop,h),o=a,h=e}u<d-1&&f(u,d-1,t.loop,h)}return c}(t,e,i,n):e}function cs(t){return{backgroundColor:t.backgroundColor,borderCapStyle:t.borderCapStyle,borderDash:t.borderDash,borderDashOffset:t.borderDashOffset,borderJoinStyle:t.borderJoinStyle,borderWidth:t.borderWidth,borderColor:t.borderColor}}function hs(t,e){return e&&JSON.stringify(t)!==JSON.stringify(e)}var us=new class{constructor(){this._request=null,this._charts=new Map,this._running=!1,this._lastDate=void 0}_notify(t,e,i,n){const r=e.listeners[n],o=e.duration;r.forEach((n=>n({chart:t,initial:e.initial,numSteps:o,currentStep:Math.min(i-e.start,o)})))}_refresh(){this._request||(this._running=!0,this._request=qn.call(window,(()=>{this._update(),this._request=null,this._running&&this._refresh()})))}_update(t=Date.now()){let e=0;this._charts.forEach(((i,n)=>{if(!i.running||!i.items.length)return;const r=i.items;let o,s=r.length-1,a=!1;for(;s>=0;--s)o=r[s],o._active?(o._total>i.duration&&(i.duration=o._total),o.tick(t),a=!0):(r[s]=r[r.length-1],r.pop());a&&(n.draw(),this._notify(n,i,t,"progress")),r.length||(i.running=!1,this._notify(n,i,t,"complete"),i.initial=!1),e+=r.length})),this._lastDate=t,0===e&&(this._running=!1)}_getAnims(t){const e=this._charts;let i=e.get(t);return i||(i={running:!1,initial:!0,items:[],listeners:{complete:[],progress:[]}},e.set(t,i)),i}listen(t,e,i){this._getAnims(t).listeners[e].push(i)}add(t,e){e&&e.length&&this._getAnims(t).items.push(...e)}has(t){return this._getAnims(t).items.length>0}start(t){const e=this._charts.get(t);e&&(e.running=!0,e.start=Date.now(),e.duration=e.items.reduce(((t,e)=>Math.max(t,e._duration)),0),this._refresh())}running(t){if(!this._running)return!1;const e=this._charts.get(t);return!!(e&&e.running&&e.items.length)}stop(t){const e=this._charts.get(t);if(!e||!e.items.length)return;const i=e.items;let n=i.length-1;for(;n>=0;--n)i[n].cancel();e.items=[],this._notify(t,e,Date.now(),"complete")}remove(t){return this._charts.delete(t)}};const ds="transparent",fs={boolean:(t,e,i)=>i>.5?e:t,color(t,e,i){const n=Fr(t||ds),r=n.valid&&Fr(e||ds);return r&&r.valid?r.mix(n,i).hexString():e},number:(t,e,i)=>t+(e-t)*i};class ps{constructor(t,e,i,n){const r=e[i];n=po([t.to,n,r,t.from]);const o=po([t.from,r,n]);this._active=!0,this._fn=t.fn||fs[t.type||typeof o],this._easing=er[t.easing]||er.linear,this._start=Math.floor(Date.now()+(t.delay||0)),this._duration=this._total=Math.floor(t.duration),this._loop=!!t.loop,this._target=e,this._prop=i,this._from=o,this._to=n,this._promises=void 0}active(){return this._active}update(t,e,i){if(this._active){this._notify(!1);const n=this._target[this._prop],r=i-this._start,o=this._duration-r;this._start=i,this._duration=Math.floor(Math.max(o,t.duration)),this._total+=r,this._loop=!!t.loop,this._to=po([t.to,e,n,t.from]),this._from=po([t.from,n,e])}}cancel(){this._active&&(this.tick(Date.now()),this._active=!1,this._notify(!1))}tick(t){const e=t-this._start,i=this._duration,n=this._prop,r=this._from,o=this._loop,s=this._to;let a;if(this._active=r!==s&&(o||e<i),!this._active)return this._target[n]=s,void this._notify(!0);e<0?this._target[n]=r:(a=e/i%2,a=o&&a>1?2-a:a,a=this._easing(Math.min(1,Math.max(0,a))),this._target[n]=this._fn(r,s,a))}wait(){const t=this._promises||(this._promises=[]);return new Promise(((e,i)=>{t.push({res:e,rej:i})}))}_notify(t){const e=t?"res":"rej",i=this._promises||[];for(let t=0;t<i.length;t++)i[t][e]()}}Hr.set("animation",{delay:void 0,duration:1e3,easing:"easeOutQuart",fn:void 0,from:void 0,loop:void 0,to:void 0,type:void 0});const gs=Object.keys(Hr.animation);Hr.describe("animation",{_fallback:!1,_indexable:!1,_scriptable:t=>"onProgress"!==t&&"onComplete"!==t&&"fn"!==t}),Hr.set("animations",{colors:{type:"color",properties:["color","borderColor","backgroundColor"]},numbers:{type:"number",properties:["x","y","borderWidth","radius","tension"]}}),Hr.describe("animations",{_fallback:"animation"}),Hr.set("transitions",{active:{animation:{duration:400}},resize:{animation:{duration:0}},show:{animations:{colors:{from:"transparent"},visible:{type:"boolean",duration:0}}},hide:{animations:{colors:{to:"transparent"},visible:{type:"boolean",easing:"linear",fn:t=>0|t}}}});class ms{constructor(t,e){this._chart=t,this._properties=new Map,this.configure(e)}configure(t){if(!Gi(t))return;const e=this._properties;Object.getOwnPropertyNames(t).forEach((i=>{const n=t[i];if(!Gi(n))return;const r={};for(const t of gs)r[t]=n[t];(Xi(n.properties)&&n.properties||[i]).forEach((t=>{t!==i&&e.has(t)||e.set(t,r)}))}))}_animateOptions(t,e){const i=e.options,n=function(t,e){if(!e)return;let i=t.options;if(!i)return void(t.options=e);i.$shared&&(t.options=i=Object.assign({},i,{$shared:!1,$animations:{}}));return i}(t,i);if(!n)return[];const r=this._createAnimations(n,i);return i.$shared&&function(t,e){const i=[],n=Object.keys(e);for(let e=0;e<n.length;e++){const r=t[n[e]];r&&r.active()&&i.push(r.wait())}return Promise.all(i)}(t.options.$animations,i).then((()=>{t.options=i}),(()=>{})),r}_createAnimations(t,e){const i=this._properties,n=[],r=t.$animations||(t.$animations={}),o=Object.keys(e),s=Date.now();let a;for(a=o.length-1;a>=0;--a){const l=o[a];if("$"===l.charAt(0))continue;if("options"===l){n.push(...this._animateOptions(t,e));continue}const c=e[l];let h=r[l];const u=i.get(l);if(h){if(u&&h.active()){h.update(u,c,s);continue}h.cancel()}u&&u.duration?(r[l]=h=new ps(u,t,l,c),n.push(h)):t[l]=c}return n}update(t,e){if(0===this._properties.size)return void Object.assign(t,e);const i=this._createAnimations(t,e);return i.length?(us.add(this._chart,i),!0):void 0}}function bs(t,e){const i=t&&t.options||{},n=i.reverse,r=void 0===i.min?e:0,o=void 0===i.max?e:0;return{start:n?o:r,end:n?r:o}}function vs(t,e){const i=[],n=t._getSortedDatasetMetas(e);let r,o;for(r=0,o=n.length;r<o;++r)i.push(n[r].index);return i}function ys(t,e,i,n={}){const r=t.keys,o="single"===n.mode;let s,a,l,c;if(null!==e){for(s=0,a=r.length;s<a;++s){if(l=+r[s],l===i){if(n.all)continue;break}c=t.values[l],Ki(c)&&(o||0===e||Sn(e)===Sn(c))&&(e+=c)}return e}}function xs(t,e){const i=t&&t.options.stacked;return i||void 0===i&&void 0!==e.stack}function _s(t,e,i){const n=t[e]||(t[e]={});return n[i]||(n[i]={})}function ws(t,e,i,n){for(const r of e.getMatchingVisibleMetas(n).reverse()){const e=t[r.index];if(i&&e>0||!i&&e<0)return r.index}return null}function ks(t,e){const{chart:i,_cachedMeta:n}=t,r=i._stacks||(i._stacks={}),{iScale:o,vScale:s,index:a}=n,l=o.axis,c=s.axis,h=function(t,e,i){return`${t.id}.${e.id}.${i.stack||i.type}`}(o,s,n),u=e.length;let d;for(let t=0;t<u;++t){const i=e[t],{[l]:o,[c]:u}=i;d=(i._stacks||(i._stacks={}))[c]=_s(r,h,o),d[a]=u,d._top=ws(d,s,!0,n.type),d._bottom=ws(d,s,!1,n.type)}}function Os(t,e){const i=t.scales;return Object.keys(i).filter((t=>i[t].axis===e)).shift()}function Ss(t,e){const i=t.controller.index,n=t.vScale&&t.vScale.axis;if(n){e=e||t._parsed;for(const t of e){const e=t._stacks;if(!e||void 0===e[n]||void 0===e[n][i])return;delete e[n][i]}}}const Ms=t=>"reset"===t||"none"===t,Es=(t,e)=>e?t:Object.assign({},t);class Ps{constructor(t,e){this.chart=t,this._ctx=t.ctx,this.index=e,this._cachedDataOpts={},this._cachedMeta=this.getMeta(),this._type=this._cachedMeta.type,this.options=void 0,this._parsing=!1,this._data=void 0,this._objectData=void 0,this._sharedOptions=void 0,this._drawStart=void 0,this._drawCount=void 0,this.enableOptionSharing=!1,this.supportsDecimation=!1,this.$context=void 0,this._syncList=[],this.initialize()}initialize(){const t=this._cachedMeta;this.configure(),this.linkScales(),t._stacked=xs(t.vScale,t),this.addElements()}updateIndex(t){this.index!==t&&Ss(this._cachedMeta),this.index=t}linkScales(){const t=this.chart,e=this._cachedMeta,i=this.getDataset(),n=(t,e,i,n)=>"x"===t?e:"r"===t?n:i,r=e.xAxisID=Qi(i.xAxisID,Os(t,"x")),o=e.yAxisID=Qi(i.yAxisID,Os(t,"y")),s=e.rAxisID=Qi(i.rAxisID,Os(t,"r")),a=e.indexAxis,l=e.iAxisID=n(a,r,o,s),c=e.vAxisID=n(a,o,r,s);e.xScale=this.getScaleForId(r),e.yScale=this.getScaleForId(o),e.rScale=this.getScaleForId(s),e.iScale=this.getScaleForId(l),e.vScale=this.getScaleForId(c)}getDataset(){return this.chart.data.datasets[this.index]}getMeta(){return this.chart.getDatasetMeta(this.index)}getScaleForId(t){return this.chart.scales[t]}_getOtherScale(t){const e=this._cachedMeta;return t===e.iScale?e.vScale:e.iScale}reset(){this._update("reset")}_destroy(){const t=this._cachedMeta;this._data&&$n(this._data,this),t._stacked&&Ss(t)}_dataCheck(){const t=this.getDataset(),e=t.data||(t.data=[]),i=this._data;if(Gi(e))this._data=function(t){const e=Object.keys(t),i=new Array(e.length);let n,r,o;for(n=0,r=e.length;n<r;++n)o=e[n],i[n]={x:o,y:t[o]};return i}(e);else if(i!==e){if(i){$n(i,this);const t=this._cachedMeta;Ss(t),t._parsed=[]}e&&Object.isExtensible(e)&&(r=this,(n=e)._chartjs?n._chartjs.listeners.push(r):(Object.defineProperty(n,"_chartjs",{configurable:!0,enumerable:!1,value:{listeners:[r]}}),Hn.forEach((t=>{const e="_onData"+dn(t),i=n[t];Object.defineProperty(n,t,{configurable:!0,enumerable:!1,value(...t){const r=i.apply(this,t);return n._chartjs.listeners.forEach((i=>{"function"==typeof i[e]&&i[e](...t)})),r}})})))),this._syncList=[],this._data=e}var n,r}addElements(){const t=this._cachedMeta;this._dataCheck(),this.datasetElementType&&(t.dataset=new this.datasetElementType)}buildOrUpdateElements(t){const e=this._cachedMeta,i=this.getDataset();let n=!1;this._dataCheck();const r=e._stacked;e._stacked=xs(e.vScale,e),e.stack!==i.stack&&(n=!0,Ss(e),e.stack=i.stack),this._resyncElements(t),(n||r!==e._stacked)&&ks(this,e._parsed)}configure(){const t=this.chart.config,e=t.datasetScopeKeys(this._type),i=t.getOptionScopes(this.getDataset(),e,!0);this.options=t.createResolver(i,this.getContext()),this._parsing=this.options.parsing,this._cachedDataOpts={}}parse(t,e){const{_cachedMeta:i,_data:n}=this,{iScale:r,_stacked:o}=i,s=r.axis;let a,l,c,h=0===t&&e===n.length||i._sorted,u=t>0&&i._parsed[t-1];if(!1===this._parsing)i._parsed=n,i._sorted=!0,c=n;else{c=Xi(n[t])?this.parseArrayData(i,n,t,e):Gi(n[t])?this.parseObjectData(i,n,t,e):this.parsePrimitiveData(i,n,t,e);const r=()=>null===l[s]||u&&l[s]<u[s];for(a=0;a<e;++a)i._parsed[a+t]=l=c[a],h&&(r()&&(h=!1),u=l);i._sorted=h}o&&ks(this,c)}parsePrimitiveData(t,e,i,n){const{iScale:r,vScale:o}=t,s=r.axis,a=o.axis,l=r.getLabels(),c=r===o,h=new Array(n);let u,d,f;for(u=0,d=n;u<d;++u)f=u+i,h[u]={[s]:c||r.parse(l[f],f),[a]:o.parse(e[f],f)};return h}parseArrayData(t,e,i,n){const{xScale:r,yScale:o}=t,s=new Array(n);let a,l,c,h;for(a=0,l=n;a<l;++a)c=a+i,h=e[c],s[a]={x:r.parse(h[0],c),y:o.parse(h[1],c)};return s}parseObjectData(t,e,i,n){const{xScale:r,yScale:o}=t,{xAxisKey:s="x",yAxisKey:a="y"}=this._parsing,l=new Array(n);let c,h,u,d;for(c=0,h=n;c<h;++c)u=c+i,d=e[u],l[c]={x:r.parse(un(d,s),u),y:o.parse(un(d,a),u)};return l}getParsed(t){return this._cachedMeta._parsed[t]}getDataElement(t){return this._cachedMeta.data[t]}applyStack(t,e,i){const n=this.chart,r=this._cachedMeta,o=e[t.axis];return ys({keys:vs(n,!0),values:e._stacks[t.axis]},o,r.index,{mode:i})}updateRangeFromParsed(t,e,i,n){const r=i[e.axis];let o=null===r?NaN:r;const s=n&&i._stacks[e.axis];n&&s&&(n.values=s,o=ys(n,r,this._cachedMeta.index)),t.min=Math.min(t.min,o),t.max=Math.max(t.max,o)}getMinMax(t,e){const i=this._cachedMeta,n=i._parsed,r=i._sorted&&t===i.iScale,o=n.length,s=this._getOtherScale(t),a=((t,e,i)=>t&&!e.hidden&&e._stacked&&{keys:vs(i,!0),values:null})(e,i,this.chart),l={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY},{min:c,max:h}=function(t){const{min:e,max:i,minDefined:n,maxDefined:r}=t.getUserBounds();return{min:n?e:Number.NEGATIVE_INFINITY,max:r?i:Number.POSITIVE_INFINITY}}(s);let u,d;function f(){d=n[u];const e=d[s.axis];return!Ki(d[t.axis])||c>e||h<e}for(u=0;u<o&&(f()||(this.updateRangeFromParsed(l,t,d,a),!r));++u);if(r)for(u=o-1;u>=0;--u)if(!f()){this.updateRangeFromParsed(l,t,d,a);break}return l}getAllParsedValues(t){const e=this._cachedMeta._parsed,i=[];let n,r,o;for(n=0,r=e.length;n<r;++n)o=e[n][t.axis],Ki(o)&&i.push(o);return i}getMaxOverflow(){return!1}getLabelAndValue(t){const e=this._cachedMeta,i=e.iScale,n=e.vScale,r=this.getParsed(t);return{label:i?""+i.getLabelForValue(r[i.axis]):"",value:n?""+n.getLabelForValue(r[n.axis]):""}}_update(t){const e=this._cachedMeta;this.update(t||"default"),e._clip=function(t){let e,i,n,r;return Gi(t)?(e=t.top,i=t.right,n=t.bottom,r=t.left):e=i=n=r=t,{top:e,right:i,bottom:n,left:r,disabled:!1===t}}(Qi(this.options.clip,function(t,e,i){if(!1===i)return!1;const n=bs(t,i),r=bs(e,i);return{top:r.end,right:n.end,bottom:r.start,left:n.start}}(e.xScale,e.yScale,this.getMaxOverflow())))}update(t){}draw(){const t=this._ctx,e=this.chart,i=this._cachedMeta,n=i.data||[],r=e.chartArea,o=[],s=this._drawStart||0,a=this._drawCount||n.length-s,l=this.options.drawActiveElementsOnTop;let c;for(i.dataset&&i.dataset.draw(t,r,s,a),c=s;c<s+a;++c){const e=n[c];e.hidden||(e.active&&l?o.push(e):e.draw(t,r))}for(c=0;c<o.length;++c)o[c].draw(t,r)}getStyle(t,e){const i=e?"active":"default";return void 0===t&&this._cachedMeta.dataset?this.resolveDatasetElementOptions(i):this.resolveDataElementOptions(t||0,i)}getContext(t,e,i){const n=this.getDataset();let r;if(t>=0&&t<this._cachedMeta.data.length){const e=this._cachedMeta.data[t];r=e.$context||(e.$context=function(t,e,i){return go(t,{active:!1,dataIndex:e,parsed:void 0,raw:void 0,element:i,index:e,mode:"default",type:"data"})}(this.getContext(),t,e)),r.parsed=this.getParsed(t),r.raw=n.data[t],r.index=r.dataIndex=t}else r=this.$context||(this.$context=function(t,e){return go(t,{active:!1,dataset:void 0,datasetIndex:e,index:e,mode:"default",type:"dataset"})}(this.chart.getContext(),this.index)),r.dataset=n,r.index=r.datasetIndex=this.index;return r.active=!!e,r.mode=i,r}resolveDatasetElementOptions(t){return this._resolveElementOptions(this.datasetElementType.id,t)}resolveDataElementOptions(t,e){return this._resolveElementOptions(this.dataElementType.id,e,t)}_resolveElementOptions(t,e="default",i){const n="active"===e,r=this._cachedDataOpts,o=t+"-"+e,s=r[o],a=this.enableOptionSharing&&fn(i);if(s)return Es(s,a);const l=this.chart.config,c=l.datasetElementScopeKeys(this._type,t),h=n?[`${t}Hover`,"hover",t,""]:[t,""],u=l.getOptionScopes(this.getDataset(),c),d=Object.keys(Hr.elements[t]),f=l.resolveNamedOptions(u,d,(()=>this.getContext(i,n)),h);return f.$shared&&(f.$shared=a,r[o]=Object.freeze(Es(f,a))),f}_resolveAnimations(t,e,i){const n=this.chart,r=this._cachedDataOpts,o=`animation-${e}`,s=r[o];if(s)return s;let a;if(!1!==n.options.animation){const n=this.chart.config,r=n.datasetAnimationScopeKeys(this._type,e),o=n.getOptionScopes(this.getDataset(),r);a=n.createResolver(o,this.getContext(t,i,e))}const l=new ms(n,a&&a.animations);return a&&a._cacheable&&(r[o]=Object.freeze(l)),l}getSharedOptions(t){if(t.$shared)return this._sharedOptions||(this._sharedOptions=Object.assign({},t))}includeOptions(t,e){return!e||Ms(t)||this.chart._animationsDisabled}_getSharedOptions(t,e){const i=this.resolveDataElementOptions(t,e),n=this._sharedOptions,r=this.getSharedOptions(i),o=this.includeOptions(e,r)||r!==n;return this.updateSharedOptions(r,e,i),{sharedOptions:r,includeOptions:o}}updateElement(t,e,i,n){Ms(n)?Object.assign(t,i):this._resolveAnimations(e,n).update(t,i)}updateSharedOptions(t,e,i){t&&!Ms(e)&&this._resolveAnimations(void 0,e).update(t,i)}_setStyle(t,e,i,n){t.active=n;const r=this.getStyle(e,n);this._resolveAnimations(e,i,n).update(t,{options:!n&&this.getSharedOptions(r)||r})}removeHoverStyle(t,e,i){this._setStyle(t,i,"active",!1)}setHoverStyle(t,e,i){this._setStyle(t,i,"active",!0)}_removeDatasetHoverStyle(){const t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!1)}_setDatasetHoverStyle(){const t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!0)}_resyncElements(t){const e=this._data,i=this._cachedMeta.data;for(const[t,e,i]of this._syncList)this[t](e,i);this._syncList=[];const n=i.length,r=e.length,o=Math.min(r,n);o&&this.parse(0,o),r>n?this._insertElements(n,r-n,t):r<n&&this._removeElements(r,n-r)}_insertElements(t,e,i=!0){const n=this._cachedMeta,r=n.data,o=t+e;let s;const a=t=>{for(t.length+=e,s=t.length-1;s>=o;s--)t[s]=t[s-e]};for(a(r),s=t;s<o;++s)r[s]=new this.dataElementType;this._parsing&&a(n._parsed),this.parse(t,e),i&&this.updateElements(r,t,e,"reset")}updateElements(t,e,i,n){}_removeElements(t,e){const i=this._cachedMeta;if(this._parsing){const n=i._parsed.splice(t,e);i._stacked&&Ss(i,n)}i.data.splice(t,e)}_sync(t){if(this._parsing)this._syncList.push(t);else{const[e,i,n]=t;this[e](i,n)}this.chart._dataChanges.push([this.index,...t])}_onDataPush(){const t=arguments.length;this._sync(["_insertElements",this.getDataset().data.length-t,t])}_onDataPop(){this._sync(["_removeElements",this._cachedMeta.data.length-1,1])}_onDataShift(){this._sync(["_removeElements",0,1])}_onDataSplice(t,e){e&&this._sync(["_removeElements",t,e]);const i=arguments.length-2;i&&this._sync(["_insertElements",t,i])}_onDataUnshift(){this._sync(["_insertElements",0,arguments.length])}}function Ts(t){const e=t.iScale,i=function(t,e){if(!t._cache.$bar){const i=t.getMatchingVisibleMetas(e);let n=[];for(let e=0,r=i.length;e<r;e++)n=n.concat(i[e].controller.getAllParsedValues(t));t._cache.$bar=Un(n.sort(((t,e)=>t-e)))}return t._cache.$bar}(e,t.type);let n,r,o,s,a=e._length;const l=()=>{32767!==o&&-32768!==o&&(fn(s)&&(a=Math.min(a,Math.abs(o-s)||a)),s=o)};for(n=0,r=i.length;n<r;++n)o=e.getPixelForValue(i[n]),l();for(s=void 0,n=0,r=e.ticks.length;n<r;++n)o=e.getPixelForTick(n),l();return a}function Ls(t,e,i,n){return Xi(t)?function(t,e,i,n){const r=i.parse(t[0],n),o=i.parse(t[1],n),s=Math.min(r,o),a=Math.max(r,o);let l=s,c=a;Math.abs(s)>Math.abs(a)&&(l=a,c=s),e[i.axis]=c,e._custom={barStart:l,barEnd:c,start:r,end:o,min:s,max:a}}(t,e,i,n):e[i.axis]=i.parse(t,n),e}function As(t,e,i,n){const r=t.iScale,o=t.vScale,s=r.getLabels(),a=r===o,l=[];let c,h,u,d;for(c=i,h=i+n;c<h;++c)d=e[c],u={},u[r.axis]=a||r.parse(s[c],c),l.push(Ls(d,u,o,c));return l}function Cs(t){return t&&void 0!==t.barStart&&void 0!==t.barEnd}function Ds(t,e,i,n){let r=e.borderSkipped;const o={};if(!r)return void(t.borderSkipped=o);if(!0===r)return void(t.borderSkipped={top:!0,right:!0,bottom:!0,left:!0});const{start:s,end:a,reverse:l,top:c,bottom:h}=function(t){let e,i,n,r,o;return t.horizontal?(e=t.base>t.x,i="left",n="right"):(e=t.base<t.y,i="bottom",n="top"),e?(r="end",o="start"):(r="start",o="end"),{start:i,end:n,reverse:e,top:r,bottom:o}}(t);"middle"===r&&i&&(t.enableBorderRadius=!0,(i._top||0)===n?r=c:(i._bottom||0)===n?r=h:(o[js(h,s,a,l)]=!0,r=c)),o[js(r,s,a,l)]=!0,t.borderSkipped=o}function js(t,e,i,n){var r,o,s;return n?(s=i,t=Is(t=(r=t)===(o=e)?s:r===s?o:r,i,e)):t=Is(t,e,i),t}function Is(t,e,i){return"start"===t?e:"end"===t?i:t}function Rs(t,{inflateAmount:e},i){t.inflateAmount="auto"===e?1===i?.33:0:e}Ps.defaults={},Ps.prototype.datasetElementType=null,Ps.prototype.dataElementType=null;class Fs extends Ps{parsePrimitiveData(t,e,i,n){return As(t,e,i,n)}parseArrayData(t,e,i,n){return As(t,e,i,n)}parseObjectData(t,e,i,n){const{iScale:r,vScale:o}=t,{xAxisKey:s="x",yAxisKey:a="y"}=this._parsing,l="x"===r.axis?s:a,c="x"===o.axis?s:a,h=[];let u,d,f,p;for(u=i,d=i+n;u<d;++u)p=e[u],f={},f[r.axis]=r.parse(un(p,l),u),h.push(Ls(un(p,c),f,o,u));return h}updateRangeFromParsed(t,e,i,n){super.updateRangeFromParsed(t,e,i,n);const r=i._custom;r&&e===this._cachedMeta.vScale&&(t.min=Math.min(t.min,r.min),t.max=Math.max(t.max,r.max))}getMaxOverflow(){return 0}getLabelAndValue(t){const e=this._cachedMeta,{iScale:i,vScale:n}=e,r=this.getParsed(t),o=r._custom,s=Cs(o)?"["+o.start+", "+o.end+"]":""+n.getLabelForValue(r[n.axis]);return{label:""+i.getLabelForValue(r[i.axis]),value:s}}initialize(){this.enableOptionSharing=!0,super.initialize();this._cachedMeta.stack=this.getDataset().stack}update(t){const e=this._cachedMeta;this.updateElements(e.data,0,e.data.length,t)}updateElements(t,e,i,n){const r="reset"===n,{index:o,_cachedMeta:{vScale:s}}=this,a=s.getBasePixel(),l=s.isHorizontal(),c=this._getRuler(),{sharedOptions:h,includeOptions:u}=this._getSharedOptions(e,n);for(let d=e;d<e+i;d++){const e=this.getParsed(d),i=r||Yi(e[s.axis])?{base:a,head:a}:this._calculateBarValuePixels(d),f=this._calculateBarIndexPixels(d,c),p=(e._stacks||{})[s.axis],g={horizontal:l,base:i.base,enableBorderRadius:!p||Cs(e._custom)||o===p._top||o===p._bottom,x:l?i.head:f.center,y:l?f.center:i.head,height:l?f.size:Math.abs(i.size),width:l?Math.abs(i.size):f.size};u&&(g.options=h||this.resolveDataElementOptions(d,t[d].active?"active":n));const m=g.options||t[d].options;Ds(g,m,p,o),Rs(g,m,c.ratio),this.updateElement(t[d],d,g,n)}}_getStacks(t,e){const{iScale:i}=this._cachedMeta,n=i.getMatchingVisibleMetas(this._type).filter((t=>t.controller.options.grouped)),r=i.options.stacked,o=[],s=t=>{const i=t.controller.getParsed(e),n=i&&i[t.vScale.axis];if(Yi(n)||isNaN(n))return!0};for(const i of n)if((void 0===e||!s(i))&&((!1===r||-1===o.indexOf(i.stack)||void 0===r&&void 0===i.stack)&&o.push(i.stack),i.index===t))break;return o.length||o.push(void 0),o}_getStackCount(t){return this._getStacks(void 0,t).length}_getStackIndex(t,e,i){const n=this._getStacks(t,i),r=void 0!==e?n.indexOf(e):-1;return-1===r?n.length-1:r}_getRuler(){const t=this.options,e=this._cachedMeta,i=e.iScale,n=[];let r,o;for(r=0,o=e.data.length;r<o;++r)n.push(i.getPixelForValue(this.getParsed(r)[i.axis],r));const s=t.barThickness;return{min:s||Ts(e),pixels:n,start:i._startPixel,end:i._endPixel,stackCount:this._getStackCount(),scale:i,grouped:t.grouped,ratio:s?1:t.categoryPercentage*t.barPercentage}}_calculateBarValuePixels(t){const{_cachedMeta:{vScale:e,_stacked:i},options:{base:n,minBarLength:r}}=this,o=n||0,s=this.getParsed(t),a=s._custom,l=Cs(a);let c,h,u=s[e.axis],d=0,f=i?this.applyStack(e,s,i):u;f!==u&&(d=f-u,f=u),l&&(u=a.barStart,f=a.barEnd-a.barStart,0!==u&&Sn(u)!==Sn(a.barEnd)&&(d=0),d+=u);const p=Yi(n)||l?d:n;let g=e.getPixelForValue(p);if(c=this.chart.getDataVisibility(t)?e.getPixelForValue(d+f):g,h=c-g,Math.abs(h)<r){h=function(t,e,i){return 0!==t?Sn(t):(e.isHorizontal()?1:-1)*(e.min>=i?1:-1)}(h,e,o)*r,u===o&&(g-=h/2);const t=e.getPixelForDecimal(0),i=e.getPixelForDecimal(1),n=Math.min(t,i),s=Math.max(t,i);g=Math.max(Math.min(g,s),n),c=g+h}if(g===e.getPixelForValue(o)){const t=Sn(h)*e.getLineWidthForValue(o)/2;g+=t,h-=t}return{size:h,base:g,head:c,center:c+h/2}}_calculateBarIndexPixels(t,e){const i=e.scale,n=this.options,r=n.skipNull,o=Qi(n.maxBarThickness,1/0);let s,a;if(e.grouped){const i=r?this._getStackCount(t):e.stackCount,l="flex"===n.barThickness?function(t,e,i,n){const r=e.pixels,o=r[t];let s=t>0?r[t-1]:null,a=t<r.length-1?r[t+1]:null;const l=i.categoryPercentage;null===s&&(s=o-(null===a?e.end-e.start:a-o)),null===a&&(a=o+o-s);const c=o-(o-Math.min(s,a))/2*l;return{chunk:Math.abs(a-s)/2*l/n,ratio:i.barPercentage,start:c}}(t,e,n,i):function(t,e,i,n){const r=i.barThickness;let o,s;return Yi(r)?(o=e.min*i.categoryPercentage,s=i.barPercentage):(o=r*n,s=1),{chunk:o/n,ratio:s,start:e.pixels[t]-o/2}}(t,e,n,i),c=this._getStackIndex(this.index,this._cachedMeta.stack,r?t:void 0);s=l.start+l.chunk*c+l.chunk/2,a=Math.min(o,l.chunk*l.ratio)}else s=i.getPixelForValue(this.getParsed(t)[i.axis],t),a=Math.min(o,e.min*e.ratio);return{base:s-a/2,head:s+a/2,center:s,size:a}}draw(){const t=this._cachedMeta,e=t.vScale,i=t.data,n=i.length;let r=0;for(;r<n;++r)null!==this.getParsed(r)[e.axis]&&i[r].draw(this._ctx)}}Fs.id="bar",Fs.defaults={datasetElementType:!1,dataElementType:"bar",categoryPercentage:.8,barPercentage:.9,grouped:!0,animations:{numbers:{type:"number",properties:["x","y","base","width","height"]}}},Fs.overrides={scales:{_index_:{type:"category",offset:!0,grid:{offset:!0}},_value_:{type:"linear",beginAtZero:!0}}};class zs extends Ps{initialize(){this.enableOptionSharing=!0,super.initialize()}parsePrimitiveData(t,e,i,n){const r=super.parsePrimitiveData(t,e,i,n);for(let t=0;t<r.length;t++)r[t]._custom=this.resolveDataElementOptions(t+i).radius;return r}parseArrayData(t,e,i,n){const r=super.parseArrayData(t,e,i,n);for(let t=0;t<r.length;t++){const n=e[i+t];r[t]._custom=Qi(n[2],this.resolveDataElementOptions(t+i).radius)}return r}parseObjectData(t,e,i,n){const r=super.parseObjectData(t,e,i,n);for(let t=0;t<r.length;t++){const n=e[i+t];r[t]._custom=Qi(n&&n.r&&+n.r,this.resolveDataElementOptions(t+i).radius)}return r}getMaxOverflow(){const t=this._cachedMeta.data;let e=0;for(let i=t.length-1;i>=0;--i)e=Math.max(e,t[i].size(this.resolveDataElementOptions(i))/2);return e>0&&e}getLabelAndValue(t){const e=this._cachedMeta,{xScale:i,yScale:n}=e,r=this.getParsed(t),o=i.getLabelForValue(r.x),s=n.getLabelForValue(r.y),a=r._custom;return{label:e.label,value:"("+o+", "+s+(a?", "+a:"")+")"}}update(t){const e=this._cachedMeta.data;this.updateElements(e,0,e.length,t)}updateElements(t,e,i,n){const r="reset"===n,{iScale:o,vScale:s}=this._cachedMeta,{sharedOptions:a,includeOptions:l}=this._getSharedOptions(e,n),c=o.axis,h=s.axis;for(let u=e;u<e+i;u++){const e=t[u],i=!r&&this.getParsed(u),d={},f=d[c]=r?o.getPixelForDecimal(.5):o.getPixelForValue(i[c]),p=d[h]=r?s.getBasePixel():s.getPixelForValue(i[h]);d.skip=isNaN(f)||isNaN(p),l&&(d.options=a||this.resolveDataElementOptions(u,e.active?"active":n),r&&(d.options.radius=0)),this.updateElement(e,u,d,n)}}resolveDataElementOptions(t,e){const i=this.getParsed(t);let n=super.resolveDataElementOptions(t,e);n.$shared&&(n=Object.assign({},n,{$shared:!1}));const r=n.radius;return"active"!==e&&(n.radius=0),n.radius+=Qi(i&&i._custom,r),n}}zs.id="bubble",zs.defaults={datasetElementType:!1,dataElementType:"point",animations:{numbers:{type:"number",properties:["x","y","borderWidth","radius"]}}},zs.overrides={scales:{x:{type:"linear"},y:{type:"linear"}},plugins:{tooltip:{callbacks:{title:()=>""}}}};class Ns extends Ps{constructor(t,e){super(t,e),this.enableOptionSharing=!0,this.innerRadius=void 0,this.outerRadius=void 0,this.offsetX=void 0,this.offsetY=void 0}linkScales(){}parse(t,e){const i=this.getDataset().data,n=this._cachedMeta;if(!1===this._parsing)n._parsed=i;else{let r,o,s=t=>+i[t];if(Gi(i[t])){const{key:t="value"}=this._parsing;s=e=>+un(i[e],t)}for(r=t,o=t+e;r<o;++r)n._parsed[r]=s(r)}}_getRotation(){return Ln(this.options.rotation-90)}_getCircumference(){return Ln(this.options.circumference)}_getRotationExtents(){let t=bn,e=-bn;for(let i=0;i<this.chart.data.datasets.length;++i)if(this.chart.isDatasetVisible(i)){const n=this.chart.getDatasetMeta(i).controller,r=n._getRotation(),o=n._getCircumference();t=Math.min(t,r),e=Math.max(e,r+o)}return{rotation:t,circumference:e-t}}update(t){const e=this.chart,{chartArea:i}=e,n=this._cachedMeta,r=n.data,o=this.getMaxBorderWidth()+this.getMaxOffset(r)+this.options.spacing,s=Math.max((Math.min(i.width,i.height)-o)/2,0),a=Math.min((l=this.options.cutout,c=s,"string"==typeof l&&l.endsWith("%")?parseFloat(l)/100:l/c),1);var l,c;const h=this._getRingWeight(this.index),{circumference:u,rotation:d}=this._getRotationExtents(),{ratioX:f,ratioY:p,offsetX:g,offsetY:m}=function(t,e,i){let n=1,r=1,o=0,s=0;if(e<bn){const a=t,l=a+e,c=Math.cos(a),h=Math.sin(a),u=Math.cos(l),d=Math.sin(l),f=(t,e,n)=>Fn(t,a,l,!0)?1:Math.max(e,e*i,n,n*i),p=(t,e,n)=>Fn(t,a,l,!0)?-1:Math.min(e,e*i,n,n*i),g=f(0,c,u),m=f(_n,h,d),b=p(mn,c,u),v=p(mn+_n,h,d);n=(g-b)/2,r=(m-v)/2,o=-(g+b)/2,s=-(m+v)/2}return{ratioX:n,ratioY:r,offsetX:o,offsetY:s}}(d,u,a),b=(i.width-o)/f,v=(i.height-o)/p,y=Math.max(Math.min(b,v)/2,0),x=Zi(this.options.radius,y),_=(x-Math.max(x*a,0))/this._getVisibleDatasetWeightTotal();this.offsetX=g*x,this.offsetY=m*x,n.total=this.calculateTotal(),this.outerRadius=x-_*this._getRingWeightOffset(this.index),this.innerRadius=Math.max(this.outerRadius-_*h,0),this.updateElements(r,0,r.length,t)}_circumference(t,e){const i=this.options,n=this._cachedMeta,r=this._getCircumference();return e&&i.animation.animateRotate||!this.chart.getDataVisibility(t)||null===n._parsed[t]||n.data[t].hidden?0:this.calculateCircumference(n._parsed[t]*r/bn)}updateElements(t,e,i,n){const r="reset"===n,o=this.chart,s=o.chartArea,a=o.options.animation,l=(s.left+s.right)/2,c=(s.top+s.bottom)/2,h=r&&a.animateScale,u=h?0:this.innerRadius,d=h?0:this.outerRadius,{sharedOptions:f,includeOptions:p}=this._getSharedOptions(e,n);let g,m=this._getRotation();for(g=0;g<e;++g)m+=this._circumference(g,r);for(g=e;g<e+i;++g){const e=this._circumference(g,r),i=t[g],o={x:l+this.offsetX,y:c+this.offsetY,startAngle:m,endAngle:m+e,circumference:e,outerRadius:d,innerRadius:u};p&&(o.options=f||this.resolveDataElementOptions(g,i.active?"active":n)),m+=e,this.updateElement(i,g,o,n)}}calculateTotal(){const t=this._cachedMeta,e=t.data;let i,n=0;for(i=0;i<e.length;i++){const r=t._parsed[i];null===r||isNaN(r)||!this.chart.getDataVisibility(i)||e[i].hidden||(n+=Math.abs(r))}return n}calculateCircumference(t){const e=this._cachedMeta.total;return e>0&&!isNaN(t)?bn*(Math.abs(t)/e):0}getLabelAndValue(t){const e=this._cachedMeta,i=this.chart,n=i.data.labels||[],r=ts(e._parsed[t],i.options.locale);return{label:n[t]||"",value:r}}getMaxBorderWidth(t){let e=0;const i=this.chart;let n,r,o,s,a;if(!t)for(n=0,r=i.data.datasets.length;n<r;++n)if(i.isDatasetVisible(n)){o=i.getDatasetMeta(n),t=o.data,s=o.controller;break}if(!t)return 0;for(n=0,r=t.length;n<r;++n)a=s.resolveDataElementOptions(n),"inner"!==a.borderAlign&&(e=Math.max(e,a.borderWidth||0,a.hoverBorderWidth||0));return e}getMaxOffset(t){let e=0;for(let i=0,n=t.length;i<n;++i){const t=this.resolveDataElementOptions(i);e=Math.max(e,t.offset||0,t.hoverOffset||0)}return e}_getRingWeightOffset(t){let e=0;for(let i=0;i<t;++i)this.chart.isDatasetVisible(i)&&(e+=this._getRingWeight(i));return e}_getRingWeight(t){return Math.max(Qi(this.chart.data.datasets[t].weight,1),0)}_getVisibleDatasetWeightTotal(){return this._getRingWeightOffset(this.chart.data.datasets.length)||1}}Ns.id="doughnut",Ns.defaults={datasetElementType:!1,dataElementType:"arc",animation:{animateRotate:!0,animateScale:!1},animations:{numbers:{type:"number",properties:["circumference","endAngle","innerRadius","outerRadius","startAngle","x","y","offset","borderWidth","spacing"]}},cutout:"50%",rotation:0,circumference:360,radius:"100%",spacing:0,indexAxis:"r"},Ns.descriptors={_scriptable:t=>"spacing"!==t,_indexable:t=>"spacing"!==t},Ns.overrides={aspectRatio:1,plugins:{legend:{labels:{generateLabels(t){const e=t.data;if(e.labels.length&&e.datasets.length){const{labels:{pointStyle:i}}=t.legend.options;return e.labels.map(((e,n)=>{const r=t.getDatasetMeta(0).controller.getStyle(n);return{text:e,fillStyle:r.backgroundColor,strokeStyle:r.borderColor,lineWidth:r.borderWidth,pointStyle:i,hidden:!t.getDataVisibility(n),index:n}}))}return[]}},onClick(t,e,i){i.chart.toggleDataVisibility(e.index),i.chart.update()}},tooltip:{callbacks:{title:()=>"",label(t){let e=t.label;const i=": "+t.formattedValue;return Xi(e)?(e=e.slice(),e[0]+=i):e+=i,e}}}}};class Bs extends Ps{initialize(){this.enableOptionSharing=!0,this.supportsDecimation=!0,super.initialize()}update(t){const e=this._cachedMeta,{dataset:i,data:n=[],_dataset:r}=e,o=this.chart._animationsDisabled;let{start:s,count:a}=Kn(e,n,o);this._drawStart=s,this._drawCount=a,Jn(e)&&(s=0,a=n.length),i._chart=this.chart,i._datasetIndex=this.index,i._decimated=!!r._decimated,i.points=n;const l=this.resolveDatasetElementOptions(t);this.options.showLine||(l.borderWidth=0),l.segment=this.options.segment,this.updateElement(i,void 0,{animated:!o,options:l},t),this.updateElements(n,s,a,t)}updateElements(t,e,i,n){const r="reset"===n,{iScale:o,vScale:s,_stacked:a,_dataset:l}=this._cachedMeta,{sharedOptions:c,includeOptions:h}=this._getSharedOptions(e,n),u=o.axis,d=s.axis,{spanGaps:f,segment:p}=this.options,g=En(f)?f:Number.POSITIVE_INFINITY,m=this.chart._animationsDisabled||r||"none"===n;let b=e>0&&this.getParsed(e-1);for(let f=e;f<e+i;++f){const e=t[f],i=this.getParsed(f),v=m?e:{},y=Yi(i[d]),x=v[u]=o.getPixelForValue(i[u],f),_=v[d]=r||y?s.getBasePixel():s.getPixelForValue(a?this.applyStack(s,i,a):i[d],f);v.skip=isNaN(x)||isNaN(_)||y,v.stop=f>0&&Math.abs(i[u]-b[u])>g,p&&(v.parsed=i,v.raw=l.data[f]),h&&(v.options=c||this.resolveDataElementOptions(f,e.active?"active":n)),m||this.updateElement(e,f,v,n),b=i}}getMaxOverflow(){const t=this._cachedMeta,e=t.dataset,i=e.options&&e.options.borderWidth||0,n=t.data||[];if(!n.length)return i;const r=n[0].size(this.resolveDataElementOptions(0)),o=n[n.length-1].size(this.resolveDataElementOptions(n.length-1));return Math.max(i,r,o)/2}draw(){const t=this._cachedMeta;t.dataset.updateControlPoints(this.chart.chartArea,t.iScale.axis),super.draw()}}Bs.id="line",Bs.defaults={datasetElementType:"line",dataElementType:"point",showLine:!0,spanGaps:!1},Bs.overrides={scales:{_index_:{type:"category"},_value_:{type:"linear"}}};class Ws extends Ps{constructor(t,e){super(t,e),this.innerRadius=void 0,this.outerRadius=void 0}getLabelAndValue(t){const e=this._cachedMeta,i=this.chart,n=i.data.labels||[],r=ts(e._parsed[t].r,i.options.locale);return{label:n[t]||"",value:r}}parseObjectData(t,e,i,n){return To.bind(this)(t,e,i,n)}update(t){const e=this._cachedMeta.data;this._updateRadius(),this.updateElements(e,0,e.length,t)}getMinMax(){const t=this._cachedMeta,e={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY};return t.data.forEach(((t,i)=>{const n=this.getParsed(i).r;!isNaN(n)&&this.chart.getDataVisibility(i)&&(n<e.min&&(e.min=n),n>e.max&&(e.max=n))})),e}_updateRadius(){const t=this.chart,e=t.chartArea,i=t.options,n=Math.min(e.right-e.left,e.bottom-e.top),r=Math.max(n/2,0),o=(r-Math.max(i.cutoutPercentage?r/100*i.cutoutPercentage:1,0))/t.getVisibleDatasetCount();this.outerRadius=r-o*this.index,this.innerRadius=this.outerRadius-o}updateElements(t,e,i,n){const r="reset"===n,o=this.chart,s=o.options.animation,a=this._cachedMeta.rScale,l=a.xCenter,c=a.yCenter,h=a.getIndexAngle(0)-.5*mn;let u,d=h;const f=360/this.countVisibleElements();for(u=0;u<e;++u)d+=this._computeAngle(u,n,f);for(u=e;u<e+i;u++){const e=t[u];let i=d,p=d+this._computeAngle(u,n,f),g=o.getDataVisibility(u)?a.getDistanceFromCenterForValue(this.getParsed(u).r):0;d=p,r&&(s.animateScale&&(g=0),s.animateRotate&&(i=p=h));const m={x:l,y:c,innerRadius:0,outerRadius:g,startAngle:i,endAngle:p,options:this.resolveDataElementOptions(u,e.active?"active":n)};this.updateElement(e,u,m,n)}}countVisibleElements(){const t=this._cachedMeta;let e=0;return t.data.forEach(((t,i)=>{!isNaN(this.getParsed(i).r)&&this.chart.getDataVisibility(i)&&e++})),e}_computeAngle(t,e,i){return this.chart.getDataVisibility(t)?Ln(this.resolveDataElementOptions(t,e).angle||i):0}}Ws.id="polarArea",Ws.defaults={dataElementType:"arc",animation:{animateRotate:!0,animateScale:!0},animations:{numbers:{type:"number",properties:["x","y","startAngle","endAngle","innerRadius","outerRadius"]}},indexAxis:"r",startAngle:0},Ws.overrides={aspectRatio:1,plugins:{legend:{labels:{generateLabels(t){const e=t.data;if(e.labels.length&&e.datasets.length){const{labels:{pointStyle:i}}=t.legend.options;return e.labels.map(((e,n)=>{const r=t.getDatasetMeta(0).controller.getStyle(n);return{text:e,fillStyle:r.backgroundColor,strokeStyle:r.borderColor,lineWidth:r.borderWidth,pointStyle:i,hidden:!t.getDataVisibility(n),index:n}}))}return[]}},onClick(t,e,i){i.chart.toggleDataVisibility(e.index),i.chart.update()}},tooltip:{callbacks:{title:()=>"",label:t=>t.chart.data.labels[t.dataIndex]+": "+t.formattedValue}}},scales:{r:{type:"radialLinear",angleLines:{display:!1},beginAtZero:!0,grid:{circular:!0},pointLabels:{display:!1},startAngle:0}}};class Vs extends Ns{}Vs.id="pie",Vs.defaults={cutout:0,rotation:0,circumference:360,radius:"100%"};class Hs extends Ps{getLabelAndValue(t){const e=this._cachedMeta.vScale,i=this.getParsed(t);return{label:e.getLabels()[t],value:""+e.getLabelForValue(i[e.axis])}}parseObjectData(t,e,i,n){return To.bind(this)(t,e,i,n)}update(t){const e=this._cachedMeta,i=e.dataset,n=e.data||[],r=e.iScale.getLabels();if(i.points=n,"resize"!==t){const e=this.resolveDatasetElementOptions(t);this.options.showLine||(e.borderWidth=0);const o={_loop:!0,_fullLoop:r.length===n.length,options:e};this.updateElement(i,void 0,o,t)}this.updateElements(n,0,n.length,t)}updateElements(t,e,i,n){const r=this._cachedMeta.rScale,o="reset"===n;for(let s=e;s<e+i;s++){const e=t[s],i=this.resolveDataElementOptions(s,e.active?"active":n),a=r.getPointPositionForValue(s,this.getParsed(s).r),l=o?r.xCenter:a.x,c=o?r.yCenter:a.y,h={x:l,y:c,angle:a.angle,skip:isNaN(l)||isNaN(c),options:i};this.updateElement(e,s,h,n)}}}Hs.id="radar",Hs.defaults={datasetElementType:"line",dataElementType:"point",indexAxis:"r",showLine:!0,elements:{line:{fill:"start"}}},Hs.overrides={aspectRatio:1,scales:{r:{type:"radialLinear"}}};class $s{constructor(){this.x=void 0,this.y=void 0,this.active=!1,this.options=void 0,this.$animations=void 0}tooltipPosition(t){const{x:e,y:i}=this.getProps(["x","y"],t);return{x:e,y:i}}hasValue(){return En(this.x)&&En(this.y)}getProps(t,e){const i=this.$animations;if(!e||!i)return this;const n={};return t.forEach((t=>{n[t]=i[t]&&i[t].active()?i[t]._to:this[t]})),n}}$s.defaults={},$s.defaultRoutes=void 0;const Us={values:t=>Xi(t)?t:""+t,numeric(t,e,i){if(0===t)return"0";const n=this.chart.options.locale;let r,o=t;if(i.length>1){const e=Math.max(Math.abs(i[0].value),Math.abs(i[i.length-1].value));(e<1e-4||e>1e15)&&(r="scientific"),o=function(t,e){let i=e.length>3?e[2].value-e[1].value:e[1].value-e[0].value;Math.abs(i)>=1&&t!==Math.floor(t)&&(i=t-Math.floor(t));return i}(t,i)}const s=On(Math.abs(o)),a=Math.max(Math.min(-1*Math.floor(s),20),0),l={notation:r,minimumFractionDigits:a,maximumFractionDigits:a};return Object.assign(l,this.options.ticks.format),ts(t,n,l)},logarithmic(t,e,i){if(0===t)return"0";const n=t/Math.pow(10,Math.floor(On(t)));return 1===n||2===n||5===n?Us.numeric.call(this,t,e,i):""}};var qs={formatters:Us};function Ys(t,e){const i=t.options.ticks,n=i.maxTicksLimit||function(t){const e=t.options.offset,i=t._tickSize(),n=t._length/i+(e?0:1),r=t._maxLength/i;return Math.floor(Math.min(n,r))}(t),r=i.major.enabled?function(t){const e=[];let i,n;for(i=0,n=t.length;i<n;i++)t[i].major&&e.push(i);return e}(e):[],o=r.length,s=r[0],a=r[o-1],l=[];if(o>n)return function(t,e,i,n){let r,o=0,s=i[0];for(n=Math.ceil(n),r=0;r<t.length;r++)r===s&&(e.push(t[r]),o++,s=i[o*n])}(e,l,r,o/n),l;const c=function(t,e,i){const n=function(t){const e=t.length;let i,n;if(e<2)return!1;for(n=t[0],i=1;i<e;++i)if(t[i]-t[i-1]!==n)return!1;return n}(t),r=e.length/i;if(!n)return Math.max(r,1);const o=function(t){const e=[],i=Math.sqrt(t);let n;for(n=1;n<i;n++)t%n==0&&(e.push(n),e.push(t/n));return i===(0|i)&&e.push(i),e.sort(((t,e)=>t-e)).pop(),e}(n);for(let t=0,e=o.length-1;t<e;t++){const e=o[t];if(e>r)return e}return Math.max(r,1)}(r,e,n);if(o>0){let t,i;const n=o>1?Math.round((a-s)/(o-1)):null;for(Xs(e,l,c,Yi(n)?0:s-n,s),t=0,i=o-1;t<i;t++)Xs(e,l,c,r[t],r[t+1]);return Xs(e,l,c,a,Yi(n)?e.length:a+n),l}return Xs(e,l,c),l}function Xs(t,e,i,n,r){const o=Qi(n,0),s=Math.min(Qi(r,t.length),t.length);let a,l,c,h=0;for(i=Math.ceil(i),r&&(a=r-n,i=a/Math.floor(a/i)),c=o;c<0;)h++,c=Math.round(o+h*i);for(l=Math.max(o,0);l<s;l++)l===c&&(e.push(t[l]),h++,c=Math.round(o+h*i))}Hr.set("scale",{display:!0,offset:!1,reverse:!1,beginAtZero:!1,bounds:"ticks",grace:0,grid:{display:!0,lineWidth:1,drawBorder:!0,drawOnChartArea:!0,drawTicks:!0,tickLength:8,tickWidth:(t,e)=>e.lineWidth,tickColor:(t,e)=>e.color,offset:!1,borderDash:[],borderDashOffset:0,borderWidth:1},title:{display:!1,text:"",padding:{top:4,bottom:4}},ticks:{minRotation:0,maxRotation:50,mirror:!1,textStrokeWidth:0,textStrokeColor:"",padding:3,display:!0,autoSkip:!0,autoSkipPadding:3,labelOffset:0,callback:qs.formatters.values,minor:{},major:{},align:"center",crossAlign:"near",showLabelBackdrop:!1,backdropColor:"rgba(255, 255, 255, 0.75)",backdropPadding:2}}),Hr.route("scale.ticks","color","","color"),Hr.route("scale.grid","color","","borderColor"),Hr.route("scale.grid","borderColor","","borderColor"),Hr.route("scale.title","color","","color"),Hr.describe("scale",{_fallback:!1,_scriptable:t=>!t.startsWith("before")&&!t.startsWith("after")&&"callback"!==t&&"parser"!==t,_indexable:t=>"borderDash"!==t&&"tickBorderDash"!==t}),Hr.describe("scales",{_fallback:"scale"}),Hr.describe("scale.ticks",{_scriptable:t=>"backdropPadding"!==t&&"callback"!==t,_indexable:t=>"backdropPadding"!==t});const Gs=(t,e,i)=>"top"===e||"left"===e?t[e]+i:t[e]-i;function Ks(t,e){const i=[],n=t.length/e,r=t.length;let o=0;for(;o<r;o+=n)i.push(t[Math.floor(o)]);return i}function Js(t,e,i){const n=t.ticks.length,r=Math.min(e,n-1),o=t._startPixel,s=t._endPixel,a=1e-6;let l,c=t.getPixelForTick(r);if(!(i&&(l=1===n?Math.max(c-o,s-c):0===e?(t.getPixelForTick(1)-c)/2:(c-t.getPixelForTick(r-1))/2,c+=r<e?l:-l,c<o-a||c>s+a)))return c}function Qs(t){return t.drawTicks?t.tickLength:0}function Zs(t,e){if(!t.display)return 0;const i=fo(t.font,e),n=uo(t.padding);return(Xi(t.text)?t.text.length:1)*i.lineHeight+n.height}function ta(t,e,i){let n=Xn(t);return(i&&"right"!==e||!i&&"right"===e)&&(n=(t=>"left"===t?"right":"right"===t?"left":t)(n)),n}class ea extends $s{constructor(t){super(),this.id=t.id,this.type=t.type,this.options=void 0,this.ctx=t.ctx,this.chart=t.chart,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this._margins={left:0,right:0,top:0,bottom:0},this.maxWidth=void 0,this.maxHeight=void 0,this.paddingTop=void 0,this.paddingBottom=void 0,this.paddingLeft=void 0,this.paddingRight=void 0,this.axis=void 0,this.labelRotation=void 0,this.min=void 0,this.max=void 0,this._range=void 0,this.ticks=[],this._gridLineItems=null,this._labelItems=null,this._labelSizes=null,this._length=0,this._maxLength=0,this._longestTextCache={},this._startPixel=void 0,this._endPixel=void 0,this._reversePixels=!1,this._userMax=void 0,this._userMin=void 0,this._suggestedMax=void 0,this._suggestedMin=void 0,this._ticksLength=0,this._borderValue=0,this._cache={},this._dataLimitsCached=!1,this.$context=void 0}init(t){this.options=t.setContext(this.getContext()),this.axis=t.axis,this._userMin=this.parse(t.min),this._userMax=this.parse(t.max),this._suggestedMin=this.parse(t.suggestedMin),this._suggestedMax=this.parse(t.suggestedMax)}parse(t,e){return t}getUserBounds(){let{_userMin:t,_userMax:e,_suggestedMin:i,_suggestedMax:n}=this;return t=Ji(t,Number.POSITIVE_INFINITY),e=Ji(e,Number.NEGATIVE_INFINITY),i=Ji(i,Number.POSITIVE_INFINITY),n=Ji(n,Number.NEGATIVE_INFINITY),{min:Ji(t,i),max:Ji(e,n),minDefined:Ki(t),maxDefined:Ki(e)}}getMinMax(t){let e,{min:i,max:n,minDefined:r,maxDefined:o}=this.getUserBounds();if(r&&o)return{min:i,max:n};const s=this.getMatchingVisibleMetas();for(let a=0,l=s.length;a<l;++a)e=s[a].controller.getMinMax(this,t),r||(i=Math.min(i,e.min)),o||(n=Math.max(n,e.max));return i=o&&i>n?n:i,n=r&&i>n?i:n,{min:Ji(i,Ji(n,i)),max:Ji(n,Ji(i,n))}}getPadding(){return{left:this.paddingLeft||0,top:this.paddingTop||0,right:this.paddingRight||0,bottom:this.paddingBottom||0}}getTicks(){return this.ticks}getLabels(){const t=this.chart.data;return this.options.labels||(this.isHorizontal()?t.xLabels:t.yLabels)||t.labels||[]}beforeLayout(){this._cache={},this._dataLimitsCached=!1}beforeUpdate(){tn(this.options.beforeUpdate,[this])}update(t,e,i){const{beginAtZero:n,grace:r,ticks:o}=this.options,s=o.sampleSize;this.beforeUpdate(),this.maxWidth=t,this.maxHeight=e,this._margins=i=Object.assign({left:0,right:0,top:0,bottom:0},i),this.ticks=null,this._labelSizes=null,this._gridLineItems=null,this._labelItems=null,this.beforeSetDimensions(),this.setDimensions(),this.afterSetDimensions(),this._maxLength=this.isHorizontal()?this.width+i.left+i.right:this.height+i.top+i.bottom,this._dataLimitsCached||(this.beforeDataLimits(),this.determineDataLimits(),this.afterDataLimits(),this._range=function(t,e,i){const{min:n,max:r}=t,o=Zi(e,(r-n)/2),s=(t,e)=>i&&0===t?0:t+e;return{min:s(n,-Math.abs(o)),max:s(r,o)}}(this,r,n),this._dataLimitsCached=!0),this.beforeBuildTicks(),this.ticks=this.buildTicks()||[],this.afterBuildTicks();const a=s<this.ticks.length;this._convertTicksToLabels(a?Ks(this.ticks,s):this.ticks),this.configure(),this.beforeCalculateLabelRotation(),this.calculateLabelRotation(),this.afterCalculateLabelRotation(),o.display&&(o.autoSkip||"auto"===o.source)&&(this.ticks=Ys(this,this.ticks),this._labelSizes=null,this.afterAutoSkip()),a&&this._convertTicksToLabels(this.ticks),this.beforeFit(),this.fit(),this.afterFit(),this.afterUpdate()}configure(){let t,e,i=this.options.reverse;this.isHorizontal()?(t=this.left,e=this.right):(t=this.top,e=this.bottom,i=!i),this._startPixel=t,this._endPixel=e,this._reversePixels=i,this._length=e-t,this._alignToPixels=this.options.alignToPixels}afterUpdate(){tn(this.options.afterUpdate,[this])}beforeSetDimensions(){tn(this.options.beforeSetDimensions,[this])}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=0,this.right=this.width):(this.height=this.maxHeight,this.top=0,this.bottom=this.height),this.paddingLeft=0,this.paddingTop=0,this.paddingRight=0,this.paddingBottom=0}afterSetDimensions(){tn(this.options.afterSetDimensions,[this])}_callHooks(t){this.chart.notifyPlugins(t,this.getContext()),tn(this.options[t],[this])}beforeDataLimits(){this._callHooks("beforeDataLimits")}determineDataLimits(){}afterDataLimits(){this._callHooks("afterDataLimits")}beforeBuildTicks(){this._callHooks("beforeBuildTicks")}buildTicks(){return[]}afterBuildTicks(){this._callHooks("afterBuildTicks")}beforeTickToLabelConversion(){tn(this.options.beforeTickToLabelConversion,[this])}generateTickLabels(t){const e=this.options.ticks;let i,n,r;for(i=0,n=t.length;i<n;i++)r=t[i],r.label=tn(e.callback,[r.value,i,t],this)}afterTickToLabelConversion(){tn(this.options.afterTickToLabelConversion,[this])}beforeCalculateLabelRotation(){tn(this.options.beforeCalculateLabelRotation,[this])}calculateLabelRotation(){const t=this.options,e=t.ticks,i=this.ticks.length,n=e.minRotation||0,r=e.maxRotation;let o,s,a,l=n;if(!this._isVisible()||!e.display||n>=r||i<=1||!this.isHorizontal())return void(this.labelRotation=n);const c=this._getLabelSizes(),h=c.widest.width,u=c.highest.height,d=zn(this.chart.width-h,0,this.maxWidth);o=t.offset?this.maxWidth/i:d/(i-1),h+6>o&&(o=d/(i-(t.offset?.5:1)),s=this.maxHeight-Qs(t.grid)-e.padding-Zs(t.title,this.chart.options.font),a=Math.sqrt(h*h+u*u),l=An(Math.min(Math.asin(zn((c.highest.height+6)/o,-1,1)),Math.asin(zn(s/a,-1,1))-Math.asin(zn(u/a,-1,1)))),l=Math.max(n,Math.min(r,l))),this.labelRotation=l}afterCalculateLabelRotation(){tn(this.options.afterCalculateLabelRotation,[this])}afterAutoSkip(){}beforeFit(){tn(this.options.beforeFit,[this])}fit(){const t={width:0,height:0},{chart:e,options:{ticks:i,title:n,grid:r}}=this,o=this._isVisible(),s=this.isHorizontal();if(o){const o=Zs(n,e.options.font);if(s?(t.width=this.maxWidth,t.height=Qs(r)+o):(t.height=this.maxHeight,t.width=Qs(r)+o),i.display&&this.ticks.length){const{first:e,last:n,widest:r,highest:o}=this._getLabelSizes(),a=2*i.padding,l=Ln(this.labelRotation),c=Math.cos(l),h=Math.sin(l);if(s){const e=i.mirror?0:h*r.width+c*o.height;t.height=Math.min(this.maxHeight,t.height+e+a)}else{const e=i.mirror?0:c*r.width+h*o.height;t.width=Math.min(this.maxWidth,t.width+e+a)}this._calculatePadding(e,n,h,c)}}this._handleMargins(),s?(this.width=this._length=e.width-this._margins.left-this._margins.right,this.height=t.height):(this.width=t.width,this.height=this._length=e.height-this._margins.top-this._margins.bottom)}_calculatePadding(t,e,i,n){const{ticks:{align:r,padding:o},position:s}=this.options,a=0!==this.labelRotation,l="top"!==s&&"x"===this.axis;if(this.isHorizontal()){const s=this.getPixelForTick(0)-this.left,c=this.right-this.getPixelForTick(this.ticks.length-1);let h=0,u=0;a?l?(h=n*t.width,u=i*e.height):(h=i*t.height,u=n*e.width):"start"===r?u=e.width:"end"===r?h=t.width:"inner"!==r&&(h=t.width/2,u=e.width/2),this.paddingLeft=Math.max((h-s+o)*this.width/(this.width-s),0),this.paddingRight=Math.max((u-c+o)*this.width/(this.width-c),0)}else{let i=e.height/2,n=t.height/2;"start"===r?(i=0,n=t.height):"end"===r&&(i=e.height,n=0),this.paddingTop=i+o,this.paddingBottom=n+o}}_handleMargins(){this._margins&&(this._margins.left=Math.max(this.paddingLeft,this._margins.left),this._margins.top=Math.max(this.paddingTop,this._margins.top),this._margins.right=Math.max(this.paddingRight,this._margins.right),this._margins.bottom=Math.max(this.paddingBottom,this._margins.bottom))}afterFit(){tn(this.options.afterFit,[this])}isHorizontal(){const{axis:t,position:e}=this.options;return"top"===e||"bottom"===e||"x"===t}isFullSize(){return this.options.fullSize}_convertTicksToLabels(t){let e,i;for(this.beforeTickToLabelConversion(),this.generateTickLabels(t),e=0,i=t.length;e<i;e++)Yi(t[e].label)&&(t.splice(e,1),i--,e--);this.afterTickToLabelConversion()}_getLabelSizes(){let t=this._labelSizes;if(!t){const e=this.options.ticks.sampleSize;let i=this.ticks;e<i.length&&(i=Ks(i,e)),this._labelSizes=t=this._computeLabelSizes(i,i.length)}return t}_computeLabelSizes(t,e){const{ctx:i,_longestTextCache:n}=this,r=[],o=[];let s,a,l,c,h,u,d,f,p,g,m,b=0,v=0;for(s=0;s<e;++s){if(c=t[s].label,h=this._resolveTickFontOptions(s),i.font=u=h.string,d=n[u]=n[u]||{data:{},gc:[]},f=h.lineHeight,p=g=0,Yi(c)||Xi(c)){if(Xi(c))for(a=0,l=c.length;a<l;++a)m=c[a],Yi(m)||Xi(m)||(p=$r(i,d.data,d.gc,p,m),g+=f)}else p=$r(i,d.data,d.gc,p,c),g=f;r.push(p),o.push(g),b=Math.max(p,b),v=Math.max(g,v)}!function(t,e){en(t,(t=>{const i=t.gc,n=i.length/2;let r;if(n>e){for(r=0;r<n;++r)delete t.data[i[r]];i.splice(0,n)}}))}(n,e);const y=r.indexOf(b),x=o.indexOf(v),_=t=>({width:r[t]||0,height:o[t]||0});return{first:_(0),last:_(e-1),widest:_(y),highest:_(x),widths:r,heights:o}}getLabelForValue(t){return t}getPixelForValue(t,e){return NaN}getValueForPixel(t){}getPixelForTick(t){const e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getPixelForDecimal(t){this._reversePixels&&(t=1-t);const e=this._startPixel+t*this._length;return zn(this._alignToPixels?qr(this.chart,e,0):e,-32768,32767)}getDecimalForPixel(t){const e=(t-this._startPixel)/this._length;return this._reversePixels?1-e:e}getBasePixel(){return this.getPixelForValue(this.getBaseValue())}getBaseValue(){const{min:t,max:e}=this;return t<0&&e<0?e:t>0&&e>0?t:0}getContext(t){const e=this.ticks||[];if(t>=0&&t<e.length){const i=e[t];return i.$context||(i.$context=function(t,e,i){return go(t,{tick:i,index:e,type:"tick"})}(this.getContext(),t,i))}return this.$context||(this.$context=go(this.chart.getContext(),{scale:this,type:"scale"}))}_tickSize(){const t=this.options.ticks,e=Ln(this.labelRotation),i=Math.abs(Math.cos(e)),n=Math.abs(Math.sin(e)),r=this._getLabelSizes(),o=t.autoSkipPadding||0,s=r?r.widest.width+o:0,a=r?r.highest.height+o:0;return this.isHorizontal()?a*i>s*n?s/i:a/n:a*n<s*i?a/i:s/n}_isVisible(){const t=this.options.display;return"auto"!==t?!!t:this.getMatchingVisibleMetas().length>0}_computeGridLineItems(t){const e=this.axis,i=this.chart,n=this.options,{grid:r,position:o}=n,s=r.offset,a=this.isHorizontal(),l=this.ticks.length+(s?1:0),c=Qs(r),h=[],u=r.setContext(this.getContext()),d=u.drawBorder?u.borderWidth:0,f=d/2,p=function(t){return qr(i,t,d)};let g,m,b,v,y,x,_,w,k,O,S,M;if("top"===o)g=p(this.bottom),x=this.bottom-c,w=g-f,O=p(t.top)+f,M=t.bottom;else if("bottom"===o)g=p(this.top),O=t.top,M=p(t.bottom)-f,x=g+f,w=this.top+c;else if("left"===o)g=p(this.right),y=this.right-c,_=g-f,k=p(t.left)+f,S=t.right;else if("right"===o)g=p(this.left),k=t.left,S=p(t.right)-f,y=g+f,_=this.left+c;else if("x"===e){if("center"===o)g=p((t.top+t.bottom)/2+.5);else if(Gi(o)){const t=Object.keys(o)[0],e=o[t];g=p(this.chart.scales[t].getPixelForValue(e))}O=t.top,M=t.bottom,x=g+f,w=x+c}else if("y"===e){if("center"===o)g=p((t.left+t.right)/2);else if(Gi(o)){const t=Object.keys(o)[0],e=o[t];g=p(this.chart.scales[t].getPixelForValue(e))}y=g-f,_=y-c,k=t.left,S=t.right}const E=Qi(n.ticks.maxTicksLimit,l),P=Math.max(1,Math.ceil(l/E));for(m=0;m<l;m+=P){const t=r.setContext(this.getContext(m)),e=t.lineWidth,n=t.color,o=t.borderDash||[],l=t.borderDashOffset,c=t.tickWidth,u=t.tickColor,d=t.tickBorderDash||[],f=t.tickBorderDashOffset;b=Js(this,m,s),void 0!==b&&(v=qr(i,b,e),a?y=_=k=S=v:x=w=O=M=v,h.push({tx1:y,ty1:x,tx2:_,ty2:w,x1:k,y1:O,x2:S,y2:M,width:e,color:n,borderDash:o,borderDashOffset:l,tickWidth:c,tickColor:u,tickBorderDash:d,tickBorderDashOffset:f}))}return this._ticksLength=l,this._borderValue=g,h}_computeLabelItems(t){const e=this.axis,i=this.options,{position:n,ticks:r}=i,o=this.isHorizontal(),s=this.ticks,{align:a,crossAlign:l,padding:c,mirror:h}=r,u=Qs(i.grid),d=u+c,f=h?-c:d,p=-Ln(this.labelRotation),g=[];let m,b,v,y,x,_,w,k,O,S,M,E,P="middle";if("top"===n)_=this.bottom-f,w=this._getXAxisLabelAlignment();else if("bottom"===n)_=this.top+f,w=this._getXAxisLabelAlignment();else if("left"===n){const t=this._getYAxisLabelAlignment(u);w=t.textAlign,x=t.x}else if("right"===n){const t=this._getYAxisLabelAlignment(u);w=t.textAlign,x=t.x}else if("x"===e){if("center"===n)_=(t.top+t.bottom)/2+d;else if(Gi(n)){const t=Object.keys(n)[0],e=n[t];_=this.chart.scales[t].getPixelForValue(e)+d}w=this._getXAxisLabelAlignment()}else if("y"===e){if("center"===n)x=(t.left+t.right)/2-d;else if(Gi(n)){const t=Object.keys(n)[0],e=n[t];x=this.chart.scales[t].getPixelForValue(e)}w=this._getYAxisLabelAlignment(u).textAlign}"y"===e&&("start"===a?P="top":"end"===a&&(P="bottom"));const T=this._getLabelSizes();for(m=0,b=s.length;m<b;++m){v=s[m],y=v.label;const t=r.setContext(this.getContext(m));k=this.getPixelForTick(m)+r.labelOffset,O=this._resolveTickFontOptions(m),S=O.lineHeight,M=Xi(y)?y.length:1;const e=M/2,i=t.color,a=t.textStrokeColor,c=t.textStrokeWidth;let u,d=w;if(o?(x=k,"inner"===w&&(d=m===b-1?this.options.reverse?"left":"right":0===m?this.options.reverse?"right":"left":"center"),E="top"===n?"near"===l||0!==p?-M*S+S/2:"center"===l?-T.highest.height/2-e*S+S:-T.highest.height+S/2:"near"===l||0!==p?S/2:"center"===l?T.highest.height/2-e*S:T.highest.height-M*S,h&&(E*=-1)):(_=k,E=(1-M)*S/2),t.showLabelBackdrop){const e=uo(t.backdropPadding),i=T.heights[m],n=T.widths[m];let r=_+E-e.top,o=x-e.left;switch(P){case"middle":r-=i/2;break;case"bottom":r-=i}switch(w){case"center":o-=n/2;break;case"right":o-=n}u={left:o,top:r,width:n+e.width,height:i+e.height,color:t.backdropColor}}g.push({rotation:p,label:y,font:O,color:i,strokeColor:a,strokeWidth:c,textOffset:E,textAlign:d,textBaseline:P,translation:[x,_],backdrop:u})}return g}_getXAxisLabelAlignment(){const{position:t,ticks:e}=this.options;if(-Ln(this.labelRotation))return"top"===t?"left":"right";let i="center";return"start"===e.align?i="left":"end"===e.align?i="right":"inner"===e.align&&(i="inner"),i}_getYAxisLabelAlignment(t){const{position:e,ticks:{crossAlign:i,mirror:n,padding:r}}=this.options,o=t+r,s=this._getLabelSizes().widest.width;let a,l;return"left"===e?n?(l=this.right+r,"near"===i?a="left":"center"===i?(a="center",l+=s/2):(a="right",l+=s)):(l=this.right-o,"near"===i?a="right":"center"===i?(a="center",l-=s/2):(a="left",l=this.left)):"right"===e?n?(l=this.left+r,"near"===i?a="right":"center"===i?(a="center",l-=s/2):(a="left",l-=s)):(l=this.left+o,"near"===i?a="left":"center"===i?(a="center",l+=s/2):(a="right",l=this.right)):a="right",{textAlign:a,x:l}}_computeLabelArea(){if(this.options.ticks.mirror)return;const t=this.chart,e=this.options.position;return"left"===e||"right"===e?{top:0,left:this.left,bottom:t.height,right:this.right}:"top"===e||"bottom"===e?{top:this.top,left:0,bottom:this.bottom,right:t.width}:void 0}drawBackground(){const{ctx:t,options:{backgroundColor:e},left:i,top:n,width:r,height:o}=this;e&&(t.save(),t.fillStyle=e,t.fillRect(i,n,r,o),t.restore())}getLineWidthForValue(t){const e=this.options.grid;if(!this._isVisible()||!e.display)return 0;const i=this.ticks.findIndex((e=>e.value===t));if(i>=0){return e.setContext(this.getContext(i)).lineWidth}return 0}drawGrid(t){const e=this.options.grid,i=this.ctx,n=this._gridLineItems||(this._gridLineItems=this._computeGridLineItems(t));let r,o;const s=(t,e,n)=>{n.width&&n.color&&(i.save(),i.lineWidth=n.width,i.strokeStyle=n.color,i.setLineDash(n.borderDash||[]),i.lineDashOffset=n.borderDashOffset,i.beginPath(),i.moveTo(t.x,t.y),i.lineTo(e.x,e.y),i.stroke(),i.restore())};if(e.display)for(r=0,o=n.length;r<o;++r){const t=n[r];e.drawOnChartArea&&s({x:t.x1,y:t.y1},{x:t.x2,y:t.y2},t),e.drawTicks&&s({x:t.tx1,y:t.ty1},{x:t.tx2,y:t.ty2},{color:t.tickColor,width:t.tickWidth,borderDash:t.tickBorderDash,borderDashOffset:t.tickBorderDashOffset})}}drawBorder(){const{chart:t,ctx:e,options:{grid:i}}=this,n=i.setContext(this.getContext()),r=i.drawBorder?n.borderWidth:0;if(!r)return;const o=i.setContext(this.getContext(0)).lineWidth,s=this._borderValue;let a,l,c,h;this.isHorizontal()?(a=qr(t,this.left,r)-r/2,l=qr(t,this.right,o)+o/2,c=h=s):(c=qr(t,this.top,r)-r/2,h=qr(t,this.bottom,o)+o/2,a=l=s),e.save(),e.lineWidth=n.borderWidth,e.strokeStyle=n.borderColor,e.beginPath(),e.moveTo(a,c),e.lineTo(l,h),e.stroke(),e.restore()}drawLabels(t){if(!this.options.ticks.display)return;const e=this.ctx,i=this._computeLabelArea();i&&Jr(e,i);const n=this._labelItems||(this._labelItems=this._computeLabelItems(t));let r,o;for(r=0,o=n.length;r<o;++r){const t=n[r],i=t.font,o=t.label;t.backdrop&&(e.fillStyle=t.backdrop.color,e.fillRect(t.backdrop.left,t.backdrop.top,t.backdrop.width,t.backdrop.height)),eo(e,o,0,t.textOffset,i,t)}i&&Qr(e)}drawTitle(){const{ctx:t,options:{position:e,title:i,reverse:n}}=this;if(!i.display)return;const r=fo(i.font),o=uo(i.padding),s=i.align;let a=r.lineHeight/2;"bottom"===e||"center"===e||Gi(e)?(a+=o.bottom,Xi(i.text)&&(a+=r.lineHeight*(i.text.length-1))):a+=o.top;const{titleX:l,titleY:c,maxWidth:h,rotation:u}=function(t,e,i,n){const{top:r,left:o,bottom:s,right:a,chart:l}=t,{chartArea:c,scales:h}=l;let u,d,f,p=0;const g=s-r,m=a-o;if(t.isHorizontal()){if(d=Gn(n,o,a),Gi(i)){const t=Object.keys(i)[0],n=i[t];f=h[t].getPixelForValue(n)+g-e}else f="center"===i?(c.bottom+c.top)/2+g-e:Gs(t,i,e);u=a-o}else{if(Gi(i)){const t=Object.keys(i)[0],n=i[t];d=h[t].getPixelForValue(n)-m+e}else d="center"===i?(c.left+c.right)/2-m+e:Gs(t,i,e);f=Gn(n,s,r),p="left"===i?-_n:_n}return{titleX:d,titleY:f,maxWidth:u,rotation:p}}(this,a,e,s);eo(t,i.text,0,0,r,{color:i.color,maxWidth:h,rotation:u,textAlign:ta(s,e,n),textBaseline:"middle",translation:[l,c]})}draw(t){this._isVisible()&&(this.drawBackground(),this.drawGrid(t),this.drawBorder(),this.drawTitle(),this.drawLabels(t))}_layers(){const t=this.options,e=t.ticks&&t.ticks.z||0,i=Qi(t.grid&&t.grid.z,-1);return this._isVisible()&&this.draw===ea.prototype.draw?[{z:i,draw:t=>{this.drawBackground(),this.drawGrid(t),this.drawTitle()}},{z:i+1,draw:()=>{this.drawBorder()}},{z:e,draw:t=>{this.drawLabels(t)}}]:[{z:e,draw:t=>{this.draw(t)}}]}getMatchingVisibleMetas(t){const e=this.chart.getSortedVisibleDatasetMetas(),i=this.axis+"AxisID",n=[];let r,o;for(r=0,o=e.length;r<o;++r){const o=e[r];o[i]!==this.id||t&&o.type!==t||n.push(o)}return n}_resolveTickFontOptions(t){return fo(this.options.ticks.setContext(this.getContext(t)).font)}_maxDigits(){const t=this._resolveTickFontOptions(0).lineHeight;return(this.isHorizontal()?this.width:this.height)/t}}class ia{constructor(t,e,i){this.type=t,this.scope=e,this.override=i,this.items=Object.create(null)}isForType(t){return Object.prototype.isPrototypeOf.call(this.type.prototype,t.prototype)}register(t){const e=Object.getPrototypeOf(t);let i;(function(t){return"id"in t&&"defaults"in t})(e)&&(i=this.register(e));const n=this.items,r=t.id,o=this.scope+"."+r;if(!r)throw new Error("class does not have id: "+t);return r in n||(n[r]=t,function(t,e,i){const n=an(Object.create(null),[i?Hr.get(i):{},Hr.get(e),t.defaults]);Hr.set(e,n),t.defaultRoutes&&function(t,e){Object.keys(e).forEach((i=>{const n=i.split("."),r=n.pop(),o=[t].concat(n).join("."),s=e[i].split("."),a=s.pop(),l=s.join(".");Hr.route(o,r,l,a)}))}(e,t.defaultRoutes);t.descriptors&&Hr.describe(e,t.descriptors)}(t,o,i),this.override&&Hr.override(t.id,t.overrides)),o}get(t){return this.items[t]}unregister(t){const e=this.items,i=t.id,n=this.scope;i in e&&delete e[i],n&&i in Hr[n]&&(delete Hr[n][i],this.override&&delete Nr[i])}}var na=new class{constructor(){this.controllers=new ia(Ps,"datasets",!0),this.elements=new ia($s,"elements"),this.plugins=new ia(Object,"plugins"),this.scales=new ia(ea,"scales"),this._typedRegistries=[this.controllers,this.scales,this.elements]}add(...t){this._each("register",t)}remove(...t){this._each("unregister",t)}addControllers(...t){this._each("register",t,this.controllers)}addElements(...t){this._each("register",t,this.elements)}addPlugins(...t){this._each("register",t,this.plugins)}addScales(...t){this._each("register",t,this.scales)}getController(t){return this._get(t,this.controllers,"controller")}getElement(t){return this._get(t,this.elements,"element")}getPlugin(t){return this._get(t,this.plugins,"plugin")}getScale(t){return this._get(t,this.scales,"scale")}removeControllers(...t){this._each("unregister",t,this.controllers)}removeElements(...t){this._each("unregister",t,this.elements)}removePlugins(...t){this._each("unregister",t,this.plugins)}removeScales(...t){this._each("unregister",t,this.scales)}_each(t,e,i){[...e].forEach((e=>{const n=i||this._getRegistryForType(e);i||n.isForType(e)||n===this.plugins&&e.id?this._exec(t,n,e):en(e,(e=>{const n=i||this._getRegistryForType(e);this._exec(t,n,e)}))}))}_exec(t,e,i){const n=dn(t);tn(i["before"+n],[],i),e[t](i),tn(i["after"+n],[],i)}_getRegistryForType(t){for(let e=0;e<this._typedRegistries.length;e++){const i=this._typedRegistries[e];if(i.isForType(t))return i}return this.plugins}_get(t,e,i){const n=e.get(t);if(void 0===n)throw new Error('"'+t+'" is not a registered '+i+".");return n}};class ra extends Ps{update(t){const e=this._cachedMeta,{data:i=[]}=e,n=this.chart._animationsDisabled;let{start:r,count:o}=Kn(e,i,n);if(this._drawStart=r,this._drawCount=o,Jn(e)&&(r=0,o=i.length),this.options.showLine){const{dataset:r,_dataset:o}=e;r._chart=this.chart,r._datasetIndex=this.index,r._decimated=!!o._decimated,r.points=i;const s=this.resolveDatasetElementOptions(t);s.segment=this.options.segment,this.updateElement(r,void 0,{animated:!n,options:s},t)}this.updateElements(i,r,o,t)}addElements(){const{showLine:t}=this.options;!this.datasetElementType&&t&&(this.datasetElementType=na.getElement("line")),super.addElements()}updateElements(t,e,i,n){const r="reset"===n,{iScale:o,vScale:s,_stacked:a,_dataset:l}=this._cachedMeta,c=this.resolveDataElementOptions(e,n),h=this.getSharedOptions(c),u=this.includeOptions(n,h),d=o.axis,f=s.axis,{spanGaps:p,segment:g}=this.options,m=En(p)?p:Number.POSITIVE_INFINITY,b=this.chart._animationsDisabled||r||"none"===n;let v=e>0&&this.getParsed(e-1);for(let c=e;c<e+i;++c){const e=t[c],i=this.getParsed(c),p=b?e:{},y=Yi(i[f]),x=p[d]=o.getPixelForValue(i[d],c),_=p[f]=r||y?s.getBasePixel():s.getPixelForValue(a?this.applyStack(s,i,a):i[f],c);p.skip=isNaN(x)||isNaN(_)||y,p.stop=c>0&&Math.abs(i[d]-v[d])>m,g&&(p.parsed=i,p.raw=l.data[c]),u&&(p.options=h||this.resolveDataElementOptions(c,e.active?"active":n)),b||this.updateElement(e,c,p,n),v=i}this.updateSharedOptions(h,n,c)}getMaxOverflow(){const t=this._cachedMeta,e=t.data||[];if(!this.options.showLine){let t=0;for(let i=e.length-1;i>=0;--i)t=Math.max(t,e[i].size(this.resolveDataElementOptions(i))/2);return t>0&&t}const i=t.dataset,n=i.options&&i.options.borderWidth||0;if(!e.length)return n;const r=e[0].size(this.resolveDataElementOptions(0)),o=e[e.length-1].size(this.resolveDataElementOptions(e.length-1));return Math.max(n,r,o)/2}}ra.id="scatter",ra.defaults={datasetElementType:!1,dataElementType:"point",showLine:!1,fill:!1},ra.overrides={interaction:{mode:"point"},plugins:{tooltip:{callbacks:{title:()=>"",label:t=>"("+t.label+", "+t.formattedValue+")"}}},scales:{x:{type:"linear"},y:{type:"linear"}}};var oa=Object.freeze({__proto__:null,BarController:Fs,BubbleController:zs,DoughnutController:Ns,LineController:Bs,PolarAreaController:Ws,PieController:Vs,RadarController:Hs,ScatterController:ra});function sa(){throw new Error("This method is not implemented: Check that a complete date adapter is provided.")}class aa{constructor(t){this.options=t||{}}init(t){}formats(){return sa()}parse(t,e){return sa()}format(t,e){return sa()}add(t,e,i){return sa()}diff(t,e,i){return sa()}startOf(t,e,i){return sa()}endOf(t,e){return sa()}}aa.override=function(t){Object.assign(aa.prototype,t)};var la={_date:aa};function ca(t,e,i,n){const{controller:r,data:o,_sorted:s}=t,a=r._cachedMeta.iScale;if(a&&e===a.axis&&"r"!==e&&s&&o.length){const t=a._reversePixels?Vn:Wn;if(!n)return t(o,e,i);if(r._sharedOptions){const n=o[0],r="function"==typeof n.getRange&&n.getRange(e);if(r){const n=t(o,e,i-r),s=t(o,e,i+r);return{lo:n.lo,hi:s.hi}}}}return{lo:0,hi:o.length-1}}function ha(t,e,i,n,r){const o=t.getSortedVisibleDatasetMetas(),s=i[e];for(let t=0,i=o.length;t<i;++t){const{index:i,data:a}=o[t],{lo:l,hi:c}=ca(o[t],e,s,r);for(let t=l;t<=c;++t){const e=a[t];e.skip||n(e,i,t)}}}function ua(t,e,i,n,r){const o=[];if(!r&&!t.isPointInArea(e))return o;return ha(t,i,e,(function(i,s,a){(r||Kr(i,t.chartArea,0))&&i.inRange(e.x,e.y,n)&&o.push({element:i,datasetIndex:s,index:a})}),!0),o}function da(t,e,i,n,r,o){let s=[];const a=function(t){const e=-1!==t.indexOf("x"),i=-1!==t.indexOf("y");return function(t,n){const r=e?Math.abs(t.x-n.x):0,o=i?Math.abs(t.y-n.y):0;return Math.sqrt(Math.pow(r,2)+Math.pow(o,2))}}(i);let l=Number.POSITIVE_INFINITY;return ha(t,i,e,(function(i,c,h){const u=i.inRange(e.x,e.y,r);if(n&&!u)return;const d=i.getCenterPoint(r);if(!(!!o||t.isPointInArea(d))&&!u)return;const f=a(e,d);f<l?(s=[{element:i,datasetIndex:c,index:h}],l=f):f===l&&s.push({element:i,datasetIndex:c,index:h})})),s}function fa(t,e,i,n,r,o){return o||t.isPointInArea(e)?"r"!==i||n?da(t,e,i,n,r,o):function(t,e,i,n){let r=[];return ha(t,i,e,(function(t,i,o){const{startAngle:s,endAngle:a}=t.getProps(["startAngle","endAngle"],n),{angle:l}=Dn(t,{x:e.x,y:e.y});Fn(l,s,a)&&r.push({element:t,datasetIndex:i,index:o})})),r}(t,e,i,r):[]}function pa(t,e,i,n,r){const o=[],s="x"===i?"inXRange":"inYRange";let a=!1;return ha(t,i,e,((t,n,l)=>{t[s](e[i],r)&&(o.push({element:t,datasetIndex:n,index:l}),a=a||t.inRange(e.x,e.y,r))})),n&&!a?[]:o}var ga={evaluateInteractionItems:ha,modes:{index(t,e,i,n){const r=$o(e,t),o=i.axis||"x",s=i.includeInvisible||!1,a=i.intersect?ua(t,r,o,n,s):fa(t,r,o,!1,n,s),l=[];return a.length?(t.getSortedVisibleDatasetMetas().forEach((t=>{const e=a[0].index,i=t.data[e];i&&!i.skip&&l.push({element:i,datasetIndex:t.index,index:e})})),l):[]},dataset(t,e,i,n){const r=$o(e,t),o=i.axis||"xy",s=i.includeInvisible||!1;let a=i.intersect?ua(t,r,o,n,s):fa(t,r,o,!1,n,s);if(a.length>0){const e=a[0].datasetIndex,i=t.getDatasetMeta(e).data;a=[];for(let t=0;t<i.length;++t)a.push({element:i[t],datasetIndex:e,index:t})}return a},point:(t,e,i,n)=>ua(t,$o(e,t),i.axis||"xy",n,i.includeInvisible||!1),nearest(t,e,i,n){const r=$o(e,t),o=i.axis||"xy",s=i.includeInvisible||!1;return fa(t,r,o,i.intersect,n,s)},x:(t,e,i,n)=>pa(t,$o(e,t),"x",i.intersect,n),y:(t,e,i,n)=>pa(t,$o(e,t),"y",i.intersect,n)}};const ma=["left","top","right","bottom"];function ba(t,e){return t.filter((t=>t.pos===e))}function va(t,e){return t.filter((t=>-1===ma.indexOf(t.pos)&&t.box.axis===e))}function ya(t,e){return t.sort(((t,i)=>{const n=e?i:t,r=e?t:i;return n.weight===r.weight?n.index-r.index:n.weight-r.weight}))}function xa(t,e){const i=function(t){const e={};for(const i of t){const{stack:t,pos:n,stackWeight:r}=i;if(!t||!ma.includes(n))continue;const o=e[t]||(e[t]={count:0,placed:0,weight:0,size:0});o.count++,o.weight+=r}return e}(t),{vBoxMaxWidth:n,hBoxMaxHeight:r}=e;let o,s,a;for(o=0,s=t.length;o<s;++o){a=t[o];const{fullSize:s}=a.box,l=i[a.stack],c=l&&a.stackWeight/l.weight;a.horizontal?(a.width=c?c*n:s&&e.availableWidth,a.height=r):(a.width=n,a.height=c?c*r:s&&e.availableHeight)}return i}function _a(t,e,i,n){return Math.max(t[i],e[i])+Math.max(t[n],e[n])}function wa(t,e){t.top=Math.max(t.top,e.top),t.left=Math.max(t.left,e.left),t.bottom=Math.max(t.bottom,e.bottom),t.right=Math.max(t.right,e.right)}function ka(t,e,i,n){const{pos:r,box:o}=i,s=t.maxPadding;if(!Gi(r)){i.size&&(t[r]-=i.size);const e=n[i.stack]||{size:0,count:1};e.size=Math.max(e.size,i.horizontal?o.height:o.width),i.size=e.size/e.count,t[r]+=i.size}o.getPadding&&wa(s,o.getPadding());const a=Math.max(0,e.outerWidth-_a(s,t,"left","right")),l=Math.max(0,e.outerHeight-_a(s,t,"top","bottom")),c=a!==t.w,h=l!==t.h;return t.w=a,t.h=l,i.horizontal?{same:c,other:h}:{same:h,other:c}}function Oa(t,e){const i=e.maxPadding;function n(t){const n={left:0,top:0,right:0,bottom:0};return t.forEach((t=>{n[t]=Math.max(e[t],i[t])})),n}return n(t?["left","right"]:["top","bottom"])}function Sa(t,e,i,n){const r=[];let o,s,a,l,c,h;for(o=0,s=t.length,c=0;o<s;++o){a=t[o],l=a.box,l.update(a.width||e.w,a.height||e.h,Oa(a.horizontal,e));const{same:s,other:u}=ka(e,i,a,n);c|=s&&r.length,h=h||u,l.fullSize||r.push(a)}return c&&Sa(r,e,i,n)||h}function Ma(t,e,i,n,r){t.top=i,t.left=e,t.right=e+n,t.bottom=i+r,t.width=n,t.height=r}function Ea(t,e,i,n){const r=i.padding;let{x:o,y:s}=e;for(const a of t){const t=a.box,l=n[a.stack]||{count:1,placed:0,weight:1},c=a.stackWeight/l.weight||1;if(a.horizontal){const n=e.w*c,o=l.size||t.height;fn(l.start)&&(s=l.start),t.fullSize?Ma(t,r.left,s,i.outerWidth-r.right-r.left,o):Ma(t,e.left+l.placed,s,n,o),l.start=s,l.placed+=n,s=t.bottom}else{const n=e.h*c,s=l.size||t.width;fn(l.start)&&(o=l.start),t.fullSize?Ma(t,o,r.top,s,i.outerHeight-r.bottom-r.top):Ma(t,o,e.top+l.placed,s,n),l.start=o,l.placed+=n,o=t.right}}e.x=o,e.y=s}Hr.set("layout",{autoPadding:!0,padding:{top:0,right:0,bottom:0,left:0}});var Pa={addBox(t,e){t.boxes||(t.boxes=[]),e.fullSize=e.fullSize||!1,e.position=e.position||"top",e.weight=e.weight||0,e._layers=e._layers||function(){return[{z:0,draw(t){e.draw(t)}}]},t.boxes.push(e)},removeBox(t,e){const i=t.boxes?t.boxes.indexOf(e):-1;-1!==i&&t.boxes.splice(i,1)},configure(t,e,i){e.fullSize=i.fullSize,e.position=i.position,e.weight=i.weight},update(t,e,i,n){if(!t)return;const r=uo(t.options.layout.padding),o=Math.max(e-r.width,0),s=Math.max(i-r.height,0),a=function(t){const e=function(t){const e=[];let i,n,r,o,s,a;for(i=0,n=(t||[]).length;i<n;++i)r=t[i],({position:o,options:{stack:s,stackWeight:a=1}}=r),e.push({index:i,box:r,pos:o,horizontal:r.isHorizontal(),weight:r.weight,stack:s&&o+s,stackWeight:a});return e}(t),i=ya(e.filter((t=>t.box.fullSize)),!0),n=ya(ba(e,"left"),!0),r=ya(ba(e,"right")),o=ya(ba(e,"top"),!0),s=ya(ba(e,"bottom")),a=va(e,"x"),l=va(e,"y");return{fullSize:i,leftAndTop:n.concat(o),rightAndBottom:r.concat(l).concat(s).concat(a),chartArea:ba(e,"chartArea"),vertical:n.concat(r).concat(l),horizontal:o.concat(s).concat(a)}}(t.boxes),l=a.vertical,c=a.horizontal;en(t.boxes,(t=>{"function"==typeof t.beforeLayout&&t.beforeLayout()}));const h=l.reduce(((t,e)=>e.box.options&&!1===e.box.options.display?t:t+1),0)||1,u=Object.freeze({outerWidth:e,outerHeight:i,padding:r,availableWidth:o,availableHeight:s,vBoxMaxWidth:o/2/h,hBoxMaxHeight:s/2}),d=Object.assign({},r);wa(d,uo(n));const f=Object.assign({maxPadding:d,w:o,h:s,x:r.left,y:r.top},r),p=xa(l.concat(c),u);Sa(a.fullSize,f,u,p),Sa(l,f,u,p),Sa(c,f,u,p)&&Sa(l,f,u,p),function(t){const e=t.maxPadding;function i(i){const n=Math.max(e[i]-t[i],0);return t[i]+=n,n}t.y+=i("top"),t.x+=i("left"),i("right"),i("bottom")}(f),Ea(a.leftAndTop,f,u,p),f.x+=f.w,f.y+=f.h,Ea(a.rightAndBottom,f,u,p),t.chartArea={left:f.left,top:f.top,right:f.left+f.w,bottom:f.top+f.h,height:f.h,width:f.w},en(a.chartArea,(e=>{const i=e.box;Object.assign(i,t.chartArea),i.update(f.w,f.h,{left:0,top:0,right:0,bottom:0})}))}};class Ta{acquireContext(t,e){}releaseContext(t){return!1}addEventListener(t,e,i){}removeEventListener(t,e,i){}getDevicePixelRatio(){return 1}getMaximumSize(t,e,i,n){return e=Math.max(0,e||t.width),i=i||t.height,{width:e,height:Math.max(0,n?Math.floor(e/n):i)}}isAttached(t){return!0}updateConfig(t){}}class La extends Ta{acquireContext(t){return t&&t.getContext&&t.getContext("2d")||null}updateConfig(t){t.options.animation=!1}}const Aa="$chartjs",Ca={touchstart:"mousedown",touchmove:"mousemove",touchend:"mouseup",pointerenter:"mouseenter",pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointerleave:"mouseout",pointerout:"mouseout"},Da=t=>null===t||""===t;const ja=!!Xo&&{passive:!0};function Ia(t,e,i){t.canvas.removeEventListener(e,i,ja)}function Ra(t,e){for(const i of t)if(i===e||i.contains(e))return!0}function Fa(t,e,i){const n=t.canvas,r=new MutationObserver((t=>{let e=!1;for(const i of t)e=e||Ra(i.addedNodes,n),e=e&&!Ra(i.removedNodes,n);e&&i()}));return r.observe(document,{childList:!0,subtree:!0}),r}function za(t,e,i){const n=t.canvas,r=new MutationObserver((t=>{let e=!1;for(const i of t)e=e||Ra(i.removedNodes,n),e=e&&!Ra(i.addedNodes,n);e&&i()}));return r.observe(document,{childList:!0,subtree:!0}),r}const Na=new Map;let Ba=0;function Wa(){const t=window.devicePixelRatio;t!==Ba&&(Ba=t,Na.forEach(((e,i)=>{i.currentDevicePixelRatio!==t&&e()})))}function Va(t,e,i){const n=t.canvas,r=n&&zo(n);if(!r)return;const o=Yn(((t,e)=>{const n=r.clientWidth;i(t,e),n<r.clientWidth&&i()}),window),s=new ResizeObserver((t=>{const e=t[0],i=e.contentRect.width,n=e.contentRect.height;0===i&&0===n||o(i,n)}));return s.observe(r),function(t,e){Na.size||window.addEventListener("resize",Wa),Na.set(t,e)}(t,o),s}function Ha(t,e,i){i&&i.disconnect(),"resize"===e&&function(t){Na.delete(t),Na.size||window.removeEventListener("resize",Wa)}(t)}function $a(t,e,i){const n=t.canvas,r=Yn((e=>{null!==t.ctx&&i(function(t,e){const i=Ca[t.type]||t.type,{x:n,y:r}=$o(t,e);return{type:i,chart:e,native:t,x:void 0!==n?n:null,y:void 0!==r?r:null}}(e,t))}),t,(t=>{const e=t[0];return[e,e.offsetX,e.offsetY]}));return function(t,e,i){t.addEventListener(e,i,ja)}(n,e,r),r}class Ua extends Ta{acquireContext(t,e){const i=t&&t.getContext&&t.getContext("2d");return i&&i.canvas===t?(function(t,e){const i=t.style,n=t.getAttribute("height"),r=t.getAttribute("width");if(t[Aa]={initial:{height:n,width:r,style:{display:i.display,height:i.height,width:i.width}}},i.display=i.display||"block",i.boxSizing=i.boxSizing||"border-box",Da(r)){const e=Go(t,"width");void 0!==e&&(t.width=e)}if(Da(n))if(""===t.style.height)t.height=t.width/(e||2);else{const e=Go(t,"height");void 0!==e&&(t.height=e)}}(t,e),i):null}releaseContext(t){const e=t.canvas;if(!e[Aa])return!1;const i=e[Aa].initial;["height","width"].forEach((t=>{const n=i[t];Yi(n)?e.removeAttribute(t):e.setAttribute(t,n)}));const n=i.style||{};return Object.keys(n).forEach((t=>{e.style[t]=n[t]})),e.width=e.width,delete e[Aa],!0}addEventListener(t,e,i){this.removeEventListener(t,e);const n=t.$proxies||(t.$proxies={}),r={attach:Fa,detach:za,resize:Va}[e]||$a;n[e]=r(t,e,i)}removeEventListener(t,e){const i=t.$proxies||(t.$proxies={}),n=i[e];if(!n)return;({attach:Ha,detach:Ha,resize:Ha}[e]||Ia)(t,e,n),i[e]=void 0}getDevicePixelRatio(){return window.devicePixelRatio}getMaximumSize(t,e,i,n){return qo(t,e,i,n)}isAttached(t){const e=zo(t);return!(!e||!e.isConnected)}}class qa{constructor(){this._init=[]}notify(t,e,i,n){"beforeInit"===e&&(this._init=this._createDescriptors(t,!0),this._notify(this._init,t,"install"));const r=n?this._descriptors(t).filter(n):this._descriptors(t),o=this._notify(r,t,e,i);return"afterDestroy"===e&&(this._notify(r,t,"stop"),this._notify(this._init,t,"uninstall")),o}_notify(t,e,i,n){n=n||{};for(const r of t){const t=r.plugin;if(!1===tn(t[i],[e,n,r.options],t)&&n.cancelable)return!1}return!0}invalidate(){Yi(this._cache)||(this._oldCache=this._cache,this._cache=void 0)}_descriptors(t){if(this._cache)return this._cache;const e=this._cache=this._createDescriptors(t);return this._notifyStateChanges(t),e}_createDescriptors(t,e){const i=t&&t.config,n=Qi(i.options&&i.options.plugins,{}),r=function(t){const e={},i=[],n=Object.keys(na.plugins.items);for(let t=0;t<n.length;t++)i.push(na.getPlugin(n[t]));const r=t.plugins||[];for(let t=0;t<r.length;t++){const n=r[t];-1===i.indexOf(n)&&(i.push(n),e[n.id]=!0)}return{plugins:i,localIds:e}}(i);return!1!==n||e?function(t,{plugins:e,localIds:i},n,r){const o=[],s=t.getContext();for(const a of e){const e=a.id,l=Ya(n[e],r);null!==l&&o.push({plugin:a,options:Xa(t.config,{plugin:a,local:i[e]},l,s)})}return o}(t,r,n,e):[]}_notifyStateChanges(t){const e=this._oldCache||[],i=this._cache,n=(t,e)=>t.filter((t=>!e.some((e=>t.plugin.id===e.plugin.id))));this._notify(n(e,i),t,"stop"),this._notify(n(i,e),t,"start")}}function Ya(t,e){return e||!1!==t?!0===t?{}:t:null}function Xa(t,{plugin:e,local:i},n,r){const o=t.pluginScopeKeys(e),s=t.getOptionScopes(n,o);return i&&e.defaults&&s.push(e.defaults),t.createResolver(s,r,[""],{scriptable:!1,indexable:!1,allKeys:!0})}function Ga(t,e){const i=Hr.datasets[t]||{};return((e.datasets||{})[t]||{}).indexAxis||e.indexAxis||i.indexAxis||"x"}function Ka(t,e){return"x"===t||"y"===t?t:e.axis||("top"===(i=e.position)||"bottom"===i?"x":"left"===i||"right"===i?"y":void 0)||t.charAt(0).toLowerCase();var i}function Ja(t){const e=t.options||(t.options={});e.plugins=Qi(e.plugins,{}),e.scales=function(t,e){const i=Nr[t.type]||{scales:{}},n=e.scales||{},r=Ga(t.type,e),o=Object.create(null),s=Object.create(null);return Object.keys(n).forEach((t=>{const e=n[t];if(!Gi(e))return console.error(`Invalid scale configuration for scale: ${t}`);if(e._proxy)return console.warn(`Ignoring resolver passed as options for scale: ${t}`);const a=Ka(t,e),l=function(t,e){return t===e?"_index_":"_value_"}(a,r),c=i.scales||{};o[a]=o[a]||t,s[t]=ln(Object.create(null),[{axis:a},e,c[a],c[l]])})),t.data.datasets.forEach((i=>{const r=i.type||t.type,a=i.indexAxis||Ga(r,e),l=(Nr[r]||{}).scales||{};Object.keys(l).forEach((t=>{const e=function(t,e){let i=t;return"_index_"===t?i=e:"_value_"===t&&(i="x"===e?"y":"x"),i}(t,a),r=i[e+"AxisID"]||o[e]||e;s[r]=s[r]||Object.create(null),ln(s[r],[{axis:e},n[r],l[t]])}))})),Object.keys(s).forEach((t=>{const e=s[t];ln(e,[Hr.scales[e.type],Hr.scale])})),s}(t,e)}function Qa(t){return(t=t||{}).datasets=t.datasets||[],t.labels=t.labels||[],t}const Za=new Map,tl=new Set;function el(t,e){let i=Za.get(t);return i||(i=e(),Za.set(t,i),tl.add(i)),i}const il=(t,e,i)=>{const n=un(e,i);void 0!==n&&t.add(n)};class nl{constructor(t){this._config=function(t){return(t=t||{}).data=Qa(t.data),Ja(t),t}(t),this._scopeCache=new Map,this._resolverCache=new Map}get platform(){return this._config.platform}get type(){return this._config.type}set type(t){this._config.type=t}get data(){return this._config.data}set data(t){this._config.data=Qa(t)}get options(){return this._config.options}set options(t){this._config.options=t}get plugins(){return this._config.plugins}update(){const t=this._config;this.clearCache(),Ja(t)}clearCache(){this._scopeCache.clear(),this._resolverCache.clear()}datasetScopeKeys(t){return el(t,(()=>[[`datasets.${t}`,""]]))}datasetAnimationScopeKeys(t,e){return el(`${t}.transition.${e}`,(()=>[[`datasets.${t}.transitions.${e}`,`transitions.${e}`],[`datasets.${t}`,""]]))}datasetElementScopeKeys(t,e){return el(`${t}-${e}`,(()=>[[`datasets.${t}.elements.${e}`,`datasets.${t}`,`elements.${e}`,""]]))}pluginScopeKeys(t){const e=t.id;return el(`${this.type}-plugin-${e}`,(()=>[[`plugins.${e}`,...t.additionalOptionScopes||[]]]))}_cachedScopes(t,e){const i=this._scopeCache;let n=i.get(t);return n&&!e||(n=new Map,i.set(t,n)),n}getOptionScopes(t,e,i){const{options:n,type:r}=this,o=this._cachedScopes(t,i),s=o.get(e);if(s)return s;const a=new Set;e.forEach((e=>{t&&(a.add(t),e.forEach((e=>il(a,t,e)))),e.forEach((t=>il(a,n,t))),e.forEach((t=>il(a,Nr[r]||{},t))),e.forEach((t=>il(a,Hr,t))),e.forEach((t=>il(a,Br,t)))}));const l=Array.from(a);return 0===l.length&&l.push(Object.create(null)),tl.has(e)&&o.set(e,l),l}chartOptionScopes(){const{options:t,type:e}=this;return[t,Nr[e]||{},Hr.datasets[e]||{},{type:e},Hr,Br]}resolveNamedOptions(t,e,i,n=[""]){const r={$shared:!0},{resolver:o,subPrefixes:s}=rl(this._resolverCache,t,n);let a=o;if(function(t,e){const{isScriptable:i,isIndexable:n}=vo(t);for(const r of e){const e=i(r),o=n(r),s=(o||e)&&t[r];if(e&&(pn(s)||ol(s))||o&&Xi(s))return!0}return!1}(o,e)){r.$shared=!1;a=bo(o,i=pn(i)?i():i,this.createResolver(t,i,s))}for(const t of e)r[t]=a[t];return r}createResolver(t,e,i=[""],n){const{resolver:r}=rl(this._resolverCache,t,i);return Gi(e)?bo(r,e,void 0,n):r}}function rl(t,e,i){let n=t.get(e);n||(n=new Map,t.set(e,n));const r=i.join();let o=n.get(r);if(!o){o={resolver:mo(e,i),subPrefixes:i.filter((t=>!t.toLowerCase().includes("hover")))},n.set(r,o)}return o}const ol=t=>Gi(t)&&Object.getOwnPropertyNames(t).reduce(((e,i)=>e||pn(t[i])),!1);const sl=["top","bottom","left","right","chartArea"];function al(t,e){return"top"===t||"bottom"===t||-1===sl.indexOf(t)&&"x"===e}function ll(t,e){return function(i,n){return i[t]===n[t]?i[e]-n[e]:i[t]-n[t]}}function cl(t){const e=t.chart,i=e.options.animation;e.notifyPlugins("afterRender"),tn(i&&i.onComplete,[t],e)}function hl(t){const e=t.chart,i=e.options.animation;tn(i&&i.onProgress,[t],e)}function ul(t){return Fo()&&"string"==typeof t?t=document.getElementById(t):t&&t.length&&(t=t[0]),t&&t.canvas&&(t=t.canvas),t}const dl={},fl=t=>{const e=ul(t);return Object.values(dl).filter((t=>t.canvas===e)).pop()};function pl(t,e,i){const n=Object.keys(t);for(const r of n){const n=+r;if(n>=e){const o=t[r];delete t[r],(i>0||n>e)&&(t[n+i]=o)}}}class gl{constructor(t,e){const i=this.config=new nl(e),n=ul(t),r=fl(n);if(r)throw new Error("Canvas is already in use. Chart with ID '"+r.id+"' must be destroyed before the canvas with ID '"+r.canvas.id+"' can be reused.");const o=i.createResolver(i.chartOptionScopes(),this.getContext());this.platform=new(i.platform||function(t){return!Fo()||"undefined"!=typeof OffscreenCanvas&&t instanceof OffscreenCanvas?La:Ua}(n)),this.platform.updateConfig(i);const s=this.platform.acquireContext(n,o.aspectRatio),a=s&&s.canvas,l=a&&a.height,c=a&&a.width;this.id=qi(),this.ctx=s,this.canvas=a,this.width=c,this.height=l,this._options=o,this._aspectRatio=this.aspectRatio,this._layers=[],this._metasets=[],this._stacks=void 0,this.boxes=[],this.currentDevicePixelRatio=void 0,this.chartArea=void 0,this._active=[],this._lastEvent=void 0,this._listeners={},this._responsiveListeners=void 0,this._sortedMetasets=[],this.scales={},this._plugins=new qa,this.$proxies={},this._hiddenIndices={},this.attached=!1,this._animationsDisabled=void 0,this.$context=void 0,this._doResize=function(t,e){let i;return function(...n){return e?(clearTimeout(i),i=setTimeout(t,e,n)):t.apply(this,n),e}}((t=>this.update(t)),o.resizeDelay||0),this._dataChanges=[],dl[this.id]=this,s&&a?(us.listen(this,"complete",cl),us.listen(this,"progress",hl),this._initialize(),this.attached&&this.update()):console.error("Failed to create chart: can't acquire context from the given item")}get aspectRatio(){const{options:{aspectRatio:t,maintainAspectRatio:e},width:i,height:n,_aspectRatio:r}=this;return Yi(t)?e&&r?r:n?i/n:null:t}get data(){return this.config.data}set data(t){this.config.data=t}get options(){return this._options}set options(t){this.config.options=t}_initialize(){return this.notifyPlugins("beforeInit"),this.options.responsive?this.resize():Yo(this,this.options.devicePixelRatio),this.bindEvents(),this.notifyPlugins("afterInit"),this}clear(){return Yr(this.canvas,this.ctx),this}stop(){return us.stop(this),this}resize(t,e){us.running(this)?this._resizeBeforeDraw={width:t,height:e}:this._resize(t,e)}_resize(t,e){const i=this.options,n=this.canvas,r=i.maintainAspectRatio&&this.aspectRatio,o=this.platform.getMaximumSize(n,t,e,r),s=i.devicePixelRatio||this.platform.getDevicePixelRatio(),a=this.width?"resize":"attach";this.width=o.width,this.height=o.height,this._aspectRatio=this.aspectRatio,Yo(this,s,!0)&&(this.notifyPlugins("resize",{size:o}),tn(i.onResize,[this,o],this),this.attached&&this._doResize(a)&&this.render())}ensureScalesHaveIDs(){en(this.options.scales||{},((t,e)=>{t.id=e}))}buildOrUpdateScales(){const t=this.options,e=t.scales,i=this.scales,n=Object.keys(i).reduce(((t,e)=>(t[e]=!1,t)),{});let r=[];e&&(r=r.concat(Object.keys(e).map((t=>{const i=e[t],n=Ka(t,i),r="r"===n,o="x"===n;return{options:i,dposition:r?"chartArea":o?"bottom":"left",dtype:r?"radialLinear":o?"category":"linear"}})))),en(r,(e=>{const r=e.options,o=r.id,s=Ka(o,r),a=Qi(r.type,e.dtype);void 0!==r.position&&al(r.position,s)===al(e.dposition)||(r.position=e.dposition),n[o]=!0;let l=null;if(o in i&&i[o].type===a)l=i[o];else{l=new(na.getScale(a))({id:o,type:a,ctx:this.ctx,chart:this}),i[l.id]=l}l.init(r,t)})),en(n,((t,e)=>{t||delete i[e]})),en(i,(t=>{Pa.configure(this,t,t.options),Pa.addBox(this,t)}))}_updateMetasets(){const t=this._metasets,e=this.data.datasets.length,i=t.length;if(t.sort(((t,e)=>t.index-e.index)),i>e){for(let t=e;t<i;++t)this._destroyDatasetMeta(t);t.splice(e,i-e)}this._sortedMetasets=t.slice(0).sort(ll("order","index"))}_removeUnreferencedMetasets(){const{_metasets:t,data:{datasets:e}}=this;t.length>e.length&&delete this._stacks,t.forEach(((t,i)=>{0===e.filter((e=>e===t._dataset)).length&&this._destroyDatasetMeta(i)}))}buildOrUpdateControllers(){const t=[],e=this.data.datasets;let i,n;for(this._removeUnreferencedMetasets(),i=0,n=e.length;i<n;i++){const n=e[i];let r=this.getDatasetMeta(i);const o=n.type||this.config.type;if(r.type&&r.type!==o&&(this._destroyDatasetMeta(i),r=this.getDatasetMeta(i)),r.type=o,r.indexAxis=n.indexAxis||Ga(o,this.options),r.order=n.order||0,r.index=i,r.label=""+n.label,r.visible=this.isDatasetVisible(i),r.controller)r.controller.updateIndex(i),r.controller.linkScales();else{const e=na.getController(o),{datasetElementType:n,dataElementType:s}=Hr.datasets[o];Object.assign(e.prototype,{dataElementType:na.getElement(s),datasetElementType:n&&na.getElement(n)}),r.controller=new e(this,i),t.push(r.controller)}}return this._updateMetasets(),t}_resetElements(){en(this.data.datasets,((t,e)=>{this.getDatasetMeta(e).controller.reset()}),this)}reset(){this._resetElements(),this.notifyPlugins("reset")}update(t){const e=this.config;e.update();const i=this._options=e.createResolver(e.chartOptionScopes(),this.getContext()),n=this._animationsDisabled=!i.animation;if(this._updateScales(),this._checkEventBindings(),this._updateHiddenIndices(),this._plugins.invalidate(),!1===this.notifyPlugins("beforeUpdate",{mode:t,cancelable:!0}))return;const r=this.buildOrUpdateControllers();this.notifyPlugins("beforeElementsUpdate");let o=0;for(let t=0,e=this.data.datasets.length;t<e;t++){const{controller:e}=this.getDatasetMeta(t),i=!n&&-1===r.indexOf(e);e.buildOrUpdateElements(i),o=Math.max(+e.getMaxOverflow(),o)}o=this._minPadding=i.layout.autoPadding?o:0,this._updateLayout(o),n||en(r,(t=>{t.reset()})),this._updateDatasets(t),this.notifyPlugins("afterUpdate",{mode:t}),this._layers.sort(ll("z","_idx"));const{_active:s,_lastEvent:a}=this;a?this._eventHandler(a,!0):s.length&&this._updateHoverStyles(s,s,!0),this.render()}_updateScales(){en(this.scales,(t=>{Pa.removeBox(this,t)})),this.ensureScalesHaveIDs(),this.buildOrUpdateScales()}_checkEventBindings(){const t=this.options,e=new Set(Object.keys(this._listeners)),i=new Set(t.events);gn(e,i)&&!!this._responsiveListeners===t.responsive||(this.unbindEvents(),this.bindEvents())}_updateHiddenIndices(){const{_hiddenIndices:t}=this,e=this._getUniformDataChanges()||[];for(const{method:i,start:n,count:r}of e){pl(t,n,"_removeElements"===i?-r:r)}}_getUniformDataChanges(){const t=this._dataChanges;if(!t||!t.length)return;this._dataChanges=[];const e=this.data.datasets.length,i=e=>new Set(t.filter((t=>t[0]===e)).map(((t,e)=>e+","+t.splice(1).join(",")))),n=i(0);for(let t=1;t<e;t++)if(!gn(n,i(t)))return;return Array.from(n).map((t=>t.split(","))).map((t=>({method:t[1],start:+t[2],count:+t[3]})))}_updateLayout(t){if(!1===this.notifyPlugins("beforeLayout",{cancelable:!0}))return;Pa.update(this,this.width,this.height,t);const e=this.chartArea,i=e.width<=0||e.height<=0;this._layers=[],en(this.boxes,(t=>{i&&"chartArea"===t.position||(t.configure&&t.configure(),this._layers.push(...t._layers()))}),this),this._layers.forEach(((t,e)=>{t._idx=e})),this.notifyPlugins("afterLayout")}_updateDatasets(t){if(!1!==this.notifyPlugins("beforeDatasetsUpdate",{mode:t,cancelable:!0})){for(let t=0,e=this.data.datasets.length;t<e;++t)this.getDatasetMeta(t).controller.configure();for(let e=0,i=this.data.datasets.length;e<i;++e)this._updateDataset(e,pn(t)?t({datasetIndex:e}):t);this.notifyPlugins("afterDatasetsUpdate",{mode:t})}}_updateDataset(t,e){const i=this.getDatasetMeta(t),n={meta:i,index:t,mode:e,cancelable:!0};!1!==this.notifyPlugins("beforeDatasetUpdate",n)&&(i.controller._update(e),n.cancelable=!1,this.notifyPlugins("afterDatasetUpdate",n))}render(){!1!==this.notifyPlugins("beforeRender",{cancelable:!0})&&(us.has(this)?this.attached&&!us.running(this)&&us.start(this):(this.draw(),cl({chart:this})))}draw(){let t;if(this._resizeBeforeDraw){const{width:t,height:e}=this._resizeBeforeDraw;this._resize(t,e),this._resizeBeforeDraw=null}if(this.clear(),this.width<=0||this.height<=0)return;if(!1===this.notifyPlugins("beforeDraw",{cancelable:!0}))return;const e=this._layers;for(t=0;t<e.length&&e[t].z<=0;++t)e[t].draw(this.chartArea);for(this._drawDatasets();t<e.length;++t)e[t].draw(this.chartArea);this.notifyPlugins("afterDraw")}_getSortedDatasetMetas(t){const e=this._sortedMetasets,i=[];let n,r;for(n=0,r=e.length;n<r;++n){const r=e[n];t&&!r.visible||i.push(r)}return i}getSortedVisibleDatasetMetas(){return this._getSortedDatasetMetas(!0)}_drawDatasets(){if(!1===this.notifyPlugins("beforeDatasetsDraw",{cancelable:!0}))return;const t=this.getSortedVisibleDatasetMetas();for(let e=t.length-1;e>=0;--e)this._drawDataset(t[e]);this.notifyPlugins("afterDatasetsDraw")}_drawDataset(t){const e=this.ctx,i=t._clip,n=!i.disabled,r=this.chartArea,o={meta:t,index:t.index,cancelable:!0};!1!==this.notifyPlugins("beforeDatasetDraw",o)&&(n&&Jr(e,{left:!1===i.left?0:r.left-i.left,right:!1===i.right?this.width:r.right+i.right,top:!1===i.top?0:r.top-i.top,bottom:!1===i.bottom?this.height:r.bottom+i.bottom}),t.controller.draw(),n&&Qr(e),o.cancelable=!1,this.notifyPlugins("afterDatasetDraw",o))}isPointInArea(t){return Kr(t,this.chartArea,this._minPadding)}getElementsAtEventForMode(t,e,i,n){const r=ga.modes[e];return"function"==typeof r?r(this,t,i,n):[]}getDatasetMeta(t){const e=this.data.datasets[t],i=this._metasets;let n=i.filter((t=>t&&t._dataset===e)).pop();return n||(n={type:null,data:[],dataset:null,controller:null,hidden:null,xAxisID:null,yAxisID:null,order:e&&e.order||0,index:t,_dataset:e,_parsed:[],_sorted:!1},i.push(n)),n}getContext(){return this.$context||(this.$context=go(null,{chart:this,type:"chart"}))}getVisibleDatasetCount(){return this.getSortedVisibleDatasetMetas().length}isDatasetVisible(t){const e=this.data.datasets[t];if(!e)return!1;const i=this.getDatasetMeta(t);return"boolean"==typeof i.hidden?!i.hidden:!e.hidden}setDatasetVisibility(t,e){this.getDatasetMeta(t).hidden=!e}toggleDataVisibility(t){this._hiddenIndices[t]=!this._hiddenIndices[t]}getDataVisibility(t){return!this._hiddenIndices[t]}_updateVisibility(t,e,i){const n=i?"show":"hide",r=this.getDatasetMeta(t),o=r.controller._resolveAnimations(void 0,n);fn(e)?(r.data[e].hidden=!i,this.update()):(this.setDatasetVisibility(t,i),o.update(r,{visible:i}),this.update((e=>e.datasetIndex===t?n:void 0)))}hide(t,e){this._updateVisibility(t,e,!1)}show(t,e){this._updateVisibility(t,e,!0)}_destroyDatasetMeta(t){const e=this._metasets[t];e&&e.controller&&e.controller._destroy(),delete this._metasets[t]}_stop(){let t,e;for(this.stop(),us.remove(this),t=0,e=this.data.datasets.length;t<e;++t)this._destroyDatasetMeta(t)}destroy(){this.notifyPlugins("beforeDestroy");const{canvas:t,ctx:e}=this;this._stop(),this.config.clearCache(),t&&(this.unbindEvents(),Yr(t,e),this.platform.releaseContext(e),this.canvas=null,this.ctx=null),this.notifyPlugins("destroy"),delete dl[this.id],this.notifyPlugins("afterDestroy")}toBase64Image(...t){return this.canvas.toDataURL(...t)}bindEvents(){this.bindUserEvents(),this.options.responsive?this.bindResponsiveEvents():this.attached=!0}bindUserEvents(){const t=this._listeners,e=this.platform,i=(i,n)=>{e.addEventListener(this,i,n),t[i]=n},n=(t,e,i)=>{t.offsetX=e,t.offsetY=i,this._eventHandler(t)};en(this.options.events,(t=>i(t,n)))}bindResponsiveEvents(){this._responsiveListeners||(this._responsiveListeners={});const t=this._responsiveListeners,e=this.platform,i=(i,n)=>{e.addEventListener(this,i,n),t[i]=n},n=(i,n)=>{t[i]&&(e.removeEventListener(this,i,n),delete t[i])},r=(t,e)=>{this.canvas&&this.resize(t,e)};let o;const s=()=>{n("attach",s),this.attached=!0,this.resize(),i("resize",r),i("detach",o)};o=()=>{this.attached=!1,n("resize",r),this._stop(),this._resize(0,0),i("attach",s)},e.isAttached(this.canvas)?s():o()}unbindEvents(){en(this._listeners,((t,e)=>{this.platform.removeEventListener(this,e,t)})),this._listeners={},en(this._responsiveListeners,((t,e)=>{this.platform.removeEventListener(this,e,t)})),this._responsiveListeners=void 0}updateHoverStyle(t,e,i){const n=i?"set":"remove";let r,o,s,a;for("dataset"===e&&(r=this.getDatasetMeta(t[0].datasetIndex),r.controller["_"+n+"DatasetHoverStyle"]()),s=0,a=t.length;s<a;++s){o=t[s];const e=o&&this.getDatasetMeta(o.datasetIndex).controller;e&&e[n+"HoverStyle"](o.element,o.datasetIndex,o.index)}}getActiveElements(){return this._active||[]}setActiveElements(t){const e=this._active||[],i=t.map((({datasetIndex:t,index:e})=>{const i=this.getDatasetMeta(t);if(!i)throw new Error("No dataset found at index "+t);return{datasetIndex:t,element:i.data[e],index:e}}));!nn(i,e)&&(this._active=i,this._lastEvent=null,this._updateHoverStyles(i,e))}notifyPlugins(t,e,i){return this._plugins.notify(this,t,e,i)}_updateHoverStyles(t,e,i){const n=this.options.hover,r=(t,e)=>t.filter((t=>!e.some((e=>t.datasetIndex===e.datasetIndex&&t.index===e.index)))),o=r(e,t),s=i?t:r(t,e);o.length&&this.updateHoverStyle(o,n.mode,!1),s.length&&n.mode&&this.updateHoverStyle(s,n.mode,!0)}_eventHandler(t,e){const i={event:t,replay:e,cancelable:!0,inChartArea:this.isPointInArea(t)},n=e=>(e.options.events||this.options.events).includes(t.native.type);if(!1===this.notifyPlugins("beforeEvent",i,n))return;const r=this._handleEvent(t,e,i.inChartArea);return i.cancelable=!1,this.notifyPlugins("afterEvent",i,n),(r||i.changed)&&this.render(),this}_handleEvent(t,e,i){const{_active:n=[],options:r}=this,o=e,s=this._getActiveElements(t,n,i,o),a=function(t){return"mouseup"===t.type||"click"===t.type||"contextmenu"===t.type}(t),l=function(t,e,i,n){return i&&"mouseout"!==t.type?n?e:t:null}(t,this._lastEvent,i,a);i&&(this._lastEvent=null,tn(r.onHover,[t,s,this],this),a&&tn(r.onClick,[t,s,this],this));const c=!nn(s,n);return(c||e)&&(this._active=s,this._updateHoverStyles(s,n,e)),this._lastEvent=l,c}_getActiveElements(t,e,i,n){if("mouseout"===t.type)return[];if(!i)return e;const r=this.options.hover;return this.getElementsAtEventForMode(t,r.mode,r,n)}}const ml=()=>en(gl.instances,(t=>t._plugins.invalidate())),bl=!0;function vl(t,e,i){const{startAngle:n,pixelMargin:r,x:o,y:s,outerRadius:a,innerRadius:l}=e;let c=r/a;t.beginPath(),t.arc(o,s,a,n-c,i+c),l>r?(c=r/l,t.arc(o,s,l,i+c,n-c,!0)):t.arc(o,s,r,i+_n,n-_n),t.closePath(),t.clip()}function yl(t,e,i,n){const r=lo(t.options.borderRadius,["outerStart","outerEnd","innerStart","innerEnd"]);const o=(i-e)/2,s=Math.min(o,n*e/2),a=t=>{const e=(i-Math.min(o,t))*n/2;return zn(t,0,Math.min(o,e))};return{outerStart:a(r.outerStart),outerEnd:a(r.outerEnd),innerStart:zn(r.innerStart,0,s),innerEnd:zn(r.innerEnd,0,s)}}function xl(t,e,i,n){return{x:i+t*Math.cos(e),y:n+t*Math.sin(e)}}function _l(t,e,i,n,r,o){const{x:s,y:a,startAngle:l,pixelMargin:c,innerRadius:h}=e,u=Math.max(e.outerRadius+n+i-c,0),d=h>0?h+n+i+c:0;let f=0;const p=r-l;if(n){const t=((h>0?h-n:0)+(u>0?u-n:0))/2;f=(p-(0!==t?p*t/(t+n):p))/2}const g=(p-Math.max(.001,p*u-i/mn)/u)/2,m=l+g+f,b=r-g-f,{outerStart:v,outerEnd:y,innerStart:x,innerEnd:_}=yl(e,d,u,b-m),w=u-v,k=u-y,O=m+v/w,S=b-y/k,M=d+x,E=d+_,P=m+x/M,T=b-_/E;if(t.beginPath(),o){if(t.arc(s,a,u,O,S),y>0){const e=xl(k,S,s,a);t.arc(e.x,e.y,y,S,b+_n)}const e=xl(E,b,s,a);if(t.lineTo(e.x,e.y),_>0){const e=xl(E,T,s,a);t.arc(e.x,e.y,_,b+_n,T+Math.PI)}if(t.arc(s,a,d,b-_/d,m+x/d,!0),x>0){const e=xl(M,P,s,a);t.arc(e.x,e.y,x,P+Math.PI,m-_n)}const i=xl(w,m,s,a);if(t.lineTo(i.x,i.y),v>0){const e=xl(w,O,s,a);t.arc(e.x,e.y,v,m-_n,O)}}else{t.moveTo(s,a);const e=Math.cos(O)*u+s,i=Math.sin(O)*u+a;t.lineTo(e,i);const n=Math.cos(S)*u+s,r=Math.sin(S)*u+a;t.lineTo(n,r)}t.closePath()}function wl(t,e,i,n,r,o){const{options:s}=e,{borderWidth:a,borderJoinStyle:l}=s,c="inner"===s.borderAlign;a&&(c?(t.lineWidth=2*a,t.lineJoin=l||"round"):(t.lineWidth=a,t.lineJoin=l||"bevel"),e.fullCircles&&function(t,e,i){const{x:n,y:r,startAngle:o,pixelMargin:s,fullCircles:a}=e,l=Math.max(e.outerRadius-s,0),c=e.innerRadius+s;let h;for(i&&vl(t,e,o+bn),t.beginPath(),t.arc(n,r,c,o+bn,o,!0),h=0;h<a;++h)t.stroke();for(t.beginPath(),t.arc(n,r,l,o,o+bn),h=0;h<a;++h)t.stroke()}(t,e,c),c&&vl(t,e,r),_l(t,e,i,n,r,o),t.stroke())}Object.defineProperties(gl,{defaults:{enumerable:bl,value:Hr},instances:{enumerable:bl,value:dl},overrides:{enumerable:bl,value:Nr},registry:{enumerable:bl,value:na},version:{enumerable:bl,value:"3.9.1"},getChart:{enumerable:bl,value:fl},register:{enumerable:bl,value:(...t)=>{na.add(...t),ml()}},unregister:{enumerable:bl,value:(...t)=>{na.remove(...t),ml()}}});class kl extends $s{constructor(t){super(),this.options=void 0,this.circumference=void 0,this.startAngle=void 0,this.endAngle=void 0,this.innerRadius=void 0,this.outerRadius=void 0,this.pixelMargin=0,this.fullCircles=0,t&&Object.assign(this,t)}inRange(t,e,i){const n=this.getProps(["x","y"],i),{angle:r,distance:o}=Dn(n,{x:t,y:e}),{startAngle:s,endAngle:a,innerRadius:l,outerRadius:c,circumference:h}=this.getProps(["startAngle","endAngle","innerRadius","outerRadius","circumference"],i),u=this.options.spacing/2,d=Qi(h,a-s)>=bn||Fn(r,s,a),f=Nn(o,l+u,c+u);return d&&f}getCenterPoint(t){const{x:e,y:i,startAngle:n,endAngle:r,innerRadius:o,outerRadius:s}=this.getProps(["x","y","startAngle","endAngle","innerRadius","outerRadius","circumference"],t),{offset:a,spacing:l}=this.options,c=(n+r)/2,h=(o+s+l+a)/2;return{x:e+Math.cos(c)*h,y:i+Math.sin(c)*h}}tooltipPosition(t){return this.getCenterPoint(t)}draw(t){const{options:e,circumference:i}=this,n=(e.offset||0)/2,r=(e.spacing||0)/2,o=e.circular;if(this.pixelMargin="inner"===e.borderAlign?.33:0,this.fullCircles=i>bn?Math.floor(i/bn):0,0===i||this.innerRadius<0||this.outerRadius<0)return;t.save();let s=0;if(n){s=n/2;const e=(this.startAngle+this.endAngle)/2;t.translate(Math.cos(e)*s,Math.sin(e)*s),this.circumference>=mn&&(s=n)}t.fillStyle=e.backgroundColor,t.strokeStyle=e.borderColor;const a=function(t,e,i,n,r){const{fullCircles:o,startAngle:s,circumference:a}=e;let l=e.endAngle;if(o){_l(t,e,i,n,s+bn,r);for(let e=0;e<o;++e)t.fill();isNaN(a)||(l=s+a%bn,a%bn==0&&(l+=bn))}return _l(t,e,i,n,l,r),t.fill(),l}(t,this,s,r,o);wl(t,this,s,r,a,o),t.restore()}}function Ol(t,e,i=e){t.lineCap=Qi(i.borderCapStyle,e.borderCapStyle),t.setLineDash(Qi(i.borderDash,e.borderDash)),t.lineDashOffset=Qi(i.borderDashOffset,e.borderDashOffset),t.lineJoin=Qi(i.borderJoinStyle,e.borderJoinStyle),t.lineWidth=Qi(i.borderWidth,e.borderWidth),t.strokeStyle=Qi(i.borderColor,e.borderColor)}function Sl(t,e,i){t.lineTo(i.x,i.y)}function Ml(t,e,i={}){const n=t.length,{start:r=0,end:o=n-1}=i,{start:s,end:a}=e,l=Math.max(r,s),c=Math.min(o,a),h=r<s&&o<s||r>a&&o>a;return{count:n,start:l,loop:e.loop,ilen:c<l&&!h?n+c-l:c-l}}function El(t,e,i,n){const{points:r,options:o}=e,{count:s,start:a,loop:l,ilen:c}=Ml(r,i,n),h=function(t){return t.stepped?Zr:t.tension||"monotone"===t.cubicInterpolationMode?to:Sl}(o);let u,d,f,{move:p=!0,reverse:g}=n||{};for(u=0;u<=c;++u)d=r[(a+(g?c-u:u))%s],d.skip||(p?(t.moveTo(d.x,d.y),p=!1):h(t,f,d,g,o.stepped),f=d);return l&&(d=r[(a+(g?c:0))%s],h(t,f,d,g,o.stepped)),!!l}function Pl(t,e,i,n){const r=e.points,{count:o,start:s,ilen:a}=Ml(r,i,n),{move:l=!0,reverse:c}=n||{};let h,u,d,f,p,g,m=0,b=0;const v=t=>(s+(c?a-t:t))%o,y=()=>{f!==p&&(t.lineTo(m,p),t.lineTo(m,f),t.lineTo(m,g))};for(l&&(u=r[v(0)],t.moveTo(u.x,u.y)),h=0;h<=a;++h){if(u=r[v(h)],u.skip)continue;const e=u.x,i=u.y,n=0|e;n===d?(i<f?f=i:i>p&&(p=i),m=(b*m+e)/++b):(y(),t.lineTo(e,i),d=n,b=0,f=p=i),g=i}y()}function Tl(t){const e=t.options,i=e.borderDash&&e.borderDash.length;return!(t._decimated||t._loop||e.tension||"monotone"===e.cubicInterpolationMode||e.stepped||i)?Pl:El}kl.id="arc",kl.defaults={borderAlign:"center",borderColor:"#fff",borderJoinStyle:void 0,borderRadius:0,borderWidth:2,offset:0,spacing:0,angle:void 0,circular:!0},kl.defaultRoutes={backgroundColor:"backgroundColor"};const Ll="function"==typeof Path2D;function Al(t,e,i,n){Ll&&!e.options.segment?function(t,e,i,n){let r=e._path;r||(r=e._path=new Path2D,e.path(r,i,n)&&r.closePath()),Ol(t,e.options),t.stroke(r)}(t,e,i,n):function(t,e,i,n){const{segments:r,options:o}=e,s=Tl(e);for(const a of r)Ol(t,o,a.style),t.beginPath(),s(t,e,a,{start:i,end:i+n-1})&&t.closePath(),t.stroke()}(t,e,i,n)}class Cl extends $s{constructor(t){super(),this.animated=!0,this.options=void 0,this._chart=void 0,this._loop=void 0,this._fullLoop=void 0,this._path=void 0,this._points=void 0,this._segments=void 0,this._decimated=!1,this._pointsUpdated=!1,this._datasetIndex=void 0,t&&Object.assign(this,t)}updateControlPoints(t,e){const i=this.options;if((i.tension||"monotone"===i.cubicInterpolationMode)&&!i.stepped&&!this._pointsUpdated){const n=i.spanGaps?this._loop:this._fullLoop;Ro(this._points,i,t,n,e),this._pointsUpdated=!0}}set points(t){this._points=t,delete this._segments,delete this._path,this._pointsUpdated=!1}get points(){return this._points}get segments(){return this._segments||(this._segments=function(t,e){const i=t.points,n=t.options.spanGaps,r=i.length;if(!r)return[];const o=!!t._loop,{start:s,end:a}=function(t,e,i,n){let r=0,o=e-1;if(i&&!n)for(;r<e&&!t[r].skip;)r++;for(;r<e&&t[r].skip;)r++;for(r%=e,i&&(o+=r);o>r&&t[o%e].skip;)o--;return o%=e,{start:r,end:o}}(i,r,o,n);return ls(t,!0===n?[{start:s,end:a,loop:o}]:function(t,e,i,n){const r=t.length,o=[];let s,a=e,l=t[e];for(s=e+1;s<=i;++s){const i=t[s%r];i.skip||i.stop?l.skip||(n=!1,o.push({start:e%r,end:(s-1)%r,loop:n}),e=a=i.stop?s:null):(a=s,l.skip&&(e=s)),l=i}return null!==a&&o.push({start:e%r,end:a%r,loop:n}),o}(i,s,a<s?a+r:a,!!t._fullLoop&&0===s&&a===r-1),i,e)}(this,this.options.segment))}first(){const t=this.segments,e=this.points;return t.length&&e[t[0].start]}last(){const t=this.segments,e=this.points,i=t.length;return i&&e[t[i-1].end]}interpolate(t,e){const i=this.options,n=t[e],r=this.points,o=as(this,{property:e,start:n,end:n});if(!o.length)return;const s=[],a=function(t){return t.stepped?Jo:t.tension||"monotone"===t.cubicInterpolationMode?Qo:Ko}(i);let l,c;for(l=0,c=o.length;l<c;++l){const{start:c,end:h}=o[l],u=r[c],d=r[h];if(u===d){s.push(u);continue}const f=a(u,d,Math.abs((n-u[e])/(d[e]-u[e])),i.stepped);f[e]=t[e],s.push(f)}return 1===s.length?s[0]:s}pathSegment(t,e,i){return Tl(this)(t,this,e,i)}path(t,e,i){const n=this.segments,r=Tl(this);let o=this._loop;e=e||0,i=i||this.points.length-e;for(const s of n)o&=r(t,this,s,{start:e,end:e+i-1});return!!o}draw(t,e,i,n){const r=this.options||{};(this.points||[]).length&&r.borderWidth&&(t.save(),Al(t,this,i,n),t.restore()),this.animated&&(this._pointsUpdated=!1,this._path=void 0)}}function Dl(t,e,i,n){const r=t.options,{[i]:o}=t.getProps([i],n);return Math.abs(e-o)<r.radius+r.hitRadius}Cl.id="line",Cl.defaults={borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderWidth:3,capBezierPoints:!0,cubicInterpolationMode:"default",fill:!1,spanGaps:!1,stepped:!1,tension:0},Cl.defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"},Cl.descriptors={_scriptable:!0,_indexable:t=>"borderDash"!==t&&"fill"!==t};class jl extends $s{constructor(t){super(),this.options=void 0,this.parsed=void 0,this.skip=void 0,this.stop=void 0,t&&Object.assign(this,t)}inRange(t,e,i){const n=this.options,{x:r,y:o}=this.getProps(["x","y"],i);return Math.pow(t-r,2)+Math.pow(e-o,2)<Math.pow(n.hitRadius+n.radius,2)}inXRange(t,e){return Dl(this,t,"x",e)}inYRange(t,e){return Dl(this,t,"y",e)}getCenterPoint(t){const{x:e,y:i}=this.getProps(["x","y"],t);return{x:e,y:i}}size(t){let e=(t=t||this.options||{}).radius||0;e=Math.max(e,e&&t.hoverRadius||0);return 2*(e+(e&&t.borderWidth||0))}draw(t,e){const i=this.options;this.skip||i.radius<.1||!Kr(this,e,this.size(i)/2)||(t.strokeStyle=i.borderColor,t.lineWidth=i.borderWidth,t.fillStyle=i.backgroundColor,Xr(t,i,this.x,this.y))}getRange(){const t=this.options||{};return t.radius+t.hitRadius}}function Il(t,e){const{x:i,y:n,base:r,width:o,height:s}=t.getProps(["x","y","base","width","height"],e);let a,l,c,h,u;return t.horizontal?(u=s/2,a=Math.min(i,r),l=Math.max(i,r),c=n-u,h=n+u):(u=o/2,a=i-u,l=i+u,c=Math.min(n,r),h=Math.max(n,r)),{left:a,top:c,right:l,bottom:h}}function Rl(t,e,i,n){return t?0:zn(e,i,n)}function Fl(t){const e=Il(t),i=e.right-e.left,n=e.bottom-e.top,r=function(t,e,i){const n=t.options.borderWidth,r=t.borderSkipped,o=co(n);return{t:Rl(r.top,o.top,0,i),r:Rl(r.right,o.right,0,e),b:Rl(r.bottom,o.bottom,0,i),l:Rl(r.left,o.left,0,e)}}(t,i/2,n/2),o=function(t,e,i){const{enableBorderRadius:n}=t.getProps(["enableBorderRadius"]),r=t.options.borderRadius,o=ho(r),s=Math.min(e,i),a=t.borderSkipped,l=n||Gi(r);return{topLeft:Rl(!l||a.top||a.left,o.topLeft,0,s),topRight:Rl(!l||a.top||a.right,o.topRight,0,s),bottomLeft:Rl(!l||a.bottom||a.left,o.bottomLeft,0,s),bottomRight:Rl(!l||a.bottom||a.right,o.bottomRight,0,s)}}(t,i/2,n/2);return{outer:{x:e.left,y:e.top,w:i,h:n,radius:o},inner:{x:e.left+r.l,y:e.top+r.t,w:i-r.l-r.r,h:n-r.t-r.b,radius:{topLeft:Math.max(0,o.topLeft-Math.max(r.t,r.l)),topRight:Math.max(0,o.topRight-Math.max(r.t,r.r)),bottomLeft:Math.max(0,o.bottomLeft-Math.max(r.b,r.l)),bottomRight:Math.max(0,o.bottomRight-Math.max(r.b,r.r))}}}}function zl(t,e,i,n){const r=null===e,o=null===i,s=t&&!(r&&o)&&Il(t,n);return s&&(r||Nn(e,s.left,s.right))&&(o||Nn(i,s.top,s.bottom))}function Nl(t,e){t.rect(e.x,e.y,e.w,e.h)}function Bl(t,e,i={}){const n=t.x!==i.x?-e:0,r=t.y!==i.y?-e:0,o=(t.x+t.w!==i.x+i.w?e:0)-n,s=(t.y+t.h!==i.y+i.h?e:0)-r;return{x:t.x+n,y:t.y+r,w:t.w+o,h:t.h+s,radius:t.radius}}jl.id="point",jl.defaults={borderWidth:1,hitRadius:1,hoverBorderWidth:1,hoverRadius:4,pointStyle:"circle",radius:3,rotation:0},jl.defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};class Wl extends $s{constructor(t){super(),this.options=void 0,this.horizontal=void 0,this.base=void 0,this.width=void 0,this.height=void 0,this.inflateAmount=void 0,t&&Object.assign(this,t)}draw(t){const{inflateAmount:e,options:{borderColor:i,backgroundColor:n}}=this,{inner:r,outer:o}=Fl(this),s=(a=o.radius).topLeft||a.topRight||a.bottomLeft||a.bottomRight?no:Nl;var a;t.save(),o.w===r.w&&o.h===r.h||(t.beginPath(),s(t,Bl(o,e,r)),t.clip(),s(t,Bl(r,-e,o)),t.fillStyle=i,t.fill("evenodd")),t.beginPath(),s(t,Bl(r,e)),t.fillStyle=n,t.fill(),t.restore()}inRange(t,e,i){return zl(this,t,e,i)}inXRange(t,e){return zl(this,t,null,e)}inYRange(t,e){return zl(this,null,t,e)}getCenterPoint(t){const{x:e,y:i,base:n,horizontal:r}=this.getProps(["x","y","base","horizontal"],t);return{x:r?(e+n)/2:e,y:r?i:(i+n)/2}}getRange(t){return"x"===t?this.width/2:this.height/2}}Wl.id="bar",Wl.defaults={borderSkipped:"start",borderWidth:0,borderRadius:0,inflateAmount:"auto",pointStyle:void 0},Wl.defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};var Vl=Object.freeze({__proto__:null,ArcElement:kl,LineElement:Cl,PointElement:jl,BarElement:Wl});function Hl(t){if(t._decimated){const e=t._data;delete t._decimated,delete t._data,Object.defineProperty(t,"data",{value:e})}}function $l(t){t.data.datasets.forEach((t=>{Hl(t)}))}var Ul={id:"decimation",defaults:{algorithm:"min-max",enabled:!1},beforeElementsUpdate:(t,e,i)=>{if(!i.enabled)return void $l(t);const n=t.width;t.data.datasets.forEach(((e,r)=>{const{_data:o,indexAxis:s}=e,a=t.getDatasetMeta(r),l=o||e.data;if("y"===po([s,t.options.indexAxis]))return;if(!a.controller.supportsDecimation)return;const c=t.scales[a.xAxisID];if("linear"!==c.type&&"time"!==c.type)return;if(t.options.parsing)return;let{start:h,count:u}=function(t,e){const i=e.length;let n,r=0;const{iScale:o}=t,{min:s,max:a,minDefined:l,maxDefined:c}=o.getUserBounds();return l&&(r=zn(Wn(e,o.axis,s).lo,0,i-1)),n=c?zn(Wn(e,o.axis,a).hi+1,r,i)-r:i-r,{start:r,count:n}}(a,l);if(u<=(i.threshold||4*n))return void Hl(e);let d;switch(Yi(o)&&(e._data=l,delete e.data,Object.defineProperty(e,"data",{configurable:!0,enumerable:!0,get:function(){return this._decimated},set:function(t){this._data=t}})),i.algorithm){case"lttb":d=function(t,e,i,n,r){const o=r.samples||n;if(o>=i)return t.slice(e,e+i);const s=[],a=(i-2)/(o-2);let l=0;const c=e+i-1;let h,u,d,f,p,g=e;for(s[l++]=t[g],h=0;h<o-2;h++){let n,r=0,o=0;const c=Math.floor((h+1)*a)+1+e,m=Math.min(Math.floor((h+2)*a)+1,i)+e,b=m-c;for(n=c;n<m;n++)r+=t[n].x,o+=t[n].y;r/=b,o/=b;const v=Math.floor(h*a)+1+e,y=Math.min(Math.floor((h+1)*a)+1,i)+e,{x:x,y:_}=t[g];for(d=f=-1,n=v;n<y;n++)f=.5*Math.abs((x-r)*(t[n].y-_)-(x-t[n].x)*(o-_)),f>d&&(d=f,u=t[n],p=n);s[l++]=u,g=p}return s[l++]=t[c],s}(l,h,u,n,i);break;case"min-max":d=function(t,e,i,n){let r,o,s,a,l,c,h,u,d,f,p=0,g=0;const m=[],b=e+i-1,v=t[e].x,y=t[b].x-v;for(r=e;r<e+i;++r){o=t[r],s=(o.x-v)/y*n,a=o.y;const e=0|s;if(e===l)a<d?(d=a,c=r):a>f&&(f=a,h=r),p=(g*p+o.x)/++g;else{const i=r-1;if(!Yi(c)&&!Yi(h)){const e=Math.min(c,h),n=Math.max(c,h);e!==u&&e!==i&&m.push({...t[e],x:p}),n!==u&&n!==i&&m.push({...t[n],x:p})}r>0&&i!==u&&m.push(t[i]),m.push(o),l=e,g=0,d=f=a,c=h=u=r}}return m}(l,h,u,n);break;default:throw new Error(`Unsupported decimation algorithm '${i.algorithm}'`)}e._decimated=d}))},destroy(t){$l(t)}};function ql(t,e,i,n){if(n)return;let r=e[t],o=i[t];return"angle"===t&&(r=Rn(r),o=Rn(o)),{property:t,start:r,end:o}}function Yl(t,e,i){for(;e>t;e--){const t=i[e];if(!isNaN(t.x)&&!isNaN(t.y))break}return e}function Xl(t,e,i,n){return t&&e?n(t[i],e[i]):t?t[i]:e?e[i]:0}function Gl(t,e){let i=[],n=!1;return Xi(t)?(n=!0,i=t):i=function(t,e){const{x:i=null,y:n=null}=t||{},r=e.points,o=[];return e.segments.forEach((({start:t,end:e})=>{e=Yl(t,e,r);const s=r[t],a=r[e];null!==n?(o.push({x:s.x,y:n}),o.push({x:a.x,y:n})):null!==i&&(o.push({x:i,y:s.y}),o.push({x:i,y:a.y}))})),o}(t,e),i.length?new Cl({points:i,options:{tension:0},_loop:n,_fullLoop:n}):null}function Kl(t){return t&&!1!==t.fill}function Jl(t,e,i){let n=t[e].fill;const r=[e];let o;if(!i)return n;for(;!1!==n&&-1===r.indexOf(n);){if(!Ki(n))return n;if(o=t[n],!o)return!1;if(o.visible)return n;r.push(n),n=o.fill}return!1}function Ql(t,e,i){const n=function(t){const e=t.options,i=e.fill;let n=Qi(i&&i.target,i);void 0===n&&(n=!!e.backgroundColor);if(!1===n||null===n)return!1;if(!0===n)return"origin";return n}(t);if(Gi(n))return!isNaN(n.value)&&n;let r=parseFloat(n);return Ki(r)&&Math.floor(r)===r?function(t,e,i,n){"-"!==t&&"+"!==t||(i=e+i);if(i===e||i<0||i>=n)return!1;return i}(n[0],e,r,i):["origin","start","end","stack","shape"].indexOf(n)>=0&&n}function Zl(t,e,i){const n=[];for(let r=0;r<i.length;r++){const o=i[r],{first:s,last:a,point:l}=tc(o,e,"x");if(!(!l||s&&a))if(s)n.unshift(l);else if(t.push(l),!a)break}t.push(...n)}function tc(t,e,i){const n=t.interpolate(e,i);if(!n)return{};const r=n[i],o=t.segments,s=t.points;let a=!1,l=!1;for(let t=0;t<o.length;t++){const e=o[t],n=s[e.start][i],c=s[e.end][i];if(Nn(r,n,c)){a=r===n,l=r===c;break}}return{first:a,last:l,point:n}}class ec{constructor(t){this.x=t.x,this.y=t.y,this.radius=t.radius}pathSegment(t,e,i){const{x:n,y:r,radius:o}=this;return e=e||{start:0,end:bn},t.arc(n,r,o,e.end,e.start,!0),!i.bounds}interpolate(t){const{x:e,y:i,radius:n}=this,r=t.angle;return{x:e+Math.cos(r)*n,y:i+Math.sin(r)*n,angle:r}}}function ic(t){const{chart:e,fill:i,line:n}=t;if(Ki(i))return function(t,e){const i=t.getDatasetMeta(e),n=i&&t.isDatasetVisible(e);return n?i.dataset:null}(e,i);if("stack"===i)return function(t){const{scale:e,index:i,line:n}=t,r=[],o=n.segments,s=n.points,a=function(t,e){const i=[],n=t.getMatchingVisibleMetas("line");for(let t=0;t<n.length;t++){const r=n[t];if(r.index===e)break;r.hidden||i.unshift(r.dataset)}return i}(e,i);a.push(Gl({x:null,y:e.bottom},n));for(let t=0;t<o.length;t++){const e=o[t];for(let t=e.start;t<=e.end;t++)Zl(r,s[t],a)}return new Cl({points:r,options:{}})}(t);if("shape"===i)return!0;const r=function(t){const e=t.scale||{};if(e.getPointPositionForValue)return function(t){const{scale:e,fill:i}=t,n=e.options,r=e.getLabels().length,o=n.reverse?e.max:e.min,s=function(t,e,i){let n;return n="start"===t?i:"end"===t?e.options.reverse?e.min:e.max:Gi(t)?t.value:e.getBaseValue(),n}(i,e,o),a=[];if(n.grid.circular){const t=e.getPointPositionForValue(0,o);return new ec({x:t.x,y:t.y,radius:e.getDistanceFromCenterForValue(s)})}for(let t=0;t<r;++t)a.push(e.getPointPositionForValue(t,s));return a}(t);return function(t){const{scale:e={},fill:i}=t,n=function(t,e){let i=null;return"start"===t?i=e.bottom:"end"===t?i=e.top:Gi(t)?i=e.getPixelForValue(t.value):e.getBasePixel&&(i=e.getBasePixel()),i}(i,e);if(Ki(n)){const t=e.isHorizontal();return{x:t?n:null,y:t?null:n}}return null}(t)}(t);return r instanceof ec?r:Gl(r,n)}function nc(t,e,i){const n=ic(e),{line:r,scale:o,axis:s}=e,a=r.options,l=a.fill,c=a.backgroundColor,{above:h=c,below:u=c}=l||{};n&&r.points.length&&(Jr(t,i),function(t,e){const{line:i,target:n,above:r,below:o,area:s,scale:a}=e,l=i._loop?"angle":e.axis;t.save(),"x"===l&&o!==r&&(rc(t,n,s.top),oc(t,{line:i,target:n,color:r,scale:a,property:l}),t.restore(),t.save(),rc(t,n,s.bottom));oc(t,{line:i,target:n,color:o,scale:a,property:l}),t.restore()}(t,{line:r,target:n,above:h,below:u,area:i,scale:o,axis:s}),Qr(t))}function rc(t,e,i){const{segments:n,points:r}=e;let o=!0,s=!1;t.beginPath();for(const a of n){const{start:n,end:l}=a,c=r[n],h=r[Yl(n,l,r)];o?(t.moveTo(c.x,c.y),o=!1):(t.lineTo(c.x,i),t.lineTo(c.x,c.y)),s=!!e.pathSegment(t,a,{move:s}),s?t.closePath():t.lineTo(h.x,i)}t.lineTo(e.first().x,i),t.closePath(),t.clip()}function oc(t,e){const{line:i,target:n,property:r,color:o,scale:s}=e,a=function(t,e,i){const n=t.segments,r=t.points,o=e.points,s=[];for(const t of n){let{start:n,end:a}=t;a=Yl(n,a,r);const l=ql(i,r[n],r[a],t.loop);if(!e.segments){s.push({source:t,target:l,start:r[n],end:r[a]});continue}const c=as(e,l);for(const e of c){const n=ql(i,o[e.start],o[e.end],e.loop),a=ss(t,r,n);for(const t of a)s.push({source:t,target:e,start:{[i]:Xl(l,n,"start",Math.max)},end:{[i]:Xl(l,n,"end",Math.min)}})}}return s}(i,n,r);for(const{source:e,target:l,start:c,end:h}of a){const{style:{backgroundColor:a=o}={}}=e,u=!0!==n;t.save(),t.fillStyle=a,sc(t,s,u&&ql(r,c,h)),t.beginPath();const d=!!i.pathSegment(t,e);let f;if(u){d?t.closePath():ac(t,n,h,r);const e=!!n.pathSegment(t,l,{move:d,reverse:!0});f=d&&e,f||ac(t,n,c,r)}t.closePath(),t.fill(f?"evenodd":"nonzero"),t.restore()}}function sc(t,e,i){const{top:n,bottom:r}=e.chart.chartArea,{property:o,start:s,end:a}=i||{};"x"===o&&(t.beginPath(),t.rect(s,n,a-s,r-n),t.clip())}function ac(t,e,i,n){const r=e.interpolate(i,n);r&&t.lineTo(r.x,r.y)}var lc={id:"filler",afterDatasetsUpdate(t,e,i){const n=(t.data.datasets||[]).length,r=[];let o,s,a,l;for(s=0;s<n;++s)o=t.getDatasetMeta(s),a=o.dataset,l=null,a&&a.options&&a instanceof Cl&&(l={visible:t.isDatasetVisible(s),index:s,fill:Ql(a,s,n),chart:t,axis:o.controller.options.indexAxis,scale:o.vScale,line:a}),o.$filler=l,r.push(l);for(s=0;s<n;++s)l=r[s],l&&!1!==l.fill&&(l.fill=Jl(r,s,i.propagate))},beforeDraw(t,e,i){const n="beforeDraw"===i.drawTime,r=t.getSortedVisibleDatasetMetas(),o=t.chartArea;for(let e=r.length-1;e>=0;--e){const i=r[e].$filler;i&&(i.line.updateControlPoints(o,i.axis),n&&i.fill&&nc(t.ctx,i,o))}},beforeDatasetsDraw(t,e,i){if("beforeDatasetsDraw"!==i.drawTime)return;const n=t.getSortedVisibleDatasetMetas();for(let e=n.length-1;e>=0;--e){const i=n[e].$filler;Kl(i)&&nc(t.ctx,i,t.chartArea)}},beforeDatasetDraw(t,e,i){const n=e.meta.$filler;Kl(n)&&"beforeDatasetDraw"===i.drawTime&&nc(t.ctx,n,t.chartArea)},defaults:{propagate:!0,drawTime:"beforeDatasetDraw"}};const cc=(t,e)=>{let{boxHeight:i=e,boxWidth:n=e}=t;return t.usePointStyle&&(i=Math.min(i,e),n=t.pointStyleWidth||Math.min(n,e)),{boxWidth:n,boxHeight:i,itemHeight:Math.max(e,i)}};class hc extends $s{constructor(t){super(),this._added=!1,this.legendHitBoxes=[],this._hoveredItem=null,this.doughnutMode=!1,this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this.legendItems=void 0,this.columnSizes=void 0,this.lineWidths=void 0,this.maxHeight=void 0,this.maxWidth=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.height=void 0,this.width=void 0,this._margins=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,e,i){this.maxWidth=t,this.maxHeight=e,this._margins=i,this.setDimensions(),this.buildLabels(),this.fit()}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=this._margins.left,this.right=this.width):(this.height=this.maxHeight,this.top=this._margins.top,this.bottom=this.height)}buildLabels(){const t=this.options.labels||{};let e=tn(t.generateLabels,[this.chart],this)||[];t.filter&&(e=e.filter((e=>t.filter(e,this.chart.data)))),t.sort&&(e=e.sort(((e,i)=>t.sort(e,i,this.chart.data)))),this.options.reverse&&e.reverse(),this.legendItems=e}fit(){const{options:t,ctx:e}=this;if(!t.display)return void(this.width=this.height=0);const i=t.labels,n=fo(i.font),r=n.size,o=this._computeTitleHeight(),{boxWidth:s,itemHeight:a}=cc(i,r);let l,c;e.font=n.string,this.isHorizontal()?(l=this.maxWidth,c=this._fitRows(o,r,s,a)+10):(c=this.maxHeight,l=this._fitCols(o,r,s,a)+10),this.width=Math.min(l,t.maxWidth||this.maxWidth),this.height=Math.min(c,t.maxHeight||this.maxHeight)}_fitRows(t,e,i,n){const{ctx:r,maxWidth:o,options:{labels:{padding:s}}}=this,a=this.legendHitBoxes=[],l=this.lineWidths=[0],c=n+s;let h=t;r.textAlign="left",r.textBaseline="middle";let u=-1,d=-c;return this.legendItems.forEach(((t,f)=>{const p=i+e/2+r.measureText(t.text).width;(0===f||l[l.length-1]+p+2*s>o)&&(h+=c,l[l.length-(f>0?0:1)]=0,d+=c,u++),a[f]={left:0,top:d,row:u,width:p,height:n},l[l.length-1]+=p+s})),h}_fitCols(t,e,i,n){const{ctx:r,maxHeight:o,options:{labels:{padding:s}}}=this,a=this.legendHitBoxes=[],l=this.columnSizes=[],c=o-t;let h=s,u=0,d=0,f=0,p=0;return this.legendItems.forEach(((t,o)=>{const g=i+e/2+r.measureText(t.text).width;o>0&&d+n+2*s>c&&(h+=u+s,l.push({width:u,height:d}),f+=u+s,p++,u=d=0),a[o]={left:f,top:d,col:p,width:g,height:n},u=Math.max(u,g),d+=n+s})),h+=u,l.push({width:u,height:d}),h}adjustHitBoxes(){if(!this.options.display)return;const t=this._computeTitleHeight(),{legendHitBoxes:e,options:{align:i,labels:{padding:n},rtl:r}}=this,o=es(r,this.left,this.width);if(this.isHorizontal()){let r=0,s=Gn(i,this.left+n,this.right-this.lineWidths[r]);for(const a of e)r!==a.row&&(r=a.row,s=Gn(i,this.left+n,this.right-this.lineWidths[r])),a.top+=this.top+t+n,a.left=o.leftForLtr(o.x(s),a.width),s+=a.width+n}else{let r=0,s=Gn(i,this.top+t+n,this.bottom-this.columnSizes[r].height);for(const a of e)a.col!==r&&(r=a.col,s=Gn(i,this.top+t+n,this.bottom-this.columnSizes[r].height)),a.top=s,a.left+=this.left+n,a.left=o.leftForLtr(o.x(a.left),a.width),s+=a.height+n}}isHorizontal(){return"top"===this.options.position||"bottom"===this.options.position}draw(){if(this.options.display){const t=this.ctx;Jr(t,this),this._draw(),Qr(t)}}_draw(){const{options:t,columnSizes:e,lineWidths:i,ctx:n}=this,{align:r,labels:o}=t,s=Hr.color,a=es(t.rtl,this.left,this.width),l=fo(o.font),{color:c,padding:h}=o,u=l.size,d=u/2;let f;this.drawTitle(),n.textAlign=a.textAlign("left"),n.textBaseline="middle",n.lineWidth=.5,n.font=l.string;const{boxWidth:p,boxHeight:g,itemHeight:m}=cc(o,u),b=this.isHorizontal(),v=this._computeTitleHeight();f=b?{x:Gn(r,this.left+h,this.right-i[0]),y:this.top+h+v,line:0}:{x:this.left+h,y:Gn(r,this.top+v+h,this.bottom-e[0].height),line:0},is(this.ctx,t.textDirection);const y=m+h;this.legendItems.forEach(((x,_)=>{n.strokeStyle=x.fontColor||c,n.fillStyle=x.fontColor||c;const w=n.measureText(x.text).width,k=a.textAlign(x.textAlign||(x.textAlign=o.textAlign)),O=p+d+w;let S=f.x,M=f.y;a.setWidth(this.width),b?_>0&&S+O+h>this.right&&(M=f.y+=y,f.line++,S=f.x=Gn(r,this.left+h,this.right-i[f.line])):_>0&&M+y>this.bottom&&(S=f.x=S+e[f.line].width+h,f.line++,M=f.y=Gn(r,this.top+v+h,this.bottom-e[f.line].height));!function(t,e,i){if(isNaN(p)||p<=0||isNaN(g)||g<0)return;n.save();const r=Qi(i.lineWidth,1);if(n.fillStyle=Qi(i.fillStyle,s),n.lineCap=Qi(i.lineCap,"butt"),n.lineDashOffset=Qi(i.lineDashOffset,0),n.lineJoin=Qi(i.lineJoin,"miter"),n.lineWidth=r,n.strokeStyle=Qi(i.strokeStyle,s),n.setLineDash(Qi(i.lineDash,[])),o.usePointStyle){const s={radius:g*Math.SQRT2/2,pointStyle:i.pointStyle,rotation:i.rotation,borderWidth:r},l=a.xPlus(t,p/2);Gr(n,s,l,e+d,o.pointStyleWidth&&p)}else{const o=e+Math.max((u-g)/2,0),s=a.leftForLtr(t,p),l=ho(i.borderRadius);n.beginPath(),Object.values(l).some((t=>0!==t))?no(n,{x:s,y:o,w:p,h:g,radius:l}):n.rect(s,o,p,g),n.fill(),0!==r&&n.stroke()}n.restore()}(a.x(S),M,x),S=((t,e,i,n)=>t===(n?"left":"right")?i:"center"===t?(e+i)/2:e)(k,S+p+d,b?S+O:this.right,t.rtl),function(t,e,i){eo(n,i.text,t,e+m/2,l,{strikethrough:i.hidden,textAlign:a.textAlign(i.textAlign)})}(a.x(S),M,x),b?f.x+=O+h:f.y+=y})),ns(this.ctx,t.textDirection)}drawTitle(){const t=this.options,e=t.title,i=fo(e.font),n=uo(e.padding);if(!e.display)return;const r=es(t.rtl,this.left,this.width),o=this.ctx,s=e.position,a=i.size/2,l=n.top+a;let c,h=this.left,u=this.width;if(this.isHorizontal())u=Math.max(...this.lineWidths),c=this.top+l,h=Gn(t.align,h,this.right-u);else{const e=this.columnSizes.reduce(((t,e)=>Math.max(t,e.height)),0);c=l+Gn(t.align,this.top,this.bottom-e-t.labels.padding-this._computeTitleHeight())}const d=Gn(s,h,h+u);o.textAlign=r.textAlign(Xn(s)),o.textBaseline="middle",o.strokeStyle=e.color,o.fillStyle=e.color,o.font=i.string,eo(o,e.text,d,c,i)}_computeTitleHeight(){const t=this.options.title,e=fo(t.font),i=uo(t.padding);return t.display?e.lineHeight+i.height:0}_getLegendItemAt(t,e){let i,n,r;if(Nn(t,this.left,this.right)&&Nn(e,this.top,this.bottom))for(r=this.legendHitBoxes,i=0;i<r.length;++i)if(n=r[i],Nn(t,n.left,n.left+n.width)&&Nn(e,n.top,n.top+n.height))return this.legendItems[i];return null}handleEvent(t){const e=this.options;if(!function(t,e){if(("mousemove"===t||"mouseout"===t)&&(e.onHover||e.onLeave))return!0;if(e.onClick&&("click"===t||"mouseup"===t))return!0;return!1}(t.type,e))return;const i=this._getLegendItemAt(t.x,t.y);if("mousemove"===t.type||"mouseout"===t.type){const o=this._hoveredItem,s=(r=i,null!==(n=o)&&null!==r&&n.datasetIndex===r.datasetIndex&&n.index===r.index);o&&!s&&tn(e.onLeave,[t,o,this],this),this._hoveredItem=i,i&&!s&&tn(e.onHover,[t,i,this],this)}else i&&tn(e.onClick,[t,i,this],this);var n,r}}var uc={id:"legend",_element:hc,start(t,e,i){const n=t.legend=new hc({ctx:t.ctx,options:i,chart:t});Pa.configure(t,n,i),Pa.addBox(t,n)},stop(t){Pa.removeBox(t,t.legend),delete t.legend},beforeUpdate(t,e,i){const n=t.legend;Pa.configure(t,n,i),n.options=i},afterUpdate(t){const e=t.legend;e.buildLabels(),e.adjustHitBoxes()},afterEvent(t,e){e.replay||t.legend.handleEvent(e.event)},defaults:{display:!0,position:"top",align:"center",fullSize:!0,reverse:!1,weight:1e3,onClick(t,e,i){const n=e.datasetIndex,r=i.chart;r.isDatasetVisible(n)?(r.hide(n),e.hidden=!0):(r.show(n),e.hidden=!1)},onHover:null,onLeave:null,labels:{color:t=>t.chart.options.color,boxWidth:40,padding:10,generateLabels(t){const e=t.data.datasets,{labels:{usePointStyle:i,pointStyle:n,textAlign:r,color:o}}=t.legend.options;return t._getSortedDatasetMetas().map((t=>{const s=t.controller.getStyle(i?0:void 0),a=uo(s.borderWidth);return{text:e[t.index].label,fillStyle:s.backgroundColor,fontColor:o,hidden:!t.visible,lineCap:s.borderCapStyle,lineDash:s.borderDash,lineDashOffset:s.borderDashOffset,lineJoin:s.borderJoinStyle,lineWidth:(a.width+a.height)/4,strokeStyle:s.borderColor,pointStyle:n||s.pointStyle,rotation:s.rotation,textAlign:r||s.textAlign,borderRadius:0,datasetIndex:t.index}}),this)}},title:{color:t=>t.chart.options.color,display:!1,position:"center",text:""}},descriptors:{_scriptable:t=>!t.startsWith("on"),labels:{_scriptable:t=>!["generateLabels","filter","sort"].includes(t)}}};class dc extends $s{constructor(t){super(),this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this._padding=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,e){const i=this.options;if(this.left=0,this.top=0,!i.display)return void(this.width=this.height=this.right=this.bottom=0);this.width=this.right=t,this.height=this.bottom=e;const n=Xi(i.text)?i.text.length:1;this._padding=uo(i.padding);const r=n*fo(i.font).lineHeight+this._padding.height;this.isHorizontal()?this.height=r:this.width=r}isHorizontal(){const t=this.options.position;return"top"===t||"bottom"===t}_drawArgs(t){const{top:e,left:i,bottom:n,right:r,options:o}=this,s=o.align;let a,l,c,h=0;return this.isHorizontal()?(l=Gn(s,i,r),c=e+t,a=r-i):("left"===o.position?(l=i+t,c=Gn(s,n,e),h=-.5*mn):(l=r-t,c=Gn(s,e,n),h=.5*mn),a=n-e),{titleX:l,titleY:c,maxWidth:a,rotation:h}}draw(){const t=this.ctx,e=this.options;if(!e.display)return;const i=fo(e.font),n=i.lineHeight/2+this._padding.top,{titleX:r,titleY:o,maxWidth:s,rotation:a}=this._drawArgs(n);eo(t,e.text,0,0,i,{color:e.color,maxWidth:s,rotation:a,textAlign:Xn(e.align),textBaseline:"middle",translation:[r,o]})}}var fc={id:"title",_element:dc,start(t,e,i){!function(t,e){const i=new dc({ctx:t.ctx,options:e,chart:t});Pa.configure(t,i,e),Pa.addBox(t,i),t.titleBlock=i}(t,i)},stop(t){const e=t.titleBlock;Pa.removeBox(t,e),delete t.titleBlock},beforeUpdate(t,e,i){const n=t.titleBlock;Pa.configure(t,n,i),n.options=i},defaults:{align:"center",display:!1,font:{weight:"bold"},fullSize:!0,padding:10,position:"top",text:"",weight:2e3},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}};const pc=new WeakMap;var gc={id:"subtitle",start(t,e,i){const n=new dc({ctx:t.ctx,options:i,chart:t});Pa.configure(t,n,i),Pa.addBox(t,n),pc.set(t,n)},stop(t){Pa.removeBox(t,pc.get(t)),pc.delete(t)},beforeUpdate(t,e,i){const n=pc.get(t);Pa.configure(t,n,i),n.options=i},defaults:{align:"center",display:!1,font:{weight:"normal"},fullSize:!0,padding:0,position:"top",text:"",weight:1500},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}};const mc={average(t){if(!t.length)return!1;let e,i,n=0,r=0,o=0;for(e=0,i=t.length;e<i;++e){const i=t[e].element;if(i&&i.hasValue()){const t=i.tooltipPosition();n+=t.x,r+=t.y,++o}}return{x:n/o,y:r/o}},nearest(t,e){if(!t.length)return!1;let i,n,r,o=e.x,s=e.y,a=Number.POSITIVE_INFINITY;for(i=0,n=t.length;i<n;++i){const n=t[i].element;if(n&&n.hasValue()){const t=jn(e,n.getCenterPoint());t<a&&(a=t,r=n)}}if(r){const t=r.tooltipPosition();o=t.x,s=t.y}return{x:o,y:s}}};function bc(t,e){return e&&(Xi(e)?Array.prototype.push.apply(t,e):t.push(e)),t}function vc(t){return("string"==typeof t||t instanceof String)&&t.indexOf("\n")>-1?t.split("\n"):t}function yc(t,e){const{element:i,datasetIndex:n,index:r}=e,o=t.getDatasetMeta(n).controller,{label:s,value:a}=o.getLabelAndValue(r);return{chart:t,label:s,parsed:o.getParsed(r),raw:t.data.datasets[n].data[r],formattedValue:a,dataset:o.getDataset(),dataIndex:r,datasetIndex:n,element:i}}function xc(t,e){const i=t.chart.ctx,{body:n,footer:r,title:o}=t,{boxWidth:s,boxHeight:a}=e,l=fo(e.bodyFont),c=fo(e.titleFont),h=fo(e.footerFont),u=o.length,d=r.length,f=n.length,p=uo(e.padding);let g=p.height,m=0,b=n.reduce(((t,e)=>t+e.before.length+e.lines.length+e.after.length),0);if(b+=t.beforeBody.length+t.afterBody.length,u&&(g+=u*c.lineHeight+(u-1)*e.titleSpacing+e.titleMarginBottom),b){g+=f*(e.displayColors?Math.max(a,l.lineHeight):l.lineHeight)+(b-f)*l.lineHeight+(b-1)*e.bodySpacing}d&&(g+=e.footerMarginTop+d*h.lineHeight+(d-1)*e.footerSpacing);let v=0;const y=function(t){m=Math.max(m,i.measureText(t).width+v)};return i.save(),i.font=c.string,en(t.title,y),i.font=l.string,en(t.beforeBody.concat(t.afterBody),y),v=e.displayColors?s+2+e.boxPadding:0,en(n,(t=>{en(t.before,y),en(t.lines,y),en(t.after,y)})),v=0,i.font=h.string,en(t.footer,y),i.restore(),m+=p.width,{width:m,height:g}}function _c(t,e,i,n){const{x:r,width:o}=i,{width:s,chartArea:{left:a,right:l}}=t;let c="center";return"center"===n?c=r<=(a+l)/2?"left":"right":r<=o/2?c="left":r>=s-o/2&&(c="right"),function(t,e,i,n){const{x:r,width:o}=n,s=i.caretSize+i.caretPadding;return"left"===t&&r+o+s>e.width||"right"===t&&r-o-s<0||void 0}(c,t,e,i)&&(c="center"),c}function wc(t,e,i){const n=i.yAlign||e.yAlign||function(t,e){const{y:i,height:n}=e;return i<n/2?"top":i>t.height-n/2?"bottom":"center"}(t,i);return{xAlign:i.xAlign||e.xAlign||_c(t,e,i,n),yAlign:n}}function kc(t,e,i,n){const{caretSize:r,caretPadding:o,cornerRadius:s}=t,{xAlign:a,yAlign:l}=i,c=r+o,{topLeft:h,topRight:u,bottomLeft:d,bottomRight:f}=ho(s);let p=function(t,e){let{x:i,width:n}=t;return"right"===e?i-=n:"center"===e&&(i-=n/2),i}(e,a);const g=function(t,e,i){let{y:n,height:r}=t;return"top"===e?n+=i:n-="bottom"===e?r+i:r/2,n}(e,l,c);return"center"===l?"left"===a?p+=c:"right"===a&&(p-=c):"left"===a?p-=Math.max(h,d)+r:"right"===a&&(p+=Math.max(u,f)+r),{x:zn(p,0,n.width-e.width),y:zn(g,0,n.height-e.height)}}function Oc(t,e,i){const n=uo(i.padding);return"center"===e?t.x+t.width/2:"right"===e?t.x+t.width-n.right:t.x+n.left}function Sc(t){return bc([],vc(t))}function Mc(t,e){const i=e&&e.dataset&&e.dataset.tooltip&&e.dataset.tooltip.callbacks;return i?t.override(i):t}class Ec extends $s{constructor(t){super(),this.opacity=0,this._active=[],this._eventPosition=void 0,this._size=void 0,this._cachedAnimations=void 0,this._tooltipItems=[],this.$animations=void 0,this.$context=void 0,this.chart=t.chart||t._chart,this._chart=this.chart,this.options=t.options,this.dataPoints=void 0,this.title=void 0,this.beforeBody=void 0,this.body=void 0,this.afterBody=void 0,this.footer=void 0,this.xAlign=void 0,this.yAlign=void 0,this.x=void 0,this.y=void 0,this.height=void 0,this.width=void 0,this.caretX=void 0,this.caretY=void 0,this.labelColors=void 0,this.labelPointStyles=void 0,this.labelTextColors=void 0}initialize(t){this.options=t,this._cachedAnimations=void 0,this.$context=void 0}_resolveAnimations(){const t=this._cachedAnimations;if(t)return t;const e=this.chart,i=this.options.setContext(this.getContext()),n=i.enabled&&e.options.animation&&i.animations,r=new ms(this.chart,n);return n._cacheable&&(this._cachedAnimations=Object.freeze(r)),r}getContext(){return this.$context||(this.$context=(t=this.chart.getContext(),e=this,i=this._tooltipItems,go(t,{tooltip:e,tooltipItems:i,type:"tooltip"})));var t,e,i}getTitle(t,e){const{callbacks:i}=e,n=i.beforeTitle.apply(this,[t]),r=i.title.apply(this,[t]),o=i.afterTitle.apply(this,[t]);let s=[];return s=bc(s,vc(n)),s=bc(s,vc(r)),s=bc(s,vc(o)),s}getBeforeBody(t,e){return Sc(e.callbacks.beforeBody.apply(this,[t]))}getBody(t,e){const{callbacks:i}=e,n=[];return en(t,(t=>{const e={before:[],lines:[],after:[]},r=Mc(i,t);bc(e.before,vc(r.beforeLabel.call(this,t))),bc(e.lines,r.label.call(this,t)),bc(e.after,vc(r.afterLabel.call(this,t))),n.push(e)})),n}getAfterBody(t,e){return Sc(e.callbacks.afterBody.apply(this,[t]))}getFooter(t,e){const{callbacks:i}=e,n=i.beforeFooter.apply(this,[t]),r=i.footer.apply(this,[t]),o=i.afterFooter.apply(this,[t]);let s=[];return s=bc(s,vc(n)),s=bc(s,vc(r)),s=bc(s,vc(o)),s}_createItems(t){const e=this._active,i=this.chart.data,n=[],r=[],o=[];let s,a,l=[];for(s=0,a=e.length;s<a;++s)l.push(yc(this.chart,e[s]));return t.filter&&(l=l.filter(((e,n,r)=>t.filter(e,n,r,i)))),t.itemSort&&(l=l.sort(((e,n)=>t.itemSort(e,n,i)))),en(l,(e=>{const i=Mc(t.callbacks,e);n.push(i.labelColor.call(this,e)),r.push(i.labelPointStyle.call(this,e)),o.push(i.labelTextColor.call(this,e))})),this.labelColors=n,this.labelPointStyles=r,this.labelTextColors=o,this.dataPoints=l,l}update(t,e){const i=this.options.setContext(this.getContext()),n=this._active;let r,o=[];if(n.length){const t=mc[i.position].call(this,n,this._eventPosition);o=this._createItems(i),this.title=this.getTitle(o,i),this.beforeBody=this.getBeforeBody(o,i),this.body=this.getBody(o,i),this.afterBody=this.getAfterBody(o,i),this.footer=this.getFooter(o,i);const e=this._size=xc(this,i),s=Object.assign({},t,e),a=wc(this.chart,i,s),l=kc(i,s,a,this.chart);this.xAlign=a.xAlign,this.yAlign=a.yAlign,r={opacity:1,x:l.x,y:l.y,width:e.width,height:e.height,caretX:t.x,caretY:t.y}}else 0!==this.opacity&&(r={opacity:0});this._tooltipItems=o,this.$context=void 0,r&&this._resolveAnimations().update(this,r),t&&i.external&&i.external.call(this,{chart:this.chart,tooltip:this,replay:e})}drawCaret(t,e,i,n){const r=this.getCaretPosition(t,i,n);e.lineTo(r.x1,r.y1),e.lineTo(r.x2,r.y2),e.lineTo(r.x3,r.y3)}getCaretPosition(t,e,i){const{xAlign:n,yAlign:r}=this,{caretSize:o,cornerRadius:s}=i,{topLeft:a,topRight:l,bottomLeft:c,bottomRight:h}=ho(s),{x:u,y:d}=t,{width:f,height:p}=e;let g,m,b,v,y,x;return"center"===r?(y=d+p/2,"left"===n?(g=u,m=g-o,v=y+o,x=y-o):(g=u+f,m=g+o,v=y-o,x=y+o),b=g):(m="left"===n?u+Math.max(a,c)+o:"right"===n?u+f-Math.max(l,h)-o:this.caretX,"top"===r?(v=d,y=v-o,g=m-o,b=m+o):(v=d+p,y=v+o,g=m+o,b=m-o),x=v),{x1:g,x2:m,x3:b,y1:v,y2:y,y3:x}}drawTitle(t,e,i){const n=this.title,r=n.length;let o,s,a;if(r){const l=es(i.rtl,this.x,this.width);for(t.x=Oc(this,i.titleAlign,i),e.textAlign=l.textAlign(i.titleAlign),e.textBaseline="middle",o=fo(i.titleFont),s=i.titleSpacing,e.fillStyle=i.titleColor,e.font=o.string,a=0;a<r;++a)e.fillText(n[a],l.x(t.x),t.y+o.lineHeight/2),t.y+=o.lineHeight+s,a+1===r&&(t.y+=i.titleMarginBottom-s)}}_drawColorBox(t,e,i,n,r){const o=this.labelColors[i],s=this.labelPointStyles[i],{boxHeight:a,boxWidth:l,boxPadding:c}=r,h=fo(r.bodyFont),u=Oc(this,"left",r),d=n.x(u),f=a<h.lineHeight?(h.lineHeight-a)/2:0,p=e.y+f;if(r.usePointStyle){const e={radius:Math.min(l,a)/2,pointStyle:s.pointStyle,rotation:s.rotation,borderWidth:1},i=n.leftForLtr(d,l)+l/2,c=p+a/2;t.strokeStyle=r.multiKeyBackground,t.fillStyle=r.multiKeyBackground,Xr(t,e,i,c),t.strokeStyle=o.borderColor,t.fillStyle=o.backgroundColor,Xr(t,e,i,c)}else{t.lineWidth=Gi(o.borderWidth)?Math.max(...Object.values(o.borderWidth)):o.borderWidth||1,t.strokeStyle=o.borderColor,t.setLineDash(o.borderDash||[]),t.lineDashOffset=o.borderDashOffset||0;const e=n.leftForLtr(d,l-c),i=n.leftForLtr(n.xPlus(d,1),l-c-2),s=ho(o.borderRadius);Object.values(s).some((t=>0!==t))?(t.beginPath(),t.fillStyle=r.multiKeyBackground,no(t,{x:e,y:p,w:l,h:a,radius:s}),t.fill(),t.stroke(),t.fillStyle=o.backgroundColor,t.beginPath(),no(t,{x:i,y:p+1,w:l-2,h:a-2,radius:s}),t.fill()):(t.fillStyle=r.multiKeyBackground,t.fillRect(e,p,l,a),t.strokeRect(e,p,l,a),t.fillStyle=o.backgroundColor,t.fillRect(i,p+1,l-2,a-2))}t.fillStyle=this.labelTextColors[i]}drawBody(t,e,i){const{body:n}=this,{bodySpacing:r,bodyAlign:o,displayColors:s,boxHeight:a,boxWidth:l,boxPadding:c}=i,h=fo(i.bodyFont);let u=h.lineHeight,d=0;const f=es(i.rtl,this.x,this.width),p=function(i){e.fillText(i,f.x(t.x+d),t.y+u/2),t.y+=u+r},g=f.textAlign(o);let m,b,v,y,x,_,w;for(e.textAlign=o,e.textBaseline="middle",e.font=h.string,t.x=Oc(this,g,i),e.fillStyle=i.bodyColor,en(this.beforeBody,p),d=s&&"right"!==g?"center"===o?l/2+c:l+2+c:0,y=0,_=n.length;y<_;++y){for(m=n[y],b=this.labelTextColors[y],e.fillStyle=b,en(m.before,p),v=m.lines,s&&v.length&&(this._drawColorBox(e,t,y,f,i),u=Math.max(h.lineHeight,a)),x=0,w=v.length;x<w;++x)p(v[x]),u=h.lineHeight;en(m.after,p)}d=0,u=h.lineHeight,en(this.afterBody,p),t.y-=r}drawFooter(t,e,i){const n=this.footer,r=n.length;let o,s;if(r){const a=es(i.rtl,this.x,this.width);for(t.x=Oc(this,i.footerAlign,i),t.y+=i.footerMarginTop,e.textAlign=a.textAlign(i.footerAlign),e.textBaseline="middle",o=fo(i.footerFont),e.fillStyle=i.footerColor,e.font=o.string,s=0;s<r;++s)e.fillText(n[s],a.x(t.x),t.y+o.lineHeight/2),t.y+=o.lineHeight+i.footerSpacing}}drawBackground(t,e,i,n){const{xAlign:r,yAlign:o}=this,{x:s,y:a}=t,{width:l,height:c}=i,{topLeft:h,topRight:u,bottomLeft:d,bottomRight:f}=ho(n.cornerRadius);e.fillStyle=n.backgroundColor,e.strokeStyle=n.borderColor,e.lineWidth=n.borderWidth,e.beginPath(),e.moveTo(s+h,a),"top"===o&&this.drawCaret(t,e,i,n),e.lineTo(s+l-u,a),e.quadraticCurveTo(s+l,a,s+l,a+u),"center"===o&&"right"===r&&this.drawCaret(t,e,i,n),e.lineTo(s+l,a+c-f),e.quadraticCurveTo(s+l,a+c,s+l-f,a+c),"bottom"===o&&this.drawCaret(t,e,i,n),e.lineTo(s+d,a+c),e.quadraticCurveTo(s,a+c,s,a+c-d),"center"===o&&"left"===r&&this.drawCaret(t,e,i,n),e.lineTo(s,a+h),e.quadraticCurveTo(s,a,s+h,a),e.closePath(),e.fill(),n.borderWidth>0&&e.stroke()}_updateAnimationTarget(t){const e=this.chart,i=this.$animations,n=i&&i.x,r=i&&i.y;if(n||r){const i=mc[t.position].call(this,this._active,this._eventPosition);if(!i)return;const o=this._size=xc(this,t),s=Object.assign({},i,this._size),a=wc(e,t,s),l=kc(t,s,a,e);n._to===l.x&&r._to===l.y||(this.xAlign=a.xAlign,this.yAlign=a.yAlign,this.width=o.width,this.height=o.height,this.caretX=i.x,this.caretY=i.y,this._resolveAnimations().update(this,l))}}_willRender(){return!!this.opacity}draw(t){const e=this.options.setContext(this.getContext());let i=this.opacity;if(!i)return;this._updateAnimationTarget(e);const n={width:this.width,height:this.height},r={x:this.x,y:this.y};i=Math.abs(i)<.001?0:i;const o=uo(e.padding),s=this.title.length||this.beforeBody.length||this.body.length||this.afterBody.length||this.footer.length;e.enabled&&s&&(t.save(),t.globalAlpha=i,this.drawBackground(r,t,n,e),is(t,e.textDirection),r.y+=o.top,this.drawTitle(r,t,e),this.drawBody(r,t,e),this.drawFooter(r,t,e),ns(t,e.textDirection),t.restore())}getActiveElements(){return this._active||[]}setActiveElements(t,e){const i=this._active,n=t.map((({datasetIndex:t,index:e})=>{const i=this.chart.getDatasetMeta(t);if(!i)throw new Error("Cannot find a dataset at index "+t);return{datasetIndex:t,element:i.data[e],index:e}})),r=!nn(i,n),o=this._positionChanged(n,e);(r||o)&&(this._active=n,this._eventPosition=e,this._ignoreReplayEvents=!0,this.update(!0))}handleEvent(t,e,i=!0){if(e&&this._ignoreReplayEvents)return!1;this._ignoreReplayEvents=!1;const n=this.options,r=this._active||[],o=this._getActiveElements(t,r,e,i),s=this._positionChanged(o,t),a=e||!nn(o,r)||s;return a&&(this._active=o,(n.enabled||n.external)&&(this._eventPosition={x:t.x,y:t.y},this.update(!0,e))),a}_getActiveElements(t,e,i,n){const r=this.options;if("mouseout"===t.type)return[];if(!n)return e;const o=this.chart.getElementsAtEventForMode(t,r.mode,r,i);return r.reverse&&o.reverse(),o}_positionChanged(t,e){const{caretX:i,caretY:n,options:r}=this,o=mc[r.position].call(this,t,e);return!1!==o&&(i!==o.x||n!==o.y)}}Ec.positioners=mc;var Pc={id:"tooltip",_element:Ec,positioners:mc,afterInit(t,e,i){i&&(t.tooltip=new Ec({chart:t,options:i}))},beforeUpdate(t,e,i){t.tooltip&&t.tooltip.initialize(i)},reset(t,e,i){t.tooltip&&t.tooltip.initialize(i)},afterDraw(t){const e=t.tooltip;if(e&&e._willRender()){const i={tooltip:e};if(!1===t.notifyPlugins("beforeTooltipDraw",i))return;e.draw(t.ctx),t.notifyPlugins("afterTooltipDraw",i)}},afterEvent(t,e){if(t.tooltip){const i=e.replay;t.tooltip.handleEvent(e.event,i,e.inChartArea)&&(e.changed=!0)}},defaults:{enabled:!0,external:null,position:"average",backgroundColor:"rgba(0,0,0,0.8)",titleColor:"#fff",titleFont:{weight:"bold"},titleSpacing:2,titleMarginBottom:6,titleAlign:"left",bodyColor:"#fff",bodySpacing:2,bodyFont:{},bodyAlign:"left",footerColor:"#fff",footerSpacing:2,footerMarginTop:6,footerFont:{weight:"bold"},footerAlign:"left",padding:6,caretPadding:2,caretSize:5,cornerRadius:6,boxHeight:(t,e)=>e.bodyFont.size,boxWidth:(t,e)=>e.bodyFont.size,multiKeyBackground:"#fff",displayColors:!0,boxPadding:0,borderColor:"rgba(0,0,0,0)",borderWidth:0,animation:{duration:400,easing:"easeOutQuart"},animations:{numbers:{type:"number",properties:["x","y","width","height","caretX","caretY"]},opacity:{easing:"linear",duration:200}},callbacks:{beforeTitle:Ui,title(t){if(t.length>0){const e=t[0],i=e.chart.data.labels,n=i?i.length:0;if(this&&this.options&&"dataset"===this.options.mode)return e.dataset.label||"";if(e.label)return e.label;if(n>0&&e.dataIndex<n)return i[e.dataIndex]}return""},afterTitle:Ui,beforeBody:Ui,beforeLabel:Ui,label(t){if(this&&this.options&&"dataset"===this.options.mode)return t.label+": "+t.formattedValue||t.formattedValue;let e=t.dataset.label||"";e&&(e+=": ");const i=t.formattedValue;return Yi(i)||(e+=i),e},labelColor(t){const e=t.chart.getDatasetMeta(t.datasetIndex).controller.getStyle(t.dataIndex);return{borderColor:e.borderColor,backgroundColor:e.backgroundColor,borderWidth:e.borderWidth,borderDash:e.borderDash,borderDashOffset:e.borderDashOffset,borderRadius:0}},labelTextColor(){return this.options.bodyColor},labelPointStyle(t){const e=t.chart.getDatasetMeta(t.datasetIndex).controller.getStyle(t.dataIndex);return{pointStyle:e.pointStyle,rotation:e.rotation}},afterLabel:Ui,afterBody:Ui,beforeFooter:Ui,footer:Ui,afterFooter:Ui}},defaultRoutes:{bodyFont:"font",footerFont:"font",titleFont:"font"},descriptors:{_scriptable:t=>"filter"!==t&&"itemSort"!==t&&"external"!==t,_indexable:!1,callbacks:{_scriptable:!1,_indexable:!1},animation:{_fallback:!1},animations:{_fallback:"animation"}},additionalOptionScopes:["interaction"]},Tc=Object.freeze({__proto__:null,Decimation:Ul,Filler:lc,Legend:uc,SubTitle:gc,Title:fc,Tooltip:Pc});function Lc(t,e,i,n){const r=t.indexOf(e);if(-1===r)return((t,e,i,n)=>("string"==typeof e?(i=t.push(e)-1,n.unshift({index:i,label:e})):isNaN(e)&&(i=null),i))(t,e,i,n);return r!==t.lastIndexOf(e)?i:r}class Ac extends ea{constructor(t){super(t),this._startValue=void 0,this._valueRange=0,this._addedLabels=[]}init(t){const e=this._addedLabels;if(e.length){const t=this.getLabels();for(const{index:i,label:n}of e)t[i]===n&&t.splice(i,1);this._addedLabels=[]}super.init(t)}parse(t,e){if(Yi(t))return null;const i=this.getLabels();return((t,e)=>null===t?null:zn(Math.round(t),0,e))(e=isFinite(e)&&i[e]===t?e:Lc(i,t,Qi(e,t),this._addedLabels),i.length-1)}determineDataLimits(){const{minDefined:t,maxDefined:e}=this.getUserBounds();let{min:i,max:n}=this.getMinMax(!0);"ticks"===this.options.bounds&&(t||(i=0),e||(n=this.getLabels().length-1)),this.min=i,this.max=n}buildTicks(){const t=this.min,e=this.max,i=this.options.offset,n=[];let r=this.getLabels();r=0===t&&e===r.length-1?r:r.slice(t,e+1),this._valueRange=Math.max(r.length-(i?0:1),1),this._startValue=this.min-(i?.5:0);for(let i=t;i<=e;i++)n.push({value:i});return n}getLabelForValue(t){const e=this.getLabels();return t>=0&&t<e.length?e[t]:t}configure(){super.configure(),this.isHorizontal()||(this._reversePixels=!this._reversePixels)}getPixelForValue(t){return"number"!=typeof t&&(t=this.parse(t)),null===t?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getPixelForTick(t){const e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getValueForPixel(t){return Math.round(this._startValue+this.getDecimalForPixel(t)*this._valueRange)}getBasePixel(){return this.bottom}}function Cc(t,e){const i=[],{bounds:n,step:r,min:o,max:s,precision:a,count:l,maxTicks:c,maxDigits:h,includeBounds:u}=t,d=r||1,f=c-1,{min:p,max:g}=e,m=!Yi(o),b=!Yi(s),v=!Yi(l),y=(g-p)/(h+1);let x,_,w,k,O=Mn((g-p)/f/d)*d;if(O<1e-14&&!m&&!b)return[{value:p},{value:g}];k=Math.ceil(g/O)-Math.floor(p/O),k>f&&(O=Mn(k*O/f/d)*d),Yi(a)||(x=Math.pow(10,a),O=Math.ceil(O*x)/x),"ticks"===n?(_=Math.floor(p/O)*O,w=Math.ceil(g/O)*O):(_=p,w=g),m&&b&&r&&function(t,e){const i=Math.round(t);return i-e<=t&&i+e>=t}((s-o)/r,O/1e3)?(k=Math.round(Math.min((s-o)/O,c)),O=(s-o)/k,_=o,w=s):v?(_=m?o:_,w=b?s:w,k=l-1,O=(w-_)/k):(k=(w-_)/O,k=Pn(k,Math.round(k),O/1e3)?Math.round(k):Math.ceil(k));const S=Math.max(Cn(O),Cn(_));x=Math.pow(10,Yi(a)?S:a),_=Math.round(_*x)/x,w=Math.round(w*x)/x;let M=0;for(m&&(u&&_!==o?(i.push({value:o}),_<o&&M++,Pn(Math.round((_+M*O)*x)/x,o,Dc(o,y,t))&&M++):_<o&&M++);M<k;++M)i.push({value:Math.round((_+M*O)*x)/x});return b&&u&&w!==s?i.length&&Pn(i[i.length-1].value,s,Dc(s,y,t))?i[i.length-1].value=s:i.push({value:s}):b&&w!==s||i.push({value:w}),i}function Dc(t,e,{horizontal:i,minRotation:n}){const r=Ln(n),o=(i?Math.sin(r):Math.cos(r))||.001,s=.75*e*(""+t).length;return Math.min(e/o,s)}Ac.id="category",Ac.defaults={ticks:{callback:Ac.prototype.getLabelForValue}};class jc extends ea{constructor(t){super(t),this.start=void 0,this.end=void 0,this._startValue=void 0,this._endValue=void 0,this._valueRange=0}parse(t,e){return Yi(t)||("number"==typeof t||t instanceof Number)&&!isFinite(+t)?null:+t}handleTickRangeOptions(){const{beginAtZero:t}=this.options,{minDefined:e,maxDefined:i}=this.getUserBounds();let{min:n,max:r}=this;const o=t=>n=e?n:t,s=t=>r=i?r:t;if(t){const t=Sn(n),e=Sn(r);t<0&&e<0?s(0):t>0&&e>0&&o(0)}if(n===r){let e=1;(r>=Number.MAX_SAFE_INTEGER||n<=Number.MIN_SAFE_INTEGER)&&(e=Math.abs(.05*r)),s(r+e),t||o(n-e)}this.min=n,this.max=r}getTickLimit(){const t=this.options.ticks;let e,{maxTicksLimit:i,stepSize:n}=t;return n?(e=Math.ceil(this.max/n)-Math.floor(this.min/n)+1,e>1e3&&(console.warn(`scales.${this.id}.ticks.stepSize: ${n} would result generating up to ${e} ticks. Limiting to 1000.`),e=1e3)):(e=this.computeTickLimit(),i=i||11),i&&(e=Math.min(i,e)),e}computeTickLimit(){return Number.POSITIVE_INFINITY}buildTicks(){const t=this.options,e=t.ticks;let i=this.getTickLimit();i=Math.max(2,i);const n=Cc({maxTicks:i,bounds:t.bounds,min:t.min,max:t.max,precision:e.precision,step:e.stepSize,count:e.count,maxDigits:this._maxDigits(),horizontal:this.isHorizontal(),minRotation:e.minRotation||0,includeBounds:!1!==e.includeBounds},this._range||this);return"ticks"===t.bounds&&Tn(n,this,"value"),t.reverse?(n.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),n}configure(){const t=this.ticks;let e=this.min,i=this.max;if(super.configure(),this.options.offset&&t.length){const n=(i-e)/Math.max(t.length-1,1)/2;e-=n,i+=n}this._startValue=e,this._endValue=i,this._valueRange=i-e}getLabelForValue(t){return ts(t,this.chart.options.locale,this.options.ticks.format)}}class Ic extends jc{determineDataLimits(){const{min:t,max:e}=this.getMinMax(!0);this.min=Ki(t)?t:0,this.max=Ki(e)?e:1,this.handleTickRangeOptions()}computeTickLimit(){const t=this.isHorizontal(),e=t?this.width:this.height,i=Ln(this.options.ticks.minRotation),n=(t?Math.sin(i):Math.cos(i))||.001,r=this._resolveTickFontOptions(0);return Math.ceil(e/Math.min(40,r.lineHeight/n))}getPixelForValue(t){return null===t?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getValueForPixel(t){return this._startValue+this.getDecimalForPixel(t)*this._valueRange}}function Rc(t){return 1===t/Math.pow(10,Math.floor(On(t)))}Ic.id="linear",Ic.defaults={ticks:{callback:qs.formatters.numeric}};class Fc extends ea{constructor(t){super(t),this.start=void 0,this.end=void 0,this._startValue=void 0,this._valueRange=0}parse(t,e){const i=jc.prototype.parse.apply(this,[t,e]);if(0!==i)return Ki(i)&&i>0?i:null;this._zero=!0}determineDataLimits(){const{min:t,max:e}=this.getMinMax(!0);this.min=Ki(t)?Math.max(0,t):null,this.max=Ki(e)?Math.max(0,e):null,this.options.beginAtZero&&(this._zero=!0),this.handleTickRangeOptions()}handleTickRangeOptions(){const{minDefined:t,maxDefined:e}=this.getUserBounds();let i=this.min,n=this.max;const r=e=>i=t?i:e,o=t=>n=e?n:t,s=(t,e)=>Math.pow(10,Math.floor(On(t))+e);i===n&&(i<=0?(r(1),o(10)):(r(s(i,-1)),o(s(n,1)))),i<=0&&r(s(n,-1)),n<=0&&o(s(i,1)),this._zero&&this.min!==this._suggestedMin&&i===s(this.min,0)&&r(s(i,-1)),this.min=i,this.max=n}buildTicks(){const t=this.options,e=function(t,e){const i=Math.floor(On(e.max)),n=Math.ceil(e.max/Math.pow(10,i)),r=[];let o=Ji(t.min,Math.pow(10,Math.floor(On(e.min)))),s=Math.floor(On(o)),a=Math.floor(o/Math.pow(10,s)),l=s<0?Math.pow(10,Math.abs(s)):1;do{r.push({value:o,major:Rc(o)}),++a,10===a&&(a=1,++s,l=s>=0?1:l),o=Math.round(a*Math.pow(10,s)*l)/l}while(s<i||s===i&&a<n);const c=Ji(t.max,o);return r.push({value:c,major:Rc(o)}),r}({min:this._userMin,max:this._userMax},this);return"ticks"===t.bounds&&Tn(e,this,"value"),t.reverse?(e.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),e}getLabelForValue(t){return void 0===t?"0":ts(t,this.chart.options.locale,this.options.ticks.format)}configure(){const t=this.min;super.configure(),this._startValue=On(t),this._valueRange=On(this.max)-On(t)}getPixelForValue(t){return void 0!==t&&0!==t||(t=this.min),null===t||isNaN(t)?NaN:this.getPixelForDecimal(t===this.min?0:(On(t)-this._startValue)/this._valueRange)}getValueForPixel(t){const e=this.getDecimalForPixel(t);return Math.pow(10,this._startValue+e*this._valueRange)}}function zc(t){const e=t.ticks;if(e.display&&t.display){const t=uo(e.backdropPadding);return Qi(e.font&&e.font.size,Hr.font.size)+t.height}return 0}function Nc(t,e,i,n,r){return t===n||t===r?{start:e-i/2,end:e+i/2}:t<n||t>r?{start:e-i,end:e}:{start:e,end:e+i}}function Bc(t){const e={l:t.left+t._padding.left,r:t.right-t._padding.right,t:t.top+t._padding.top,b:t.bottom-t._padding.bottom},i=Object.assign({},e),n=[],r=[],o=t._pointLabels.length,s=t.options.pointLabels,a=s.centerPointLabels?mn/o:0;for(let u=0;u<o;u++){const o=s.setContext(t.getPointLabelContext(u));r[u]=o.padding;const d=t.getPointPosition(u,t.drawingArea+r[u],a),f=fo(o.font),p=(l=t.ctx,c=f,h=Xi(h=t._pointLabels[u])?h:[h],{w:Ur(l,c.string,h),h:h.length*c.lineHeight});n[u]=p;const g=Rn(t.getIndexAngle(u)+a),m=Math.round(An(g));Wc(i,e,g,Nc(m,d.x,p.w,0,180),Nc(m,d.y,p.h,90,270))}var l,c,h;t.setCenterPoint(e.l-i.l,i.r-e.r,e.t-i.t,i.b-e.b),t._pointLabelItems=function(t,e,i){const n=[],r=t._pointLabels.length,o=t.options,s=zc(o)/2,a=t.drawingArea,l=o.pointLabels.centerPointLabels?mn/r:0;for(let o=0;o<r;o++){const r=t.getPointPosition(o,a+s+i[o],l),c=Math.round(An(Rn(r.angle+_n))),h=e[o],u=$c(r.y,h.h,c),d=Vc(c),f=Hc(r.x,h.w,d);n.push({x:r.x,y:u,textAlign:d,left:f,top:u,right:f+h.w,bottom:u+h.h})}return n}(t,n,r)}function Wc(t,e,i,n,r){const o=Math.abs(Math.sin(i)),s=Math.abs(Math.cos(i));let a=0,l=0;n.start<e.l?(a=(e.l-n.start)/o,t.l=Math.min(t.l,e.l-a)):n.end>e.r&&(a=(n.end-e.r)/o,t.r=Math.max(t.r,e.r+a)),r.start<e.t?(l=(e.t-r.start)/s,t.t=Math.min(t.t,e.t-l)):r.end>e.b&&(l=(r.end-e.b)/s,t.b=Math.max(t.b,e.b+l))}function Vc(t){return 0===t||180===t?"center":t<180?"left":"right"}function Hc(t,e,i){return"right"===i?t-=e:"center"===i&&(t-=e/2),t}function $c(t,e,i){return 90===i||270===i?t-=e/2:(i>270||i<90)&&(t-=e),t}function Uc(t,e,i,n){const{ctx:r}=t;if(i)r.arc(t.xCenter,t.yCenter,e,0,bn);else{let i=t.getPointPosition(0,e);r.moveTo(i.x,i.y);for(let o=1;o<n;o++)i=t.getPointPosition(o,e),r.lineTo(i.x,i.y)}}Fc.id="logarithmic",Fc.defaults={ticks:{callback:qs.formatters.logarithmic,major:{enabled:!0}}};class qc extends jc{constructor(t){super(t),this.xCenter=void 0,this.yCenter=void 0,this.drawingArea=void 0,this._pointLabels=[],this._pointLabelItems=[]}setDimensions(){const t=this._padding=uo(zc(this.options)/2),e=this.width=this.maxWidth-t.width,i=this.height=this.maxHeight-t.height;this.xCenter=Math.floor(this.left+e/2+t.left),this.yCenter=Math.floor(this.top+i/2+t.top),this.drawingArea=Math.floor(Math.min(e,i)/2)}determineDataLimits(){const{min:t,max:e}=this.getMinMax(!1);this.min=Ki(t)&&!isNaN(t)?t:0,this.max=Ki(e)&&!isNaN(e)?e:0,this.handleTickRangeOptions()}computeTickLimit(){return Math.ceil(this.drawingArea/zc(this.options))}generateTickLabels(t){jc.prototype.generateTickLabels.call(this,t),this._pointLabels=this.getLabels().map(((t,e)=>{const i=tn(this.options.pointLabels.callback,[t,e],this);return i||0===i?i:""})).filter(((t,e)=>this.chart.getDataVisibility(e)))}fit(){const t=this.options;t.display&&t.pointLabels.display?Bc(this):this.setCenterPoint(0,0,0,0)}setCenterPoint(t,e,i,n){this.xCenter+=Math.floor((t-e)/2),this.yCenter+=Math.floor((i-n)/2),this.drawingArea-=Math.min(this.drawingArea/2,Math.max(t,e,i,n))}getIndexAngle(t){return Rn(t*(bn/(this._pointLabels.length||1))+Ln(this.options.startAngle||0))}getDistanceFromCenterForValue(t){if(Yi(t))return NaN;const e=this.drawingArea/(this.max-this.min);return this.options.reverse?(this.max-t)*e:(t-this.min)*e}getValueForDistanceFromCenter(t){if(Yi(t))return NaN;const e=t/(this.drawingArea/(this.max-this.min));return this.options.reverse?this.max-e:this.min+e}getPointLabelContext(t){const e=this._pointLabels||[];if(t>=0&&t<e.length){const i=e[t];return function(t,e,i){return go(t,{label:i,index:e,type:"pointLabel"})}(this.getContext(),t,i)}}getPointPosition(t,e,i=0){const n=this.getIndexAngle(t)-_n+i;return{x:Math.cos(n)*e+this.xCenter,y:Math.sin(n)*e+this.yCenter,angle:n}}getPointPositionForValue(t,e){return this.getPointPosition(t,this.getDistanceFromCenterForValue(e))}getBasePosition(t){return this.getPointPositionForValue(t||0,this.getBaseValue())}getPointLabelPosition(t){const{left:e,top:i,right:n,bottom:r}=this._pointLabelItems[t];return{left:e,top:i,right:n,bottom:r}}drawBackground(){const{backgroundColor:t,grid:{circular:e}}=this.options;if(t){const i=this.ctx;i.save(),i.beginPath(),Uc(this,this.getDistanceFromCenterForValue(this._endValue),e,this._pointLabels.length),i.closePath(),i.fillStyle=t,i.fill(),i.restore()}}drawGrid(){const t=this.ctx,e=this.options,{angleLines:i,grid:n}=e,r=this._pointLabels.length;let o,s,a;if(e.pointLabels.display&&function(t,e){const{ctx:i,options:{pointLabels:n}}=t;for(let r=e-1;r>=0;r--){const e=n.setContext(t.getPointLabelContext(r)),o=fo(e.font),{x:s,y:a,textAlign:l,left:c,top:h,right:u,bottom:d}=t._pointLabelItems[r],{backdropColor:f}=e;if(!Yi(f)){const t=ho(e.borderRadius),n=uo(e.backdropPadding);i.fillStyle=f;const r=c-n.left,o=h-n.top,s=u-c+n.width,a=d-h+n.height;Object.values(t).some((t=>0!==t))?(i.beginPath(),no(i,{x:r,y:o,w:s,h:a,radius:t}),i.fill()):i.fillRect(r,o,s,a)}eo(i,t._pointLabels[r],s,a+o.lineHeight/2,o,{color:e.color,textAlign:l,textBaseline:"middle"})}}(this,r),n.display&&this.ticks.forEach(((t,e)=>{if(0!==e){s=this.getDistanceFromCenterForValue(t.value);!function(t,e,i,n){const r=t.ctx,o=e.circular,{color:s,lineWidth:a}=e;!o&&!n||!s||!a||i<0||(r.save(),r.strokeStyle=s,r.lineWidth=a,r.setLineDash(e.borderDash),r.lineDashOffset=e.borderDashOffset,r.beginPath(),Uc(t,i,o,n),r.closePath(),r.stroke(),r.restore())}(this,n.setContext(this.getContext(e-1)),s,r)}})),i.display){for(t.save(),o=r-1;o>=0;o--){const n=i.setContext(this.getPointLabelContext(o)),{color:r,lineWidth:l}=n;l&&r&&(t.lineWidth=l,t.strokeStyle=r,t.setLineDash(n.borderDash),t.lineDashOffset=n.borderDashOffset,s=this.getDistanceFromCenterForValue(e.ticks.reverse?this.min:this.max),a=this.getPointPosition(o,s),t.beginPath(),t.moveTo(this.xCenter,this.yCenter),t.lineTo(a.x,a.y),t.stroke())}t.restore()}}drawBorder(){}drawLabels(){const t=this.ctx,e=this.options,i=e.ticks;if(!i.display)return;const n=this.getIndexAngle(0);let r,o;t.save(),t.translate(this.xCenter,this.yCenter),t.rotate(n),t.textAlign="center",t.textBaseline="middle",this.ticks.forEach(((n,s)=>{if(0===s&&!e.reverse)return;const a=i.setContext(this.getContext(s)),l=fo(a.font);if(r=this.getDistanceFromCenterForValue(this.ticks[s].value),a.showLabelBackdrop){t.font=l.string,o=t.measureText(n.label).width,t.fillStyle=a.backdropColor;const e=uo(a.backdropPadding);t.fillRect(-o/2-e.left,-r-l.size/2-e.top,o+e.width,l.size+e.height)}eo(t,n.label,0,-r,l,{color:a.color})})),t.restore()}drawTitle(){}}qc.id="radialLinear",qc.defaults={display:!0,animate:!0,position:"chartArea",angleLines:{display:!0,lineWidth:1,borderDash:[],borderDashOffset:0},grid:{circular:!1},startAngle:0,ticks:{showLabelBackdrop:!0,callback:qs.formatters.numeric},pointLabels:{backdropColor:void 0,backdropPadding:2,display:!0,font:{size:10},callback:t=>t,padding:5,centerPointLabels:!1}},qc.defaultRoutes={"angleLines.color":"borderColor","pointLabels.color":"color","ticks.color":"color"},qc.descriptors={angleLines:{_fallback:"grid"}};const Yc={millisecond:{common:!0,size:1,steps:1e3},second:{common:!0,size:1e3,steps:60},minute:{common:!0,size:6e4,steps:60},hour:{common:!0,size:36e5,steps:24},day:{common:!0,size:864e5,steps:30},week:{common:!1,size:6048e5,steps:4},month:{common:!0,size:2628e6,steps:12},quarter:{common:!1,size:7884e6,steps:4},year:{common:!0,size:3154e7}},Xc=Object.keys(Yc);function Gc(t,e){return t-e}function Kc(t,e){if(Yi(e))return null;const i=t._adapter,{parser:n,round:r,isoWeekday:o}=t._parseOpts;let s=e;return"function"==typeof n&&(s=n(s)),Ki(s)||(s="string"==typeof n?i.parse(s,n):i.parse(s)),null===s?null:(r&&(s="week"!==r||!En(o)&&!0!==o?i.startOf(s,r):i.startOf(s,"isoWeek",o)),+s)}function Jc(t,e,i,n){const r=Xc.length;for(let o=Xc.indexOf(t);o<r-1;++o){const t=Yc[Xc[o]],r=t.steps?t.steps:Number.MAX_SAFE_INTEGER;if(t.common&&Math.ceil((i-e)/(r*t.size))<=n)return Xc[o]}return Xc[r-1]}function Qc(t,e,i){if(i){if(i.length){const{lo:n,hi:r}=Bn(i,e);t[i[n]>=e?i[n]:i[r]]=!0}}else t[e]=!0}function Zc(t,e,i){const n=[],r={},o=e.length;let s,a;for(s=0;s<o;++s)a=e[s],r[a]=s,n.push({value:a,major:!1});return 0!==o&&i?function(t,e,i,n){const r=t._adapter,o=+r.startOf(e[0].value,n),s=e[e.length-1].value;let a,l;for(a=o;a<=s;a=+r.add(a,1,n))l=i[a],l>=0&&(e[l].major=!0);return e}(t,n,r,i):n}class th extends ea{constructor(t){super(t),this._cache={data:[],labels:[],all:[]},this._unit="day",this._majorUnit=void 0,this._offsets={},this._normalized=!1,this._parseOpts=void 0}init(t,e){const i=t.time||(t.time={}),n=this._adapter=new la._date(t.adapters.date);n.init(e),ln(i.displayFormats,n.formats()),this._parseOpts={parser:i.parser,round:i.round,isoWeekday:i.isoWeekday},super.init(t),this._normalized=e.normalized}parse(t,e){return void 0===t?null:Kc(this,t)}beforeLayout(){super.beforeLayout(),this._cache={data:[],labels:[],all:[]}}determineDataLimits(){const t=this.options,e=this._adapter,i=t.time.unit||"day";let{min:n,max:r,minDefined:o,maxDefined:s}=this.getUserBounds();function a(t){o||isNaN(t.min)||(n=Math.min(n,t.min)),s||isNaN(t.max)||(r=Math.max(r,t.max))}o&&s||(a(this._getLabelBounds()),"ticks"===t.bounds&&"labels"===t.ticks.source||a(this.getMinMax(!1))),n=Ki(n)&&!isNaN(n)?n:+e.startOf(Date.now(),i),r=Ki(r)&&!isNaN(r)?r:+e.endOf(Date.now(),i)+1,this.min=Math.min(n,r-1),this.max=Math.max(n+1,r)}_getLabelBounds(){const t=this.getLabelTimestamps();let e=Number.POSITIVE_INFINITY,i=Number.NEGATIVE_INFINITY;return t.length&&(e=t[0],i=t[t.length-1]),{min:e,max:i}}buildTicks(){const t=this.options,e=t.time,i=t.ticks,n="labels"===i.source?this.getLabelTimestamps():this._generate();"ticks"===t.bounds&&n.length&&(this.min=this._userMin||n[0],this.max=this._userMax||n[n.length-1]);const r=this.min,o=function(t,e,i){let n=0,r=t.length;for(;n<r&&t[n]<e;)n++;for(;r>n&&t[r-1]>i;)r--;return n>0||r<t.length?t.slice(n,r):t}(n,r,this.max);return this._unit=e.unit||(i.autoSkip?Jc(e.minUnit,this.min,this.max,this._getLabelCapacity(r)):function(t,e,i,n,r){for(let o=Xc.length-1;o>=Xc.indexOf(i);o--){const i=Xc[o];if(Yc[i].common&&t._adapter.diff(r,n,i)>=e-1)return i}return Xc[i?Xc.indexOf(i):0]}(this,o.length,e.minUnit,this.min,this.max)),this._majorUnit=i.major.enabled&&"year"!==this._unit?function(t){for(let e=Xc.indexOf(t)+1,i=Xc.length;e<i;++e)if(Yc[Xc[e]].common)return Xc[e]}(this._unit):void 0,this.initOffsets(n),t.reverse&&o.reverse(),Zc(this,o,this._majorUnit)}afterAutoSkip(){this.options.offsetAfterAutoskip&&this.initOffsets(this.ticks.map((t=>+t.value)))}initOffsets(t){let e,i,n=0,r=0;this.options.offset&&t.length&&(e=this.getDecimalForValue(t[0]),n=1===t.length?1-e:(this.getDecimalForValue(t[1])-e)/2,i=this.getDecimalForValue(t[t.length-1]),r=1===t.length?i:(i-this.getDecimalForValue(t[t.length-2]))/2);const o=t.length<3?.5:.25;n=zn(n,0,o),r=zn(r,0,o),this._offsets={start:n,end:r,factor:1/(n+1+r)}}_generate(){const t=this._adapter,e=this.min,i=this.max,n=this.options,r=n.time,o=r.unit||Jc(r.minUnit,e,i,this._getLabelCapacity(e)),s=Qi(r.stepSize,1),a="week"===o&&r.isoWeekday,l=En(a)||!0===a,c={};let h,u,d=e;if(l&&(d=+t.startOf(d,"isoWeek",a)),d=+t.startOf(d,l?"day":o),t.diff(i,e,o)>1e5*s)throw new Error(e+" and "+i+" are too far apart with stepSize of "+s+" "+o);const f="data"===n.ticks.source&&this.getDataTimestamps();for(h=d,u=0;h<i;h=+t.add(h,s,o),u++)Qc(c,h,f);return h!==i&&"ticks"!==n.bounds&&1!==u||Qc(c,h,f),Object.keys(c).sort(((t,e)=>t-e)).map((t=>+t))}getLabelForValue(t){const e=this._adapter,i=this.options.time;return i.tooltipFormat?e.format(t,i.tooltipFormat):e.format(t,i.displayFormats.datetime)}_tickFormatFunction(t,e,i,n){const r=this.options,o=r.time.displayFormats,s=this._unit,a=this._majorUnit,l=s&&o[s],c=a&&o[a],h=i[e],u=a&&c&&h&&h.major,d=this._adapter.format(t,n||(u?c:l)),f=r.ticks.callback;return f?tn(f,[d,e,i],this):d}generateTickLabels(t){let e,i,n;for(e=0,i=t.length;e<i;++e)n=t[e],n.label=this._tickFormatFunction(n.value,e,t)}getDecimalForValue(t){return null===t?NaN:(t-this.min)/(this.max-this.min)}getPixelForValue(t){const e=this._offsets,i=this.getDecimalForValue(t);return this.getPixelForDecimal((e.start+i)*e.factor)}getValueForPixel(t){const e=this._offsets,i=this.getDecimalForPixel(t)/e.factor-e.end;return this.min+i*(this.max-this.min)}_getLabelSize(t){const e=this.options.ticks,i=this.ctx.measureText(t).width,n=Ln(this.isHorizontal()?e.maxRotation:e.minRotation),r=Math.cos(n),o=Math.sin(n),s=this._resolveTickFontOptions(0).size;return{w:i*r+s*o,h:i*o+s*r}}_getLabelCapacity(t){const e=this.options.time,i=e.displayFormats,n=i[e.unit]||i.millisecond,r=this._tickFormatFunction(t,0,Zc(this,[t],this._majorUnit),n),o=this._getLabelSize(r),s=Math.floor(this.isHorizontal()?this.width/o.w:this.height/o.h)-1;return s>0?s:1}getDataTimestamps(){let t,e,i=this._cache.data||[];if(i.length)return i;const n=this.getMatchingVisibleMetas();if(this._normalized&&n.length)return this._cache.data=n[0].controller.getAllParsedValues(this);for(t=0,e=n.length;t<e;++t)i=i.concat(n[t].controller.getAllParsedValues(this));return this._cache.data=this.normalize(i)}getLabelTimestamps(){const t=this._cache.labels||[];let e,i;if(t.length)return t;const n=this.getLabels();for(e=0,i=n.length;e<i;++e)t.push(Kc(this,n[e]));return this._cache.labels=this._normalized?t:this.normalize(t)}normalize(t){return Un(t.sort(Gc))}}function eh(t,e,i){let n,r,o,s,a=0,l=t.length-1;i?(e>=t[a].pos&&e<=t[l].pos&&({lo:a,hi:l}=Wn(t,"pos",e)),({pos:n,time:o}=t[a]),({pos:r,time:s}=t[l])):(e>=t[a].time&&e<=t[l].time&&({lo:a,hi:l}=Wn(t,"time",e)),({time:n,pos:o}=t[a]),({time:r,pos:s}=t[l]));const c=r-n;return c?o+(s-o)*(e-n)/c:o}th.id="time",th.defaults={bounds:"data",adapters:{},time:{parser:!1,unit:!1,round:!1,isoWeekday:!1,minUnit:"millisecond",displayFormats:{}},ticks:{source:"auto",major:{enabled:!1}}};class ih extends th{constructor(t){super(t),this._table=[],this._minPos=void 0,this._tableRange=void 0}initOffsets(){const t=this._getTimestampsForTable(),e=this._table=this.buildLookupTable(t);this._minPos=eh(e,this.min),this._tableRange=eh(e,this.max)-this._minPos,super.initOffsets(t)}buildLookupTable(t){const{min:e,max:i}=this,n=[],r=[];let o,s,a,l,c;for(o=0,s=t.length;o<s;++o)l=t[o],l>=e&&l<=i&&n.push(l);if(n.length<2)return[{time:e,pos:0},{time:i,pos:1}];for(o=0,s=n.length;o<s;++o)c=n[o+1],a=n[o-1],l=n[o],Math.round((c+a)/2)!==l&&r.push({time:l,pos:o/(s-1)});return r}_getTimestampsForTable(){let t=this._cache.all||[];if(t.length)return t;const e=this.getDataTimestamps(),i=this.getLabelTimestamps();return t=e.length&&i.length?this.normalize(e.concat(i)):e.length?e:i,t=this._cache.all=t,t}getDecimalForValue(t){return(eh(this._table,t)-this._minPos)/this._tableRange}getValueForPixel(t){const e=this._offsets,i=this.getDecimalForPixel(t)/e.factor-e.end;return eh(this._table,i*this._tableRange+this._minPos,!0)}}ih.id="timeseries",ih.defaults=th.defaults;const nh=[oa,Vl,Tc,Object.freeze({__proto__:null,CategoryScale:Ac,LinearScale:Ic,LogarithmicScale:Fc,RadialLinearScale:qc,TimeScale:th,TimeSeriesScale:ih})];gl.register(...nh);var rh=gl,oh=i(998),sh=i.n(oh);const ah={data:{},context:null,init(t){this.context=t;const e=t.querySelectorAll("[data-progress]"),i=t.querySelectorAll("[data-chart]");[...e].forEach((t=>{t.dataset.url&&(this.data[t.dataset.url]||(this.data[t.dataset.url]={items:[],poll:null}),this.data[t.dataset.url].items.push(t)),"line"===t.dataset.progress?this.line(t):"circle"===t.dataset.progress&&this.circle(t)}));for(const t in this.data)this.getValues(t);[...i].forEach((t=>{const e={labels:JSON.parse(t.dataset.dates),datasets:[{backgroundColor:t.dataset.color,borderColor:t.dataset.color,data:JSON.parse(t.dataset.data),cubicInterpolationMode:"monotone"}]};new rh(t,{type:"line",data:e,options:{responsive:!0,radius:0,interaction:{intersect:!1},plugins:{legend:{display:!1}},scales:{y:{suggestedMin:0,ticks:{color:"#999999",callback:(t,e)=>sh()(t,{decimals:2,scale:"SI"})},grid:{color:"#d3dce3"}},x:{ticks:{color:"#999999"},grid:{color:"#d3dce3"}}}}})}))},line(t){new($i().Line)(t,{strokeWidth:2,easing:"easeInOut",duration:1400,color:t.dataset.color,trailColor:"#d3dce3",trailWidth:2,svgStyle:{width:"100%",height:"100%",display:"block"}}).animate(t.dataset.value/100)},circle(t){t.dataset.basetext=t.dataset.text,t.dataset.text="";const e=t.dataset.value,i=this;if(t.bar=new($i().Circle)(t,{strokeWidth:3,easing:"easeInOut",duration:1400,color:t.dataset.color,trailColor:"#d3dce3",trailWidth:3,svgStyle:null,text:{autoStyleContainer:!1,style:{color:"#222222"}},step(e,n){const r=Math.floor(100*n.value());i.setText(n,parseFloat(r),t.dataset.text)}}),!t.dataset.url){const i=e/100;t.bar.animate(i)}},getValues(t){this.data[t].poll&&(clearTimeout(this.data[t].poll),this.data[t].poll=null),Et({path:t,method:"GET"}).then((e=>{this.data[t].items.forEach((i=>{void 0!==e[i.dataset.basetext]?i.dataset.text=e[i.dataset.basetext]:i.dataset.text=i.dataset.basetext,i.bar.animate(e[i.dataset.value]),i.dataset.poll&&!this.data[t].poll&&(this.data[t].poll=setTimeout((()=>{this.getValues(t)}),1e4))}));for(const t in e){const i=this.context.querySelectorAll(`[data-key="${t}"]`),n=this.context.querySelectorAll(`[data-text="${t}"]`);i.forEach((i=>{i.dataset.value=e[t],i.dispatchEvent(new Event("focus"))})),n.forEach((i=>{i.innerText=e[t],i.classList.contains("cld-toggle")&&(e[t]?i.classList.remove("hidden"):i.classList.add("hidden"))}))}}))},setText(t,e,i){if(!t)return;const n=document.createElement("span"),r=document.createElement("h2"),o=document.createTextNode(i);r.innerText=e+"%",n.appendChild(r),n.appendChild(o),t.setText(n)}};var lh=ah;var ch={key:"_cld_pending_state",data:null,pending:null,changed:!1,previous:{},init(){this.data=cldData.stateData?cldData.stateData:{};let t=localStorage.getItem(this.key);t&&(t=JSON.parse(t),this.data={...this.data,...t},this.sendStates()),this.previous=JSON.stringify(this.data)},_update(){this.pending&&(clearTimeout(this.pending),localStorage.removeItem(this.key)),this.previous!==JSON.stringify(this.data)&&(this.pending=setTimeout((()=>this.sendStates()),2e3),localStorage.setItem(this.key,JSON.stringify(this.data)))},set(t,e){this.data[t]&&this.data[t]===e||(this.data[t]=e,this._update())},get(t){let e=null;return this.data[t]&&(e=this.data[t]),e},sendStates(){fetch(cldData.stateURL,{method:"POST",headers:{"Content-Type":"application/json","X-WP-Nonce":cldData.stateNonce},body:JSON.stringify(this.data)}).then((t=>t.json())).then((t=>{t.success&&(this.previous=JSON.stringify(t.state),localStorage.removeItem(this.key))}))}};var hh={init(t){[...t.querySelectorAll("[data-remove]")].forEach((t=>{t.addEventListener("click",(e=>{if(t.dataset.message&&!confirm(t.dataset.message))return;const i=document.getElementById(t.dataset.remove);i.parentNode.removeChild(i)}))}))}};const uh={values:{},inputs:{},context:null,init(t){this.context=t;t.querySelectorAll("[data-tags]").forEach((t=>this.bind(t)))},bind(t){t.innerText=t.dataset.placeholder;const e=t.dataset.tags,i=document.getElementById(e),n=this.context.querySelectorAll(`[data-tags-delete="${e}"]`);this.values[e]=JSON.parse(i.value),this.inputs[e]=i,t.boundInput=e,t.boundDisplay=this.context.querySelector(`[data-tags-display="${e}"]`),t.boundDisplay.addEventListener("click",(e=>{t.focus()})),t.addEventListener("focus",(e=>{t.innerText=null})),t.addEventListener("blur",(e=>{3<t.innerText.length&&this.captureTag(t,t.innerText),t.innerText=t.dataset.placeholder})),t.addEventListener("keydown",(i=>{if("Tab"===i.key)3<t.innerText.length&&(i.preventDefault(),this.captureTag(t,t.innerText));else if("Escape"===i.key)t.blur();else if("Backspace"===i.key&&0===t.innerText.length){const i=t.boundDisplay.lastChild.previousSibling;i&&(i.parentNode.removeChild(i),this.values[e].pop(),this.updateInput(e))}})),t.addEventListener("keypress",(e=>{"Comma"!==e.code&&"Enter"!==e.code&&"Tab"!==e.code&&"Space"!==e.code||(e.preventDefault(),3<t.innerText.length&&this.captureTag(t,t.innerText))})),n.forEach((t=>{t.parentNode.control=t,t.parentNode.style.width=getComputedStyle(t.parentNode).width,t.addEventListener("click",(e=>{e.stopPropagation(),this.deleteTag(t)}))}))},deleteTag(t){const e=t.parentNode,i=e.dataset.inputId,n=this.values[i].indexOf(e.dataset.value);0<=n&&this.values[i].splice(n,1),e.style.width=0,e.style.opacity=0,e.style.padding=0,e.style.margin=0,setTimeout((()=>{e.parentNode.removeChild(e)}),500),this.updateInput(i)},captureTag(t,e){if(this[t.dataset.format]&&"string"!=typeof(e=this[t.dataset.format](e)))return t.classList.add("pulse"),void setTimeout((()=>{t.classList.remove("pulse")}),1e3);if(!this.validateUnique(t.boundDisplay,e)){const i=this.createTag(e);i.dataset.inputId=t.boundInput,this.values[t.boundInput].push(e),t.innerText=null,t.boundDisplay.insertBefore(i,t),i.style.width=getComputedStyle(i).width,i.style.opacity=1,this.updateInput(t.boundInput)}},createTag(t){const e=document.createElement("span"),i=document.createElement("span"),n=document.createElement("span");return e.classList.add("cld-input-tags-item"),i.classList.add("cld-input-tags-item-text"),n.className="cld-input-tags-item-delete dashicons dashicons-no-alt",n.addEventListener("click",(()=>this.deleteTag(n))),i.innerText=t,e.appendChild(i),e.appendChild(n),e.dataset.value=t,e.style.opacity=0,e.control=n,e},validateUnique(t,e){const i=t.querySelector(`[data-value="${e}"]`);let n=!1;return i&&(i.classList.remove("pulse"),i.classList.add("pulse"),setTimeout((()=>{i.classList.remove("pulse")}),500),n=!0),n},updateInput(t){this.inputs[t].value=JSON.stringify(this.values[t])},host(t){!1===/^(?:http:\/\/www\.|https:\/\/www\.|http:\/\/|https:\/\/)/.test(t)&&(t="https://"+t);let e="";try{e=new URL(t)}catch(t){return t}return decodeURIComponent(e.host)}};var dh=uh;var fh={suffixInputs:null,init(t){this.suffixInputs=t.querySelectorAll("[data-suffix]"),[...this.suffixInputs].forEach((t=>this.bindInput(t)))},bindInput(t){const e=document.getElementById(t.dataset.suffix),i=e.dataset.template.split("@value");this.setSuffix(e,i,t.value),t.addEventListener("change",(()=>this.setSuffix(e,i,t.value))),t.addEventListener("input",(()=>this.setSuffix(e,i,t.value)))},setSuffix(t,e,i){t.innerHTML="",t.classList.add("hidden"),-1===["none","off",""].indexOf(i)&&t.classList.remove("hidden");const n=document.createTextNode(e.join(i));t.appendChild(n)}};const ph={wrappers:null,frame:null,error:'data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg"><rect width="100%" height="100%" fill="rgba(0,0,0,0.1)"/><text x="50%" y="50%" fill="red" text-anchor="middle" dominant-baseline="middle">%26%23x26A0%3B︎</text></svg>',init(t){this.wrappers=t.querySelectorAll(".cld-size-items"),this.wrappers.forEach((t=>{const e=t.querySelectorAll(".cld-image-selector-item");e.forEach((i=>{i.addEventListener("click",(()=>{e.forEach((t=>{delete t.dataset.selected})),i.dataset.selected=!0,this.buildImages(t)}))})),this.buildImages(t)}))},buildImages(t){const e=t.dataset.base,i=t.querySelectorAll("img"),n=t.querySelector(".cld-image-selector-item[data-selected]").dataset.image;let r=null;i.forEach((i=>{const o=i.dataset.size,s=i.parentNode.querySelector(".regular-text"),a=i.parentNode.querySelector(".disable-toggle"),l=s.value.length?s.value.replace(" ",""):s.placeholder;a.checked?(s.disabled=!0,i.src=`${e}/${o}/${n}`):(s.disabled=!1,i.src=`${e}/${o},${l}/${n}`),i.bound||(s.addEventListener("input",(()=>{r&&clearTimeout(r),r=setTimeout((()=>{this.buildImages(t)}),1e3)})),a.addEventListener("change",(()=>{this.buildImages(t)})),i.addEventListener("error",(()=>{i.src=this.error})),i.bound=!0)}))}};var gh=ph;const mh={bindings:{},parent_check_data:{},check_parents:{},_init(t){const e=t.querySelectorAll("[data-condition]"),i=t.querySelectorAll("[data-toggle]"),n=t.querySelectorAll("[data-for]"),r=t.querySelectorAll("[data-tooltip]"),o=t.querySelectorAll("[data-bind-trigger]"),s=t.querySelectorAll("[data-main]"),a=t.querySelectorAll("[data-file]"),l=t.querySelectorAll("[data-auto-suffix]"),c=t.querySelectorAll("[data-confirm]"),h={};ch.init(),Vi.bind(s),l.forEach((t=>this._autoSuffix(t))),o.forEach((t=>this._trigger(t))),i.forEach((t=>this._toggle(t))),e.forEach((t=>this._bind(t))),n.forEach((t=>this._alias(t))),a.forEach((t=>this._files(t,h))),zi(r,{theme:"cloudinary",arrow:!1,placement:"bottom-start",aria:{content:"auto",expanded:"auto"},content:t=>document.getElementById(t.dataset.tooltip).innerHTML}),[...o].forEach((t=>{t.dispatchEvent(new Event("input"))})),c.forEach((t=>{t.addEventListener("click",(e=>{confirm(t.dataset.confirm)||(e.preventDefault(),e.stopPropagation())}))})),lh.init(t),hh.init(t),dh.init(t),fh.init(t),gh.init(t)},_autoSuffix(t){const e=t.dataset.autoSuffix;let i="";const n=[...e.split(";")].map((t=>0===t.indexOf("*")?(i=t.replace("*",""),i):t));t.addEventListener("change",(()=>{const e=t.value.replace(" ",""),r=e.replace(/[^0-9]/g,""),o=e.replace(/[0-9]/g,"").toLowerCase();r&&(-1===n.indexOf(o)?t.value=r+i:t.value=r+o)})),t.dispatchEvent(new Event("change"))},_files(t,e){const i=t.dataset.parent;i&&(this.check_parents[i]=document.getElementById(i),this.parent_check_data[i]||(this.parent_check_data[i]=this.check_parents[i].value?JSON.parse(this.check_parents[i].value):[]),t.addEventListener("change",(()=>{const n=this.parent_check_data[i].indexOf(t.value);t.checked?this.parent_check_data[i].push(t.value):this.parent_check_data[i].splice(n,1),e[i]&&clearTimeout(e[i]),e[i]=setTimeout((()=>{this._compileParent(i)}),10)})))},_compileParent(t){this.check_parents[t].value=JSON.stringify(this.parent_check_data[t]),this.check_parents[t].dispatchEvent(new Event("change"))},_bind(t){t.condition=JSON.parse(t.dataset.condition);for(const e in t.condition)this.bindings[e]&&this.bindings[e].elements.push(t)},_trigger(t){const e=t.dataset.bindTrigger,i=this;i.bindings[e]={input:t,value:t.value,checked:!0,elements:[]},t.addEventListener("change",(function(e){t.dispatchEvent(new Event("input"))})),t.addEventListener("input",(function(){if(i.bindings[e].value=t.value,"checkbox"===t.type&&(i.bindings[e].checked=t.checked),"radio"!==t.type||!1!==t.checked)for(const n in i.bindings[e].elements)i.toggle(i.bindings[e].elements[n],t)}))},_alias(t){t.addEventListener("click",(function(){document.getElementById(t.dataset.for).dispatchEvent(new Event("click"))}))},_toggle(t){const e=this,i=document.querySelector('[data-wrap="'+t.dataset.toggle+'"]');if(!i)return;const n=ch.get(t.id);t.addEventListener("click",(function(n){n.stopPropagation();const r=i.classList.contains("open")?"closed":"open";e.toggle(i,t,r)})),n!==t.dataset.state&&this.toggle(i,t,n)},toggle(t,e,i){if(!i){i="open";for(const e in t.condition){let n=this.bindings[e].value;const r=t.condition[e];"boolean"==typeof r&&(n=this.bindings[e].checked),r!==n&&(i="closed")}}"closed"===i?this.close(t,e):this.open(t,e),ch.set(e.id,i)},open(t,e){const i=t.getElementsByClassName("cld-ui-input");t.classList.remove("closed"),t.classList.add("open"),e&&e.classList.contains("dashicons")&&(e.classList.remove("dashicons-arrow-down-alt2"),e.classList.add("dashicons-arrow-up-alt2")),[...i].forEach((function(t){t.dataset.disabled=!1}))},close(t,e){const i=t.getElementsByClassName("cld-ui-input");t.classList.remove("open"),t.classList.add("closed"),e&&e.classList.contains("dashicons")&&(e.classList.remove("dashicons-arrow-up-alt2"),e.classList.add("dashicons-arrow-down-alt2")),[...i].forEach((function(t){t.dataset.disabled=!0}))}},bh=document.querySelectorAll(".cld-settings,.cld-meta-box");bh.length&&bh.forEach((t=>{t&&window.addEventListener("load",mh._init(t))}));const vh={storageKey:"_cld_wizard",testing:null,next:document.querySelector('[data-navigate="next"]'),back:document.querySelector('[data-navigate="back"]'),lock:document.getElementById("pad-lock"),lockIcon:document.getElementById("lock-icon"),options:document.querySelectorAll('.cld-ui-input[type="checkbox"]'),settings:document.getElementById("optimize"),tabBar:document.getElementById("wizard-tabs"),tracking:document.getElementById("tracking"),complete:document.getElementById("complete-wizard"),tabs:{"tab-1":document.getElementById("tab-icon-1"),"tab-2":document.getElementById("tab-icon-2"),"tab-3":document.getElementById("tab-icon-3")},content:{"tab-1":document.getElementById("tab-1"),"tab-2":document.getElementById("tab-2"),"tab-3":document.getElementById("tab-3"),"tab-4":document.getElementById("tab-4")},connection:{error:document.getElementById("connection-error"),success:document.getElementById("connection-success"),working:document.getElementById("connection-working")},debounceConnect:null,updateConnection:document.getElementById("update-connection"),cancelUpdateConnection:document.getElementById("cancel-update-connection"),config:{},didSave:!1,init(){if(!cldData.wizard)return;this.config=cldData.wizard.config,window.localStorage.getItem(this.storageKey)&&(this.config=JSON.parse(window.localStorage.getItem(this.storageKey))),document.location.hash.length&&this.hashChange(),Et.use(Et.createNonceMiddleware(cldData.wizard.saveNonce));const t=document.querySelectorAll("[data-navigate]"),e=document.getElementById("connect.cloudinary_url");this.updateConnection.addEventListener("click",(()=>{this.lockNext(),e.parentNode.classList.remove("hidden"),this.cancelUpdateConnection.classList.remove("hidden"),this.updateConnection.classList.add("hidden")})),this.cancelUpdateConnection.addEventListener("click",(()=>{this.unlockNext(),e.parentNode.classList.add("hidden"),this.cancelUpdateConnection.classList.add("hidden"),this.updateConnection.classList.remove("hidden"),this.config.cldString=!0,e.value="",this.connection.error.classList.remove("active"),this.connection.success.classList.add("active")})),[...t].forEach((t=>{t.addEventListener("click",(()=>{this.navigate(t.dataset.navigate)}))})),this.lock.addEventListener("click",(()=>{this.lockIcon.classList.toggle("dashicons-unlock"),this.settings.classList.toggle("disabled"),this.options.forEach((t=>{t.disabled=t.disabled?"":"disabled"}))})),e.addEventListener("input",(t=>{this.lockNext();const i=e.value.replace("CLOUDINARY_URL=","");this.connection.error.classList.remove("active"),this.connection.success.classList.remove("active"),this.connection.working.classList.remove("active"),i.length&&(this.testing=i,this.debounceConnect&&clearTimeout(this.debounceConnect),this.debounceConnect=setTimeout((()=>{this.evaluateConnectionString(i)?(this.connection.working.classList.add("active"),this.testConnection(i)):this.connection.error.classList.add("active")}),500))})),this.config.cldString&&(e.parentNode.classList.add("hidden"),this.updateConnection.classList.remove("hidden")),this.getTab(this.config.tab),this.initFeatures(),window.addEventListener("hashchange",(t=>{this.hashChange()}))},hashChange(){const t=parseInt(document.location.hash.replace("#",""));t&&0<t&&5>t&&this.getTab(t)},initFeatures(){const t=document.getElementById("media_library");t.checked=this.config.mediaLibrary,t.addEventListener("change",(()=>{this.setConfig("mediaLibrary",t.checked)}));const e=document.getElementById("non_media");e.checked=this.config.nonMedia,e.addEventListener("change",(()=>{this.setConfig("nonMedia",e.checked)}));const i=document.getElementById("advanced");i.checked=this.config.advanced,i.addEventListener("change",(()=>{this.setConfig("advanced",i.checked)}))},getCurrent(){return this.content[`tab-${this.config.tab}`]},hideTabs(){Object.keys(this.content).forEach((t=>{this.hide(this.content[t])}))},completeTab(t){this.incompleteTab(),Object.keys(this.tabs).forEach((e=>{const i=parseInt(this.tabs[e].dataset.tab);t>i?this.tabs[e].classList.add("complete"):t===i&&this.tabs[e].classList.add("active")}))},incompleteTab(t){Object.keys(this.tabs).forEach((t=>{this.tabs[t].classList.remove("complete","active")}))},getCurrentTab(){return this.tabs[`tab-icon-${this.config.tab}`]},getTab(t){if(4===t&&window.localStorage.getItem(this.storageKey)&&!this.didSave)return void this.saveConfig();const e=this.getCurrent(),i=document.getElementById(`tab-${t}`);switch(this.hideTabs(),this.completeTab(t),this.hide(document.getElementById(`tab-${this.config.tab}`)),e.classList.remove("active"),this.show(i),this.show(this.next),this.hide(this.lock),t){case 1:this.hide(this.back),this.unlockNext();break;case 2:this.show(this.back),this.config.cldString?this.showSuccess():(this.lockNext(),setTimeout((()=>{document.getElementById("connect.cloudinary_url").focus()}),0)),this.updateConnection.classList.contains("hidden")&&this.lockNext();break;case 3:if(!this.config.cldString)return void(document.location.hash="1");this.show(this.lock),this.show(this.back);break;case 4:if(!this.config.cldString)return void(document.location.hash="1");this.hide(this.tabBar),this.hide(this.next),this.hide(this.back)}this.setConfig("tab",t)},navigate(t){"next"===t?this.navigateNext():"back"===t&&this.navigateBack()},navigateBack(){document.location.hash=this.config.tab-1},navigateNext(){document.location.hash=this.config.tab+1},showError(){this.connection.error.classList.add("active"),this.connection.success.classList.remove("active")},showSuccess(){this.connection.error.classList.remove("active"),this.connection.success.classList.add("active")},show(t){t.classList.remove("hidden"),t.style.display=""},hide(t){t.classList.add("hidden"),t.style.display="none"},lockNext(){this.next.disabled="disabled"},unlockNext(){this.next.disabled=""},evaluateConnectionString:t=>new RegExp(/^(?:CLOUDINARY_URL=)?(cloudinary:\/\/){1}(\d*)[:]{1}([^@]*)[@]{1}([^@]*)$/gim).test(t),testConnection(t){Et({path:cldData.wizard.testURL,data:{cloudinary_url:t},method:"POST"}).then((e=>{e.url===this.testing&&(this.connection.working.classList.remove("active"),"connection_error"===e.type?this.showError():"connection_success"===e.type&&(this.showSuccess(),this.unlockNext(),this.setConfig("cldString",t)))}))},setConfig(t,e){this.config[t]=e,window.localStorage.setItem(this.storageKey,JSON.stringify(this.config))},saveConfig(){this.lockNext(),this.next.innerText=j("Setting up Cloudinary","cloudinary"),this.didSave=!0,Et({path:cldData.wizard.saveURL,data:this.config,method:"POST"}).then((t=>{this.next.innerText=j("Next","cloudinary"),this.unlockNext(),this.getTab(4),window.localStorage.removeItem(this.storageKey)})).fail((t=>{this.didSave=!1}))}};window.addEventListener("load",(()=>vh.init()));const yh={select:document.getElementById("connect.offload"),tooltip:null,descriptions:{},change(){[...this.descriptions].forEach((t=>{t.classList.remove("selected")})),this.tooltip.querySelector("."+this.select.value).classList.add("selected")},addEventListener(){this.select.addEventListener("change",this.change.bind(this))},_init(){this.select&&(this.addEventListener(),this.tooltip=this.select.parentNode.querySelector(".cld-tooltip"),this.descriptions=this.tooltip.querySelectorAll("li"),this.change())}};window.addEventListener("load",(()=>yh._init()));const xh={pageReloader:document.getElementById("page-reloader"),init(){if(!cldData.extensions)return;Et.use(Et.createNonceMiddleware(cldData.extensions.nonce));[...document.querySelectorAll("[data-extension]")].forEach((t=>{t.addEventListener("change",(e=>{t.spinner||(t.spinner=this.createSpinner(),t.parentNode.appendChild(t.spinner)),t.debounce&&clearTimeout(t.debounce),t.debounce=setTimeout((()=>{this.toggleExtension(t),t.debounce=null}),1e3)}))}))},toggleExtension(t){const e=t.dataset.extension,i=t.checked;Et({path:cldData.extensions.url,data:{extension:e,enabled:i},method:"POST"}).then((e=>{t.spinner&&(t.parentNode.removeChild(t.spinner),delete t.spinner),Object.keys(e).forEach((t=>{document.querySelectorAll(`[data-text="${t}"]`).forEach((i=>{i.innerText=e[t]}))})),this.pageReloader.style.display="block"}))},createSpinner(){const t=document.createElement("span");return t.classList.add("spinner"),t.classList.add("cld-extension-spinner"),t}};window.addEventListener("load",(()=>xh.init()));const _h={tabButtonSelectors:null,selectedTabID:"",deselectOldTab(){document.getElementById(this.selectedTabID).classList.remove("is-active"),this.filterActive([...this.tabButtonSelectors]).classList.remove("is-active")},selectCurrentTab(t){this.selectedTabID=t.dataset.tab,t.classList.add("is-active"),document.getElementById(this.selectedTabID).classList.add("is-active")},selectTab(t){t.preventDefault(),t.target.classList.contains("is-active")||(this.deselectOldTab(),this.selectCurrentTab(t.target))},filterTabs(){[...this.tabButtonSelectors].forEach((t=>{t.dataset.tab&&t.addEventListener("click",this.selectTab.bind(this))}))},filterActive:t=>t.filter((t=>t.classList.contains("is-active"))).pop(),init(){this.tabButtonSelectors=document.querySelectorAll(".cld-page-tabs-tab button"),0!==this.tabButtonSelectors.length&&(this.selectCurrentTab(this.filterActive([...this.tabButtonSelectors])),this.filterTabs())}};window.addEventListener("load",(()=>_h.init()));i.p,i.p,i.p,i.p,i.p,i.p,i.p,i.p,i.p,i.p,i.p,i.p,i.p,i.p,i.p,i.p,i.p,i.p,i.p,i.p,i.p,i.p,i.p,i.p,i.p,i.p,i.p,i.p;window.$=window.jQuery;e()}()}();