!function(){const t={deviceDensity:window.devicePixelRatio?window.devicePixelRatio:"auto",density:null,images:[],throttle:!1,config:CLDLB||{},lazyThreshold:0,_init(){this._calcThreshold(),[...document.images].forEach((t=>{if(!t.dataset.publicId)return;const i=t.dataset.size.split(" ");t.originalWidth=i[0],t.originalHeight=i[1],i[2]&&(t.crop=i[2]),this.images.push(t),t.addEventListener("error",(i=>{t.src='data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg"><rect width="100%" height="100%" fill="rgba(0,0,0,0.1)"/><text x="50%" y="50%" fill="red" text-anchor="middle" dominant-baseline="middle">%26%23x26A0%3B︎</text></svg>';const e=this.images.indexOf(t);this.images.splice(e,1)}))})),window.addEventListener("resize",(()=>{this._throttle(this._build.bind(this),100,!0)})),window.addEventListener("scroll",(()=>{this._throttle(this._build.bind(this),100,!1)})),setTimeout((()=>this._build()),0)},_calcThreshold(){const t=this.config.lazy_threshold.replace(/[^0-9]/g,"");let i=0;switch(this.config.lazy_threshold.replace(/[0-9]/g,"").toLowerCase()){case"em":i=parseFloat(getComputedStyle(document.body).fontSize)*t;break;case"rem":i=parseFloat(getComputedStyle(document.documentElement).fontSize)*t;break;case"vh":i=window.innerHeight/t*100;break;default:i=t}this.lazyThreshold=window.innerHeight+parseInt(i,10)},_getDensity(){if(this.density)return this.density;let t=this.config.dpr?this.config.dpr.replace("X",""):"off";if("off"===t)return this.density=1,1;let i=this.deviceDensity;return"max"!==t&&"auto"!==i&&(t=parseFloat(t),i=i>Math.ceil(t)?t:i),this.density=i,i},_throttle(t,i,e){this.throttle||setTimeout((()=>{t(e),this.throttle=!1}),i)},_build(t){this.images.forEach((i=>{!t&&i.cld_loaded||this.buildSize(i)}))},_shouldRebuild(t){const i=this.scaleWidth(t),e=t.getBoundingClientRect(),s="auto"!==this.density?this._getDensity():1;return e.top<this.lazyThreshold&&(i>t.naturalWidth/s||!t.cld_loaded)},_shouldPlacehold(t){const i=this.scaleWidth(t),e=t.getBoundingClientRect(),s="auto"!==this.density?this._getDensity():1;return this.config.placeholder&&!t.cld_loaded&&e.top<2*this.lazyThreshold&&(i>t.naturalWidth/s||!t.cld_placehold)},scaleWidth(t){const i=this.config.pixel_step,e=Math.floor((t.originalWidth-t.width)/i);let s=t.originalWidth-i*e;return s>t.originalWidth?s=t.originalWidth:this.config.max_width<s?s=this.config.max_width:this.config.min_width>s&&(s=this.config.min_width),s},scaleSize(t,i){const e=(t.originalWidth/t.originalHeight).toFixed(3),s=(t.width/t.height).toFixed(3),o=this.scaleWidth(t),h=[];t.width!==t.originalWidth&&h.push(e===s?"c_scale":"c_fill,g_auto");const a=Math.round(o/s);if(h.push("w_"+o),h.push("h_"+a),i){const t=this._getDensity();1!==t&&h.push("dpr_"+t)}return{transformation:h.join(","),nameExtension:o+"x"+a}},buildSize(t){this._shouldRebuild(t)?t.dataset.srcset?(t.cld_loaded=!0,t.srcset=t.dataset.srcset):t.src=this.getSizeURL(t):this._shouldPlacehold(t)&&(t.src=this.getPlaceholderURL(t))},getSizeURL(t){t.cld_loaded=!0;const i=this.scaleSize(t,!0),e="auto"!==this.config.image_format&&"none"!==this.config.image_format?this.config.image_format:t.dataset.format,s=t.dataset.publicId.split("/").pop();return[this.config.base_url,"images",i.transformation,t.dataset.transformations,t.dataset.publicId,s+"-"+i.nameExtension+"."+e+"?_i=AA"].filter(this.empty).join("/")},getPlaceholderURL(t){t.cld_placehold=!0;const i=this.scaleSize(t,!1);return[this.config.base_url,"images",i.transformation,this.config.placeholder,t.dataset.publicId,"placeholder"].filter(this.empty).join("/")},empty:t=>0!==t.length};window.addEventListener("load",(()=>{t._init()}))}();