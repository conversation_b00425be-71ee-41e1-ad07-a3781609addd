!function(){var t={616:function(t){t.exports=function(t,e){var n,r,i=0;function o(){var o,a,s=n,u=arguments.length;t:for(;s;){if(s.args.length===arguments.length){for(a=0;a<u;a++)if(s.args[a]!==arguments[a]){s=s.next;continue t}return s!==n&&(s===r&&(r=s.prev),s.prev.next=s.next,s.next&&(s.next.prev=s.prev),s.next=n,s.prev=null,n.prev=s,n=s),s.val}s=s.next}for(o=new Array(u),a=0;a<u;a++)o[a]=arguments[a];return s={args:o,val:t.apply(null,o)},n?(n.prev=s,s.next=n):r=s,i===e.maxSize?(r=r.prev).next=null:i++,n=s,s.val}return e=e||{},o.clear=function(){n=null,r=null,i=0},o}},604:function(t,e,n){var r;!function(){"use strict";var i={not_string:/[^s]/,not_bool:/[^t]/,not_type:/[^T]/,not_primitive:/[^v]/,number:/[diefg]/,numeric_arg:/[bcdiefguxX]/,json:/[j]/,not_json:/[^j]/,text:/^[^\x25]+/,modulo:/^\x25{2}/,placeholder:/^\x25(?:([1-9]\d*)\$|\(([^)]+)\))?(\+)?(0|'[^$])?(-)?(\d+)?(?:\.(\d+))?([b-gijostTuvxX])/,key:/^([a-z_][a-z_\d]*)/i,key_access:/^\.([a-z_][a-z_\d]*)/i,index_access:/^\[(\d+)\]/,sign:/^[+-]/};function o(t){return function(t,e){var n,r,a,s,u,c,l,p,f,d=1,h=t.length,v="";for(r=0;r<h;r++)if("string"==typeof t[r])v+=t[r];else if("object"==typeof t[r]){if((s=t[r]).keys)for(n=e[d],a=0;a<s.keys.length;a++){if(null==n)throw new Error(o('[sprintf] Cannot access property "%s" of undefined value "%s"',s.keys[a],s.keys[a-1]));n=n[s.keys[a]]}else n=s.param_no?e[s.param_no]:e[d++];if(i.not_type.test(s.type)&&i.not_primitive.test(s.type)&&n instanceof Function&&(n=n()),i.numeric_arg.test(s.type)&&"number"!=typeof n&&isNaN(n))throw new TypeError(o("[sprintf] expecting number but found %T",n));switch(i.number.test(s.type)&&(p=n>=0),s.type){case"b":n=parseInt(n,10).toString(2);break;case"c":n=String.fromCharCode(parseInt(n,10));break;case"d":case"i":n=parseInt(n,10);break;case"j":n=JSON.stringify(n,null,s.width?parseInt(s.width):0);break;case"e":n=s.precision?parseFloat(n).toExponential(s.precision):parseFloat(n).toExponential();break;case"f":n=s.precision?parseFloat(n).toFixed(s.precision):parseFloat(n);break;case"g":n=s.precision?String(Number(n.toPrecision(s.precision))):parseFloat(n);break;case"o":n=(parseInt(n,10)>>>0).toString(8);break;case"s":n=String(n),n=s.precision?n.substring(0,s.precision):n;break;case"t":n=String(!!n),n=s.precision?n.substring(0,s.precision):n;break;case"T":n=Object.prototype.toString.call(n).slice(8,-1).toLowerCase(),n=s.precision?n.substring(0,s.precision):n;break;case"u":n=parseInt(n,10)>>>0;break;case"v":n=n.valueOf(),n=s.precision?n.substring(0,s.precision):n;break;case"x":n=(parseInt(n,10)>>>0).toString(16);break;case"X":n=(parseInt(n,10)>>>0).toString(16).toUpperCase()}i.json.test(s.type)?v+=n:(!i.number.test(s.type)||p&&!s.sign?f="":(f=p?"+":"-",n=n.toString().replace(i.sign,"")),c=s.pad_char?"0"===s.pad_char?"0":s.pad_char.charAt(1):" ",l=s.width-(f+n).length,u=s.width&&l>0?c.repeat(l):"",v+=s.align?f+n+u:"0"===c?f+u+n:u+f+n)}return v}(function(t){if(s[t])return s[t];var e,n=t,r=[],o=0;for(;n;){if(null!==(e=i.text.exec(n)))r.push(e[0]);else if(null!==(e=i.modulo.exec(n)))r.push("%");else{if(null===(e=i.placeholder.exec(n)))throw new SyntaxError("[sprintf] unexpected placeholder");if(e[2]){o|=1;var a=[],u=e[2],c=[];if(null===(c=i.key.exec(u)))throw new SyntaxError("[sprintf] failed to parse named argument key");for(a.push(c[1]);""!==(u=u.substring(c[0].length));)if(null!==(c=i.key_access.exec(u)))a.push(c[1]);else{if(null===(c=i.index_access.exec(u)))throw new SyntaxError("[sprintf] failed to parse named argument key");a.push(c[1])}e[2]=a}else o|=2;if(3===o)throw new Error("[sprintf] mixing positional and named placeholders is not (yet) supported");r.push({placeholder:e[0],param_no:e[1],keys:e[2],sign:e[3],pad_char:e[4],align:e[5],width:e[6],precision:e[7],type:e[8]})}n=n.substring(e[0].length)}return s[t]=r}(t),arguments)}function a(t,e){return o.apply(null,[t].concat(e||[]))}var s=Object.create(null);o,a,"undefined"!=typeof window&&(window.sprintf=o,window.vsprintf=a,void 0===(r=function(){return{sprintf:o,vsprintf:a}}.call(e,n,e,t))||(t.exports=r))}()}},e={};function n(r){var i=e[r];if(void 0!==i)return i.exports;var o=e[r]={exports:{}};return t[r](o,o.exports,n),o.exports}n.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return n.d(e,{a:e}),e},n.d=function(t,e){for(var r in e)n.o(e,r)&&!n.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:e[r]})},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},function(){"use strict";var t,e,r,i,o=n(616),a=n.n(o);n(604),a()(console.error);function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}function u(t){var e=function(t,e){if("object"!=s(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=s(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==s(e)?e:e+""}t={"(":9,"!":8,"*":7,"/":7,"%":7,"+":6,"-":6,"<":5,"<=":5,">":5,">=":5,"==":4,"!=":4,"&&":3,"||":2,"?":1,"?:":1},e=["(","?"],r={")":["("],":":["?","?:"]},i=/<=|>=|==|!=|&&|\|\||\?:|\(|!|\*|\/|%|\+|-|<|>|\?|\)|:/;var c={"!":function(t){return!t},"*":function(t,e){return t*e},"/":function(t,e){return t/e},"%":function(t,e){return t%e},"+":function(t,e){return t+e},"-":function(t,e){return t-e},"<":function(t,e){return t<e},"<=":function(t,e){return t<=e},">":function(t,e){return t>e},">=":function(t,e){return t>=e},"==":function(t,e){return t===e},"!=":function(t,e){return t!==e},"&&":function(t,e){return t&&e},"||":function(t,e){return t||e},"?:":function(t,e,n){if(t)throw e;return n}};function l(n){var o=function(n){for(var o,a,s,u,c=[],l=[];o=n.match(i);){for(a=o[0],(s=n.substr(0,o.index).trim())&&c.push(s);u=l.pop();){if(r[a]){if(r[a][0]===u){a=r[a][1]||a;break}}else if(e.indexOf(u)>=0||t[u]<t[a]){l.push(u);break}c.push(u)}r[a]||l.push(a),n=n.substr(o.index+a.length)}return(n=n.trim())&&c.push(n),c.concat(l.reverse())}(n);return function(t){return function(t,e){var n,r,i,o,a,s,u=[];for(n=0;n<t.length;n++){if(a=t[n],o=c[a]){for(r=o.length,i=Array(r);r--;)i[r]=u.pop();try{s=o.apply(null,i)}catch(t){return t}}else s=e.hasOwnProperty(a)?e[a]:+a;u.push(s)}return u[0]}(o,t)}}var p={contextDelimiter:"",onMissingKey:null};function f(t,e){var n;for(n in this.data=t,this.pluralForms={},this.options={},p)this.options[n]=void 0!==e&&n in e?e[n]:p[n]}function d(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function h(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?d(Object(n),!0).forEach((function(e){var r,i,o;r=t,i=e,o=n[e],(i=u(i))in r?Object.defineProperty(r,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):r[i]=o})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):d(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}f.prototype.getPluralForm=function(t,e){var n,r,i,o=this.pluralForms[t];return o||("function"!=typeof(i=(n=this.data[t][""])["Plural-Forms"]||n["plural-forms"]||n.plural_forms)&&(r=function(t){var e,n,r;for(e=t.split(";"),n=0;n<e.length;n++)if(0===(r=e[n].trim()).indexOf("plural="))return r.substr(7)}(n["Plural-Forms"]||n["plural-forms"]||n.plural_forms),i=function(t){var e=l(t);return function(t){return+e({n:t})}}(r)),o=this.pluralForms[t]=i),o(e)},f.prototype.dcnpgettext=function(t,e,n,r,i){var o,a,s;return o=void 0===i?0:this.getPluralForm(t,i),a=n,e&&(a=e+this.options.contextDelimiter+n),(s=this.data[t][a])&&s[o]?s[o]:(this.options.onMissingKey&&this.options.onMissingKey(n,t),0===o?n:r)};var v={"":{plural_forms:function(t){return 1===t?0:1}}},m=/^i18n\.(n?gettext|has_translation)(_|$)/;var g=function(t){return"string"!=typeof t||""===t?(console.error("The namespace must be a non-empty string."),!1):!!/^[a-zA-Z][a-zA-Z0-9_.\-\/]*$/.test(t)||(console.error("The namespace can only contain numbers, letters, dashes, periods, underscores and slashes."),!1)};var y=function(t){return"string"!=typeof t||""===t?(console.error("The hook name must be a non-empty string."),!1):/^__/.test(t)?(console.error("The hook name cannot begin with `__`."),!1):!!/^[a-zA-Z][a-zA-Z0-9_.-]*$/.test(t)||(console.error("The hook name can only contain numbers, letters, dashes, periods and underscores."),!1)};var b=function(t,e){return function(n,r,i){var o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:10,a=t[e];if(y(n)&&g(r))if("function"==typeof i)if("number"==typeof o){var s={callback:i,priority:o,namespace:r};if(a[n]){var u,c=a[n].handlers;for(u=c.length;u>0&&!(o>=c[u-1].priority);u--);u===c.length?c[u]=s:c.splice(u,0,s),a.__current.forEach((function(t){t.name===n&&t.currentIndex>=u&&t.currentIndex++}))}else a[n]={handlers:[s],runs:0};"hookAdded"!==n&&t.doAction("hookAdded",n,r,i,o)}else console.error("If specified, the hook priority must be a number.");else console.error("The hook callback must be a function.")}};var x=function(t,e){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return function(r,i){var o=t[e];if(y(r)&&(n||g(i))){if(!o[r])return 0;var a=0;if(n)a=o[r].handlers.length,o[r]={runs:o[r].runs,handlers:[]};else for(var s=o[r].handlers,u=function(t){s[t].namespace===i&&(s.splice(t,1),a++,o.__current.forEach((function(e){e.name===r&&e.currentIndex>=t&&e.currentIndex--})))},c=s.length-1;c>=0;c--)u(c);return"hookRemoved"!==r&&t.doAction("hookRemoved",r,i),a}}};var _=function(t,e){return function(n,r){var i=t[e];return void 0!==r?n in i&&i[n].handlers.some((function(t){return t.namespace===r})):n in i}};var w=function(t,e){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return function(r){var i=t[e];i[r]||(i[r]={handlers:[],runs:0}),i[r].runs++;var o=i[r].handlers;for(var a=arguments.length,s=new Array(a>1?a-1:0),u=1;u<a;u++)s[u-1]=arguments[u];if(!o||!o.length)return n?s[0]:void 0;var c={name:r,currentIndex:0};for(i.__current.push(c);c.currentIndex<o.length;){var l=o[c.currentIndex].callback.apply(null,s);n&&(s[0]=l),c.currentIndex++}return i.__current.pop(),n?s[0]:void 0}};var k=function(t,e){return function(){var n,r,i=t[e];return null!==(n=null===(r=i.__current[i.__current.length-1])||void 0===r?void 0:r.name)&&void 0!==n?n:null}};var F=function(t,e){return function(n){var r=t[e];return void 0===n?void 0!==r.__current[0]:!!r.__current[0]&&n===r.__current[0].name}};var A=function(t,e){return function(n){var r=t[e];if(y(n))return r[n]&&r[n].runs?r[n].runs:0}},S=function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.actions=Object.create(null),this.actions.__current=[],this.filters=Object.create(null),this.filters.__current=[],this.addAction=b(this,"actions"),this.addFilter=b(this,"filters"),this.removeAction=x(this,"actions"),this.removeFilter=x(this,"filters"),this.hasAction=_(this,"actions"),this.hasFilter=_(this,"filters"),this.removeAllActions=x(this,"actions",!0),this.removeAllFilters=x(this,"filters",!0),this.doAction=w(this,"actions"),this.applyFilters=w(this,"filters",!0),this.currentAction=k(this,"actions"),this.currentFilter=k(this,"filters"),this.doingAction=F(this,"actions"),this.doingFilter=F(this,"filters"),this.didAction=A(this,"actions"),this.didFilter=A(this,"filters")};var E=function(){return new S}(),O=(E.addAction,E.addFilter,E.removeAction,E.removeFilter,E.hasAction,E.hasFilter,E.removeAllActions,E.removeAllFilters,E.doAction,E.applyFilters,E.currentAction,E.currentFilter,E.doingAction,E.doingFilter,E.didAction,E.didFilter,E.actions,E.filters,function(t,e,n){var r=new f({}),i=new Set,o=function(){i.forEach((function(t){return t()}))},a=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"default";r.data[e]=h(h(h({},v),r.data[e]),t),r.data[e][""]=h(h({},v[""]),r.data[e][""])},s=function(t,e){a(t,e),o()},u=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"default",e=arguments.length>1?arguments[1]:void 0,n=arguments.length>2?arguments[2]:void 0,i=arguments.length>3?arguments[3]:void 0,o=arguments.length>4?arguments[4]:void 0;return r.data[t]||a(void 0,t),r.dcnpgettext(t,e,n,i,o)},c=function(){return arguments.length>0&&void 0!==arguments[0]?arguments[0]:"default"},l=function(t,e,r){var i=u(r,e,t);return n?(i=n.applyFilters("i18n.gettext_with_context",i,t,e,r),n.applyFilters("i18n.gettext_with_context_"+c(r),i,t,e,r)):i};if(t&&s(t,e),n){var p=function(t){m.test(t)&&o()};n.addAction("hookAdded","core/i18n",p),n.addAction("hookRemoved","core/i18n",p)}return{getLocaleData:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"default";return r.data[t]},setLocaleData:s,resetLocaleData:function(t,e){r.data={},r.pluralForms={},s(t,e)},subscribe:function(t){return i.add(t),function(){return i.delete(t)}},__:function(t,e){var r=u(e,void 0,t);return n?(r=n.applyFilters("i18n.gettext",r,t,e),n.applyFilters("i18n.gettext_"+c(e),r,t,e)):r},_x:l,_n:function(t,e,r,i){var o=u(i,void 0,t,e,r);return n?(o=n.applyFilters("i18n.ngettext",o,t,e,r,i),n.applyFilters("i18n.ngettext_"+c(i),o,t,e,r,i)):o},_nx:function(t,e,r,i,o){var a=u(o,i,t,e,r);return n?(a=n.applyFilters("i18n.ngettext_with_context",a,t,e,r,i,o),n.applyFilters("i18n.ngettext_with_context_"+c(o),a,t,e,r,i,o)):a},isRTL:function(){return"rtl"===l("ltr","text direction")},hasTranslation:function(t,e,i){var o,a,s=e?e+""+t:t,u=!(null===(o=r.data)||void 0===o||null===(a=o[null!=i?i:"default"])||void 0===a||!a[s]);return n&&(u=n.applyFilters("i18n.has_translation",u,t,e,i),u=n.applyFilters("i18n.has_translation_"+c(i),u,t,e,i)),u}}}(void 0,void 0,E)),j=(O.getLocaleData.bind(O),O.setLocaleData.bind(O),O.resetLocaleData.bind(O),O.subscribe.bind(O),O.__.bind(O));O._x.bind(O),O._n.bind(O),O._nx.bind(O),O.isRTL.bind(O),O.hasTranslation.bind(O);const I={template:document.getElementById("main-image"),stepper:document.getElementById("responsive.pixel_step"),counter:document.getElementById("responsive.breakpoints"),max:document.getElementById("responsive.max_width"),min:document.getElementById("responsive.min_width"),details:document.getElementById("preview-details"),preview:null,init(){this.preview=this.template.parentNode,this.stepper.addEventListener("change",(()=>{this.counter.value=this.rebuildPreview()})),this.counter.addEventListener("change",(()=>{this.calculateShift()})),this.max.addEventListener("change",(()=>{this.calculateShift()})),this.min.addEventListener("change",(()=>{this.calculateShift()})),this.stepper.dispatchEvent(new Event("change"))},calculateShift(){const t=this.counter.value,e=(this.max.value-this.min.value)/(t-1);this.stepper.value=Math.floor(e),this.stepper.dispatchEvent(new Event("change"))},rebuildPreview(){let t=parseInt(this.max.value),e=parseInt(this.min.value),n=parseInt(this.stepper.value);1>n&&(this.stepper.value=n=50),t||(t=parseInt(this.max.dataset.default),this.max.value=t),e||(e=100,this.min.value=e);let r=t,i=r/t*100;const o=this.makeSize(r,i);o.classList.add("main-image"),this.preview.innerHTML="",this.preview.appendChild(o);let a=1;for(;r>e&&(r-=n,!(r<e));)i=r/t*100,this.preview.appendChild(this.makeSize(r,i)),a++;return this.details.innerText=j(`With a max width of ${t}px and a minimum of ${e}px, you get a potential of ${a} images.`,"cloudinary"),a},makeSize(t,e){const n=this.template.cloneNode(!0);return n.lastChild.innerText=t+"px",n.style.width=e+"%",n.style.height=e+"%",n.id="",n.classList.remove("main-image"),n}};window.addEventListener("load",(()=>I.init()))}()}();