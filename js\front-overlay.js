!function(){var e={616:function(e){e.exports=function(e,t){var n,r,i=0;function o(){var o,a,s=n,c=arguments.length;e:for(;s;){if(s.args.length===arguments.length){for(a=0;a<c;a++)if(s.args[a]!==arguments[a]){s=s.next;continue e}return s!==n&&(s===r&&(r=s.prev),s.prev.next=s.next,s.next&&(s.next.prev=s.prev),s.next=n,s.prev=null,n.prev=s,n=s),s.val}s=s.next}for(o=new Array(c),a=0;a<c;a++)o[a]=arguments[a];return s={args:o,val:e.apply(null,o)},n?(n.prev=s,s.next=n):r=s,i===t.maxSize?(r=r.prev).next=null:i++,n=s,s.val}return t=t||{},o.clear=function(){n=null,r=null,i=0},o}},604:function(e,t,n){var r;!function(){"use strict";var i={not_string:/[^s]/,not_bool:/[^t]/,not_type:/[^T]/,not_primitive:/[^v]/,number:/[diefg]/,numeric_arg:/[bcdiefguxX]/,json:/[j]/,not_json:/[^j]/,text:/^[^\x25]+/,modulo:/^\x25{2}/,placeholder:/^\x25(?:([1-9]\d*)\$|\(([^)]+)\))?(\+)?(0|'[^$])?(-)?(\d+)?(?:\.(\d+))?([b-gijostTuvxX])/,key:/^([a-z_][a-z_\d]*)/i,key_access:/^\.([a-z_][a-z_\d]*)/i,index_access:/^\[(\d+)\]/,sign:/^[+-]/};function o(e){return function(e,t){var n,r,a,s,c,u,p,f,l,d=1,h=e.length,m="";for(r=0;r<h;r++)if("string"==typeof e[r])m+=e[r];else if("object"==typeof e[r]){if((s=e[r]).keys)for(n=t[d],a=0;a<s.keys.length;a++){if(null==n)throw new Error(o('[sprintf] Cannot access property "%s" of undefined value "%s"',s.keys[a],s.keys[a-1]));n=n[s.keys[a]]}else n=s.param_no?t[s.param_no]:t[d++];if(i.not_type.test(s.type)&&i.not_primitive.test(s.type)&&n instanceof Function&&(n=n()),i.numeric_arg.test(s.type)&&"number"!=typeof n&&isNaN(n))throw new TypeError(o("[sprintf] expecting number but found %T",n));switch(i.number.test(s.type)&&(f=n>=0),s.type){case"b":n=parseInt(n,10).toString(2);break;case"c":n=String.fromCharCode(parseInt(n,10));break;case"d":case"i":n=parseInt(n,10);break;case"j":n=JSON.stringify(n,null,s.width?parseInt(s.width):0);break;case"e":n=s.precision?parseFloat(n).toExponential(s.precision):parseFloat(n).toExponential();break;case"f":n=s.precision?parseFloat(n).toFixed(s.precision):parseFloat(n);break;case"g":n=s.precision?String(Number(n.toPrecision(s.precision))):parseFloat(n);break;case"o":n=(parseInt(n,10)>>>0).toString(8);break;case"s":n=String(n),n=s.precision?n.substring(0,s.precision):n;break;case"t":n=String(!!n),n=s.precision?n.substring(0,s.precision):n;break;case"T":n=Object.prototype.toString.call(n).slice(8,-1).toLowerCase(),n=s.precision?n.substring(0,s.precision):n;break;case"u":n=parseInt(n,10)>>>0;break;case"v":n=n.valueOf(),n=s.precision?n.substring(0,s.precision):n;break;case"x":n=(parseInt(n,10)>>>0).toString(16);break;case"X":n=(parseInt(n,10)>>>0).toString(16).toUpperCase()}i.json.test(s.type)?m+=n:(!i.number.test(s.type)||f&&!s.sign?l="":(l=f?"+":"-",n=n.toString().replace(i.sign,"")),u=s.pad_char?"0"===s.pad_char?"0":s.pad_char.charAt(1):" ",p=s.width-(l+n).length,c=s.width&&p>0?u.repeat(p):"",m+=s.align?l+n+c:"0"===u?l+c+n:c+l+n)}return m}(function(e){if(s[e])return s[e];var t,n=e,r=[],o=0;for(;n;){if(null!==(t=i.text.exec(n)))r.push(t[0]);else if(null!==(t=i.modulo.exec(n)))r.push("%");else{if(null===(t=i.placeholder.exec(n)))throw new SyntaxError("[sprintf] unexpected placeholder");if(t[2]){o|=1;var a=[],c=t[2],u=[];if(null===(u=i.key.exec(c)))throw new SyntaxError("[sprintf] failed to parse named argument key");for(a.push(u[1]);""!==(c=c.substring(u[0].length));)if(null!==(u=i.key_access.exec(c)))a.push(u[1]);else{if(null===(u=i.index_access.exec(c)))throw new SyntaxError("[sprintf] failed to parse named argument key");a.push(u[1])}t[2]=a}else o|=2;if(3===o)throw new Error("[sprintf] mixing positional and named placeholders is not (yet) supported");r.push({placeholder:t[0],param_no:t[1],keys:t[2],sign:t[3],pad_char:t[4],align:t[5],width:t[6],precision:t[7],type:t[8]})}n=n.substring(t[0].length)}return s[e]=r}(e),arguments)}function a(e,t){return o.apply(null,[e].concat(t||[]))}var s=Object.create(null);o,a,"undefined"!=typeof window&&(window.sprintf=o,window.vsprintf=a,void 0===(r=function(){return{sprintf:o,vsprintf:a}}.call(t,n,t,e))||(e.exports=r))}()}},t={};function n(r){var i=t[r];if(void 0!==i)return i.exports;var o=t[r]={exports:{}};return e[r](o,o.exports,n),o.exports}n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,{a:t}),t},n.d=function(e,t){for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},function(){"use strict";var e,t,r,i,o=n(616),a=n.n(o);n(604),a()(console.error);function s(e){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},s(e)}function c(e){var t=function(e,t){if("object"!=s(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=s(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==s(t)?t:t+""}e={"(":9,"!":8,"*":7,"/":7,"%":7,"+":6,"-":6,"<":5,"<=":5,">":5,">=":5,"==":4,"!=":4,"&&":3,"||":2,"?":1,"?:":1},t=["(","?"],r={")":["("],":":["?","?:"]},i=/<=|>=|==|!=|&&|\|\||\?:|\(|!|\*|\/|%|\+|-|<|>|\?|\)|:/;var u={"!":function(e){return!e},"*":function(e,t){return e*t},"/":function(e,t){return e/t},"%":function(e,t){return e%t},"+":function(e,t){return e+t},"-":function(e,t){return e-t},"<":function(e,t){return e<t},"<=":function(e,t){return e<=t},">":function(e,t){return e>t},">=":function(e,t){return e>=t},"==":function(e,t){return e===t},"!=":function(e,t){return e!==t},"&&":function(e,t){return e&&t},"||":function(e,t){return e||t},"?:":function(e,t,n){if(e)throw t;return n}};function p(n){var o=function(n){for(var o,a,s,c,u=[],p=[];o=n.match(i);){for(a=o[0],(s=n.substr(0,o.index).trim())&&u.push(s);c=p.pop();){if(r[a]){if(r[a][0]===c){a=r[a][1]||a;break}}else if(t.indexOf(c)>=0||e[c]<e[a]){p.push(c);break}u.push(c)}r[a]||p.push(a),n=n.substr(o.index+a.length)}return(n=n.trim())&&u.push(n),u.concat(p.reverse())}(n);return function(e){return function(e,t){var n,r,i,o,a,s,c=[];for(n=0;n<e.length;n++){if(a=e[n],o=u[a]){for(r=o.length,i=Array(r);r--;)i[r]=c.pop();try{s=o.apply(null,i)}catch(e){return e}}else s=t.hasOwnProperty(a)?t[a]:+a;c.push(s)}return c[0]}(o,e)}}var f={contextDelimiter:"",onMissingKey:null};function l(e,t){var n;for(n in this.data=e,this.pluralForms={},this.options={},f)this.options[n]=void 0!==t&&n in t?t[n]:f[n]}function d(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function h(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?d(Object(n),!0).forEach((function(t){var r,i,o;r=e,i=t,o=n[t],(i=c(i))in r?Object.defineProperty(r,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):r[i]=o})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):d(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}l.prototype.getPluralForm=function(e,t){var n,r,i,o=this.pluralForms[e];return o||("function"!=typeof(i=(n=this.data[e][""])["Plural-Forms"]||n["plural-forms"]||n.plural_forms)&&(r=function(e){var t,n,r;for(t=e.split(";"),n=0;n<t.length;n++)if(0===(r=t[n].trim()).indexOf("plural="))return r.substr(7)}(n["Plural-Forms"]||n["plural-forms"]||n.plural_forms),i=function(e){var t=p(e);return function(e){return+t({n:e})}}(r)),o=this.pluralForms[e]=i),o(t)},l.prototype.dcnpgettext=function(e,t,n,r,i){var o,a,s;return o=void 0===i?0:this.getPluralForm(e,i),a=n,t&&(a=t+this.options.contextDelimiter+n),(s=this.data[e][a])&&s[o]?s[o]:(this.options.onMissingKey&&this.options.onMissingKey(n,e),0===o?n:r)};var m={"":{plural_forms:function(e){return 1===e?0:1}}},v=/^i18n\.(n?gettext|has_translation)(_|$)/;var g=function(e){return"string"!=typeof e||""===e?(console.error("The namespace must be a non-empty string."),!1):!!/^[a-zA-Z][a-zA-Z0-9_.\-\/]*$/.test(e)||(console.error("The namespace can only contain numbers, letters, dashes, periods, underscores and slashes."),!1)};var y=function(e){return"string"!=typeof e||""===e?(console.error("The hook name must be a non-empty string."),!1):/^__/.test(e)?(console.error("The hook name cannot begin with `__`."),!1):!!/^[a-zA-Z][a-zA-Z0-9_.-]*$/.test(e)||(console.error("The hook name can only contain numbers, letters, dashes, periods and underscores."),!1)};var b=function(e,t){return function(n,r,i){var o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:10,a=e[t];if(y(n)&&g(r))if("function"==typeof i)if("number"==typeof o){var s={callback:i,priority:o,namespace:r};if(a[n]){var c,u=a[n].handlers;for(c=u.length;c>0&&!(o>=u[c-1].priority);c--);c===u.length?u[c]=s:u.splice(c,0,s),a.__current.forEach((function(e){e.name===n&&e.currentIndex>=c&&e.currentIndex++}))}else a[n]={handlers:[s],runs:0};"hookAdded"!==n&&e.doAction("hookAdded",n,r,i,o)}else console.error("If specified, the hook priority must be a number.");else console.error("The hook callback must be a function.")}};var x=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return function(r,i){var o=e[t];if(y(r)&&(n||g(i))){if(!o[r])return 0;var a=0;if(n)a=o[r].handlers.length,o[r]={runs:o[r].runs,handlers:[]};else for(var s=o[r].handlers,c=function(e){s[e].namespace===i&&(s.splice(e,1),a++,o.__current.forEach((function(t){t.name===r&&t.currentIndex>=e&&t.currentIndex--})))},u=s.length-1;u>=0;u--)c(u);return"hookRemoved"!==r&&e.doAction("hookRemoved",r,i),a}}};var w=function(e,t){return function(n,r){var i=e[t];return void 0!==r?n in i&&i[n].handlers.some((function(e){return e.namespace===r})):n in i}};var O=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return function(r){var i=e[t];i[r]||(i[r]={handlers:[],runs:0}),i[r].runs++;var o=i[r].handlers;for(var a=arguments.length,s=new Array(a>1?a-1:0),c=1;c<a;c++)s[c-1]=arguments[c];if(!o||!o.length)return n?s[0]:void 0;var u={name:r,currentIndex:0};for(i.__current.push(u);u.currentIndex<o.length;){var p=o[u.currentIndex].callback.apply(null,s);n&&(s[0]=p),u.currentIndex++}return i.__current.pop(),n?s[0]:void 0}};var A=function(e,t){return function(){var n,r,i=e[t];return null!==(n=null===(r=i.__current[i.__current.length-1])||void 0===r?void 0:r.name)&&void 0!==n?n:null}};var E=function(e,t){return function(n){var r=e[t];return void 0===n?void 0!==r.__current[0]:!!r.__current[0]&&n===r.__current[0].name}};var T=function(e,t){return function(n){var r=e[t];if(y(n))return r[n]&&r[n].runs?r[n].runs:0}},_=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.actions=Object.create(null),this.actions.__current=[],this.filters=Object.create(null),this.filters.__current=[],this.addAction=b(this,"actions"),this.addFilter=b(this,"filters"),this.removeAction=x(this,"actions"),this.removeFilter=x(this,"filters"),this.hasAction=w(this,"actions"),this.hasFilter=w(this,"filters"),this.removeAllActions=x(this,"actions",!0),this.removeAllFilters=x(this,"filters",!0),this.doAction=O(this,"actions"),this.applyFilters=O(this,"filters",!0),this.currentAction=A(this,"actions"),this.currentFilter=A(this,"filters"),this.doingAction=E(this,"actions"),this.doingFilter=E(this,"filters"),this.didAction=T(this,"actions"),this.didFilter=T(this,"filters")};var k=function(){return new _}(),j=(k.addAction,k.addFilter,k.removeAction,k.removeFilter,k.hasAction,k.hasFilter,k.removeAllActions,k.removeAllFilters,k.doAction,k.applyFilters,k.currentAction,k.currentFilter,k.doingAction,k.doingFilter,k.didAction,k.didFilter,k.actions,k.filters,function(e,t,n){var r=new l({}),i=new Set,o=function(){i.forEach((function(e){return e()}))},a=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"default";r.data[t]=h(h(h({},m),r.data[t]),e),r.data[t][""]=h(h({},m[""]),r.data[t][""])},s=function(e,t){a(e,t),o()},c=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"default",t=arguments.length>1?arguments[1]:void 0,n=arguments.length>2?arguments[2]:void 0,i=arguments.length>3?arguments[3]:void 0,o=arguments.length>4?arguments[4]:void 0;return r.data[e]||a(void 0,e),r.dcnpgettext(e,t,n,i,o)},u=function(){return arguments.length>0&&void 0!==arguments[0]?arguments[0]:"default"},p=function(e,t,r){var i=c(r,t,e);return n?(i=n.applyFilters("i18n.gettext_with_context",i,e,t,r),n.applyFilters("i18n.gettext_with_context_"+u(r),i,e,t,r)):i};if(e&&s(e,t),n){var f=function(e){v.test(e)&&o()};n.addAction("hookAdded","core/i18n",f),n.addAction("hookRemoved","core/i18n",f)}return{getLocaleData:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"default";return r.data[e]},setLocaleData:s,resetLocaleData:function(e,t){r.data={},r.pluralForms={},s(e,t)},subscribe:function(e){return i.add(e),function(){return i.delete(e)}},__:function(e,t){var r=c(t,void 0,e);return n?(r=n.applyFilters("i18n.gettext",r,e,t),n.applyFilters("i18n.gettext_"+u(t),r,e,t)):r},_x:p,_n:function(e,t,r,i){var o=c(i,void 0,e,t,r);return n?(o=n.applyFilters("i18n.ngettext",o,e,t,r,i),n.applyFilters("i18n.ngettext_"+u(i),o,e,t,r,i)):o},_nx:function(e,t,r,i,o){var a=c(o,i,e,t,r);return n?(a=n.applyFilters("i18n.ngettext_with_context",a,e,t,r,i,o),n.applyFilters("i18n.ngettext_with_context_"+u(o),a,e,t,r,i,o)):a},isRTL:function(){return"rtl"===p("ltr","text direction")},hasTranslation:function(e,t,i){var o,a,s=t?t+""+e:e,c=!(null===(o=r.data)||void 0===o||null===(a=o[null!=i?i:"default"])||void 0===a||!a[s]);return n&&(c=n.applyFilters("i18n.has_translation",c,e,t,i),c=n.applyFilters("i18n.has_translation_"+u(i),c,e,t,i)),c}}}(void 0,void 0,k)),L=(j.getLocaleData.bind(j),j.setLocaleData.bind(j),j.resetLocaleData.bind(j),j.subscribe.bind(j),j.__.bind(j));j._x.bind(j),j._n.bind(j),j._nx.bind(j),j.isRTL.bind(j),j.hasTranslation.bind(j);function D(e){if(null==e)return window;if("[object Window]"!==e.toString()){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function C(e){return e instanceof D(e).Element||e instanceof Element}function S(e){return e instanceof D(e).HTMLElement||e instanceof HTMLElement}function F(e){return"undefined"!=typeof ShadowRoot&&(e instanceof D(e).ShadowRoot||e instanceof ShadowRoot)}var P=Math.max,I=Math.min,M=Math.round;function R(){var e=navigator.userAgentData;return null!=e&&e.brands?e.brands.map((function(e){return e.brand+"/"+e.version})).join(" "):navigator.userAgent}function H(){return!/^((?!chrome|android).)*safari/i.test(R())}function V(e,t,n){void 0===t&&(t=!1),void 0===n&&(n=!1);var r=e.getBoundingClientRect(),i=1,o=1;t&&S(e)&&(i=e.offsetWidth>0&&M(r.width)/e.offsetWidth||1,o=e.offsetHeight>0&&M(r.height)/e.offsetHeight||1);var a=(C(e)?D(e):window).visualViewport,s=!H()&&n,c=(r.left+(s&&a?a.offsetLeft:0))/i,u=(r.top+(s&&a?a.offsetTop:0))/o,p=r.width/i,f=r.height/o;return{width:p,height:f,top:u,right:c+p,bottom:u+f,left:c,x:c,y:u}}function W(e){var t=D(e);return{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function N(e){return e?(e.nodeName||"").toLowerCase():null}function B(e){return((C(e)?e.ownerDocument:e.document)||window.document).documentElement}function z(e){return V(B(e)).left+W(e).scrollLeft}function q(e){return D(e).getComputedStyle(e)}function U(e){var t=q(e),n=t.overflow,r=t.overflowX,i=t.overflowY;return/auto|scroll|overlay|hidden/.test(n+i+r)}function $(e,t,n){void 0===n&&(n=!1);var r,i,o=S(t),a=S(t)&&function(e){var t=e.getBoundingClientRect(),n=M(t.width)/e.offsetWidth||1,r=M(t.height)/e.offsetHeight||1;return 1!==n||1!==r}(t),s=B(t),c=V(e,a,n),u={scrollLeft:0,scrollTop:0},p={x:0,y:0};return(o||!o&&!n)&&(("body"!==N(t)||U(s))&&(u=(r=t)!==D(r)&&S(r)?{scrollLeft:(i=r).scrollLeft,scrollTop:i.scrollTop}:W(r)),S(t)?((p=V(t,!0)).x+=t.clientLeft,p.y+=t.clientTop):s&&(p.x=z(s))),{x:c.left+u.scrollLeft-p.x,y:c.top+u.scrollTop-p.y,width:c.width,height:c.height}}function X(e){var t=V(e),n=e.offsetWidth,r=e.offsetHeight;return Math.abs(t.width-n)<=1&&(n=t.width),Math.abs(t.height-r)<=1&&(r=t.height),{x:e.offsetLeft,y:e.offsetTop,width:n,height:r}}function Z(e){return"html"===N(e)?e:e.assignedSlot||e.parentNode||(F(e)?e.host:null)||B(e)}function K(e){return["html","body","#document"].indexOf(N(e))>=0?e.ownerDocument.body:S(e)&&U(e)?e:K(Z(e))}function Y(e,t){var n;void 0===t&&(t=[]);var r=K(e),i=r===(null==(n=e.ownerDocument)?void 0:n.body),o=D(r),a=i?[o].concat(o.visualViewport||[],U(r)?r:[]):r,s=t.concat(a);return i?s:s.concat(Y(Z(a)))}function J(e){return["table","td","th"].indexOf(N(e))>=0}function G(e){return S(e)&&"fixed"!==q(e).position?e.offsetParent:null}function Q(e){for(var t=D(e),n=G(e);n&&J(n)&&"static"===q(n).position;)n=G(n);return n&&("html"===N(n)||"body"===N(n)&&"static"===q(n).position)?t:n||function(e){var t=/firefox/i.test(R());if(/Trident/i.test(R())&&S(e)&&"fixed"===q(e).position)return null;var n=Z(e);for(F(n)&&(n=n.host);S(n)&&["html","body"].indexOf(N(n))<0;){var r=q(n);if("none"!==r.transform||"none"!==r.perspective||"paint"===r.contain||-1!==["transform","perspective"].indexOf(r.willChange)||t&&"filter"===r.willChange||t&&r.filter&&"none"!==r.filter)return n;n=n.parentNode}return null}(e)||t}var ee="top",te="bottom",ne="right",re="left",ie="auto",oe=[ee,te,ne,re],ae="start",se="end",ce="clippingParents",ue="viewport",pe="popper",fe="reference",le=oe.reduce((function(e,t){return e.concat([t+"-"+ae,t+"-"+se])}),[]),de=[].concat(oe,[ie]).reduce((function(e,t){return e.concat([t,t+"-"+ae,t+"-"+se])}),[]),he=["beforeRead","read","afterRead","beforeMain","main","afterMain","beforeWrite","write","afterWrite"];function me(e){var t=new Map,n=new Set,r=[];function i(e){n.add(e.name),[].concat(e.requires||[],e.requiresIfExists||[]).forEach((function(e){if(!n.has(e)){var r=t.get(e);r&&i(r)}})),r.push(e)}return e.forEach((function(e){t.set(e.name,e)})),e.forEach((function(e){n.has(e.name)||i(e)})),r}var ve={placement:"bottom",modifiers:[],strategy:"absolute"};function ge(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return!t.some((function(e){return!(e&&"function"==typeof e.getBoundingClientRect)}))}function ye(e){void 0===e&&(e={});var t=e,n=t.defaultModifiers,r=void 0===n?[]:n,i=t.defaultOptions,o=void 0===i?ve:i;return function(e,t,n){void 0===n&&(n=o);var i,a,s={placement:"bottom",orderedModifiers:[],options:Object.assign({},ve,o),modifiersData:{},elements:{reference:e,popper:t},attributes:{},styles:{}},c=[],u=!1,p={state:s,setOptions:function(n){var i="function"==typeof n?n(s.options):n;f(),s.options=Object.assign({},o,s.options,i),s.scrollParents={reference:C(e)?Y(e):e.contextElement?Y(e.contextElement):[],popper:Y(t)};var a=function(e){var t=me(e);return he.reduce((function(e,n){return e.concat(t.filter((function(e){return e.phase===n})))}),[])}(function(e){var t=e.reduce((function(e,t){var n=e[t.name];return e[t.name]=n?Object.assign({},n,t,{options:Object.assign({},n.options,t.options),data:Object.assign({},n.data,t.data)}):t,e}),{});return Object.keys(t).map((function(e){return t[e]}))}([].concat(r,s.options.modifiers)));return s.orderedModifiers=a.filter((function(e){return e.enabled})),s.orderedModifiers.forEach((function(e){var t=e.name,n=e.options,r=void 0===n?{}:n,i=e.effect;if("function"==typeof i){var o=i({state:s,name:t,instance:p,options:r}),a=function(){};c.push(o||a)}})),p.update()},forceUpdate:function(){if(!u){var e=s.elements,t=e.reference,n=e.popper;if(ge(t,n)){s.rects={reference:$(t,Q(n),"fixed"===s.options.strategy),popper:X(n)},s.reset=!1,s.placement=s.options.placement,s.orderedModifiers.forEach((function(e){return s.modifiersData[e.name]=Object.assign({},e.data)}));for(var r=0;r<s.orderedModifiers.length;r++)if(!0!==s.reset){var i=s.orderedModifiers[r],o=i.fn,a=i.options,c=void 0===a?{}:a,f=i.name;"function"==typeof o&&(s=o({state:s,options:c,name:f,instance:p})||s)}else s.reset=!1,r=-1}}},update:(i=function(){return new Promise((function(e){p.forceUpdate(),e(s)}))},function(){return a||(a=new Promise((function(e){Promise.resolve().then((function(){a=void 0,e(i())}))}))),a}),destroy:function(){f(),u=!0}};if(!ge(e,t))return p;function f(){c.forEach((function(e){return e()})),c=[]}return p.setOptions(n).then((function(e){!u&&n.onFirstUpdate&&n.onFirstUpdate(e)})),p}}var be={passive:!0};function xe(e){return e.split("-")[0]}function we(e){return e.split("-")[1]}function Oe(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function Ae(e){var t,n=e.reference,r=e.element,i=e.placement,o=i?xe(i):null,a=i?we(i):null,s=n.x+n.width/2-r.width/2,c=n.y+n.height/2-r.height/2;switch(o){case ee:t={x:s,y:n.y-r.height};break;case te:t={x:s,y:n.y+n.height};break;case ne:t={x:n.x+n.width,y:c};break;case re:t={x:n.x-r.width,y:c};break;default:t={x:n.x,y:n.y}}var u=o?Oe(o):null;if(null!=u){var p="y"===u?"height":"width";switch(a){case ae:t[u]=t[u]-(n[p]/2-r[p]/2);break;case se:t[u]=t[u]+(n[p]/2-r[p]/2)}}return t}var Ee={top:"auto",right:"auto",bottom:"auto",left:"auto"};function Te(e){var t,n=e.popper,r=e.popperRect,i=e.placement,o=e.variation,a=e.offsets,s=e.position,c=e.gpuAcceleration,u=e.adaptive,p=e.roundOffsets,f=e.isFixed,l=a.x,d=void 0===l?0:l,h=a.y,m=void 0===h?0:h,v="function"==typeof p?p({x:d,y:m}):{x:d,y:m};d=v.x,m=v.y;var g=a.hasOwnProperty("x"),y=a.hasOwnProperty("y"),b=re,x=ee,w=window;if(u){var O=Q(n),A="clientHeight",E="clientWidth";if(O===D(n)&&"static"!==q(O=B(n)).position&&"absolute"===s&&(A="scrollHeight",E="scrollWidth"),i===ee||(i===re||i===ne)&&o===se)x=te,m-=(f&&O===w&&w.visualViewport?w.visualViewport.height:O[A])-r.height,m*=c?1:-1;if(i===re||(i===ee||i===te)&&o===se)b=ne,d-=(f&&O===w&&w.visualViewport?w.visualViewport.width:O[E])-r.width,d*=c?1:-1}var T,_=Object.assign({position:s},u&&Ee),k=!0===p?function(e){var t=e.x,n=e.y,r=window.devicePixelRatio||1;return{x:M(t*r)/r||0,y:M(n*r)/r||0}}({x:d,y:m}):{x:d,y:m};return d=k.x,m=k.y,c?Object.assign({},_,((T={})[x]=y?"0":"",T[b]=g?"0":"",T.transform=(w.devicePixelRatio||1)<=1?"translate("+d+"px, "+m+"px)":"translate3d("+d+"px, "+m+"px, 0)",T)):Object.assign({},_,((t={})[x]=y?m+"px":"",t[b]=g?d+"px":"",t.transform="",t))}var _e={name:"applyStyles",enabled:!0,phase:"write",fn:function(e){var t=e.state;Object.keys(t.elements).forEach((function(e){var n=t.styles[e]||{},r=t.attributes[e]||{},i=t.elements[e];S(i)&&N(i)&&(Object.assign(i.style,n),Object.keys(r).forEach((function(e){var t=r[e];!1===t?i.removeAttribute(e):i.setAttribute(e,!0===t?"":t)})))}))},effect:function(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow),function(){Object.keys(t.elements).forEach((function(e){var r=t.elements[e],i=t.attributes[e]||{},o=Object.keys(t.styles.hasOwnProperty(e)?t.styles[e]:n[e]).reduce((function(e,t){return e[t]="",e}),{});S(r)&&N(r)&&(Object.assign(r.style,o),Object.keys(i).forEach((function(e){r.removeAttribute(e)})))}))}},requires:["computeStyles"]};var ke={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(e){var t=e.state,n=e.options,r=e.name,i=n.offset,o=void 0===i?[0,0]:i,a=de.reduce((function(e,n){return e[n]=function(e,t,n){var r=xe(e),i=[re,ee].indexOf(r)>=0?-1:1,o="function"==typeof n?n(Object.assign({},t,{placement:e})):n,a=o[0],s=o[1];return a=a||0,s=(s||0)*i,[re,ne].indexOf(r)>=0?{x:s,y:a}:{x:a,y:s}}(n,t.rects,o),e}),{}),s=a[t.placement],c=s.x,u=s.y;null!=t.modifiersData.popperOffsets&&(t.modifiersData.popperOffsets.x+=c,t.modifiersData.popperOffsets.y+=u),t.modifiersData[r]=a}},je={left:"right",right:"left",bottom:"top",top:"bottom"};function Le(e){return e.replace(/left|right|bottom|top/g,(function(e){return je[e]}))}var De={start:"end",end:"start"};function Ce(e){return e.replace(/start|end/g,(function(e){return De[e]}))}function Se(e,t){var n=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(n&&F(n)){var r=t;do{if(r&&e.isSameNode(r))return!0;r=r.parentNode||r.host}while(r)}return!1}function Fe(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function Pe(e,t,n){return t===ue?Fe(function(e,t){var n=D(e),r=B(e),i=n.visualViewport,o=r.clientWidth,a=r.clientHeight,s=0,c=0;if(i){o=i.width,a=i.height;var u=H();(u||!u&&"fixed"===t)&&(s=i.offsetLeft,c=i.offsetTop)}return{width:o,height:a,x:s+z(e),y:c}}(e,n)):C(t)?function(e,t){var n=V(e,!1,"fixed"===t);return n.top=n.top+e.clientTop,n.left=n.left+e.clientLeft,n.bottom=n.top+e.clientHeight,n.right=n.left+e.clientWidth,n.width=e.clientWidth,n.height=e.clientHeight,n.x=n.left,n.y=n.top,n}(t,n):Fe(function(e){var t,n=B(e),r=W(e),i=null==(t=e.ownerDocument)?void 0:t.body,o=P(n.scrollWidth,n.clientWidth,i?i.scrollWidth:0,i?i.clientWidth:0),a=P(n.scrollHeight,n.clientHeight,i?i.scrollHeight:0,i?i.clientHeight:0),s=-r.scrollLeft+z(e),c=-r.scrollTop;return"rtl"===q(i||n).direction&&(s+=P(n.clientWidth,i?i.clientWidth:0)-o),{width:o,height:a,x:s,y:c}}(B(e)))}function Ie(e,t,n,r){var i="clippingParents"===t?function(e){var t=Y(Z(e)),n=["absolute","fixed"].indexOf(q(e).position)>=0&&S(e)?Q(e):e;return C(n)?t.filter((function(e){return C(e)&&Se(e,n)&&"body"!==N(e)})):[]}(e):[].concat(t),o=[].concat(i,[n]),a=o[0],s=o.reduce((function(t,n){var i=Pe(e,n,r);return t.top=P(i.top,t.top),t.right=I(i.right,t.right),t.bottom=I(i.bottom,t.bottom),t.left=P(i.left,t.left),t}),Pe(e,a,r));return s.width=s.right-s.left,s.height=s.bottom-s.top,s.x=s.left,s.y=s.top,s}function Me(e){return Object.assign({},{top:0,right:0,bottom:0,left:0},e)}function Re(e,t){return t.reduce((function(t,n){return t[n]=e,t}),{})}function He(e,t){void 0===t&&(t={});var n=t,r=n.placement,i=void 0===r?e.placement:r,o=n.strategy,a=void 0===o?e.strategy:o,s=n.boundary,c=void 0===s?ce:s,u=n.rootBoundary,p=void 0===u?ue:u,f=n.elementContext,l=void 0===f?pe:f,d=n.altBoundary,h=void 0!==d&&d,m=n.padding,v=void 0===m?0:m,g=Me("number"!=typeof v?v:Re(v,oe)),y=l===pe?fe:pe,b=e.rects.popper,x=e.elements[h?y:l],w=Ie(C(x)?x:x.contextElement||B(e.elements.popper),c,p,a),O=V(e.elements.reference),A=Ae({reference:O,element:b,strategy:"absolute",placement:i}),E=Fe(Object.assign({},b,A)),T=l===pe?E:O,_={top:w.top-T.top+g.top,bottom:T.bottom-w.bottom+g.bottom,left:w.left-T.left+g.left,right:T.right-w.right+g.right},k=e.modifiersData.offset;if(l===pe&&k){var j=k[i];Object.keys(_).forEach((function(e){var t=[ne,te].indexOf(e)>=0?1:-1,n=[ee,te].indexOf(e)>=0?"y":"x";_[e]+=j[n]*t}))}return _}function Ve(e,t,n){return P(e,I(t,n))}var We={name:"preventOverflow",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,r=e.name,i=n.mainAxis,o=void 0===i||i,a=n.altAxis,s=void 0!==a&&a,c=n.boundary,u=n.rootBoundary,p=n.altBoundary,f=n.padding,l=n.tether,d=void 0===l||l,h=n.tetherOffset,m=void 0===h?0:h,v=He(t,{boundary:c,rootBoundary:u,padding:f,altBoundary:p}),g=xe(t.placement),y=we(t.placement),b=!y,x=Oe(g),w="x"===x?"y":"x",O=t.modifiersData.popperOffsets,A=t.rects.reference,E=t.rects.popper,T="function"==typeof m?m(Object.assign({},t.rects,{placement:t.placement})):m,_="number"==typeof T?{mainAxis:T,altAxis:T}:Object.assign({mainAxis:0,altAxis:0},T),k=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,j={x:0,y:0};if(O){if(o){var L,D="y"===x?ee:re,C="y"===x?te:ne,S="y"===x?"height":"width",F=O[x],M=F+v[D],R=F-v[C],H=d?-E[S]/2:0,V=y===ae?A[S]:E[S],W=y===ae?-E[S]:-A[S],N=t.elements.arrow,B=d&&N?X(N):{width:0,height:0},z=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:{top:0,right:0,bottom:0,left:0},q=z[D],U=z[C],$=Ve(0,A[S],B[S]),Z=b?A[S]/2-H-$-q-_.mainAxis:V-$-q-_.mainAxis,K=b?-A[S]/2+H+$+U+_.mainAxis:W+$+U+_.mainAxis,Y=t.elements.arrow&&Q(t.elements.arrow),J=Y?"y"===x?Y.clientTop||0:Y.clientLeft||0:0,G=null!=(L=null==k?void 0:k[x])?L:0,ie=F+K-G,oe=Ve(d?I(M,F+Z-G-J):M,F,d?P(R,ie):R);O[x]=oe,j[x]=oe-F}if(s){var se,ce="x"===x?ee:re,ue="x"===x?te:ne,pe=O[w],fe="y"===w?"height":"width",le=pe+v[ce],de=pe-v[ue],he=-1!==[ee,re].indexOf(g),me=null!=(se=null==k?void 0:k[w])?se:0,ve=he?le:pe-A[fe]-E[fe]-me+_.altAxis,ge=he?pe+A[fe]+E[fe]-me-_.altAxis:de,ye=d&&he?function(e,t,n){var r=Ve(e,t,n);return r>n?n:r}(ve,pe,ge):Ve(d?ve:le,pe,d?ge:de);O[w]=ye,j[w]=ye-pe}t.modifiersData[r]=j}},requiresIfExists:["offset"]};var Ne={name:"arrow",enabled:!0,phase:"main",fn:function(e){var t,n=e.state,r=e.name,i=e.options,o=n.elements.arrow,a=n.modifiersData.popperOffsets,s=xe(n.placement),c=Oe(s),u=[re,ne].indexOf(s)>=0?"height":"width";if(o&&a){var p=function(e,t){return Me("number"!=typeof(e="function"==typeof e?e(Object.assign({},t.rects,{placement:t.placement})):e)?e:Re(e,oe))}(i.padding,n),f=X(o),l="y"===c?ee:re,d="y"===c?te:ne,h=n.rects.reference[u]+n.rects.reference[c]-a[c]-n.rects.popper[u],m=a[c]-n.rects.reference[c],v=Q(o),g=v?"y"===c?v.clientHeight||0:v.clientWidth||0:0,y=h/2-m/2,b=p[l],x=g-f[u]-p[d],w=g/2-f[u]/2+y,O=Ve(b,w,x),A=c;n.modifiersData[r]=((t={})[A]=O,t.centerOffset=O-w,t)}},effect:function(e){var t=e.state,n=e.options.element,r=void 0===n?"[data-popper-arrow]":n;null!=r&&("string"!=typeof r||(r=t.elements.popper.querySelector(r)))&&Se(t.elements.popper,r)&&(t.elements.arrow=r)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function Be(e,t,n){return void 0===n&&(n={x:0,y:0}),{top:e.top-t.height-n.y,right:e.right-t.width+n.x,bottom:e.bottom-t.height+n.y,left:e.left-t.width-n.x}}function ze(e){return[ee,ne,te,re].some((function(t){return e[t]>=0}))}var qe=ye({defaultModifiers:[{name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(e){var t=e.state,n=e.instance,r=e.options,i=r.scroll,o=void 0===i||i,a=r.resize,s=void 0===a||a,c=D(t.elements.popper),u=[].concat(t.scrollParents.reference,t.scrollParents.popper);return o&&u.forEach((function(e){e.addEventListener("scroll",n.update,be)})),s&&c.addEventListener("resize",n.update,be),function(){o&&u.forEach((function(e){e.removeEventListener("scroll",n.update,be)})),s&&c.removeEventListener("resize",n.update,be)}},data:{}},{name:"popperOffsets",enabled:!0,phase:"read",fn:function(e){var t=e.state,n=e.name;t.modifiersData[n]=Ae({reference:t.rects.reference,element:t.rects.popper,strategy:"absolute",placement:t.placement})},data:{}},{name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(e){var t=e.state,n=e.options,r=n.gpuAcceleration,i=void 0===r||r,o=n.adaptive,a=void 0===o||o,s=n.roundOffsets,c=void 0===s||s,u={placement:xe(t.placement),variation:we(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:i,isFixed:"fixed"===t.options.strategy};null!=t.modifiersData.popperOffsets&&(t.styles.popper=Object.assign({},t.styles.popper,Te(Object.assign({},u,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:a,roundOffsets:c})))),null!=t.modifiersData.arrow&&(t.styles.arrow=Object.assign({},t.styles.arrow,Te(Object.assign({},u,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:c})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})},data:{}},_e,ke,{name:"flip",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,r=e.name;if(!t.modifiersData[r]._skip){for(var i=n.mainAxis,o=void 0===i||i,a=n.altAxis,s=void 0===a||a,c=n.fallbackPlacements,u=n.padding,p=n.boundary,f=n.rootBoundary,l=n.altBoundary,d=n.flipVariations,h=void 0===d||d,m=n.allowedAutoPlacements,v=t.options.placement,g=xe(v),y=c||(g===v||!h?[Le(v)]:function(e){if(xe(e)===ie)return[];var t=Le(e);return[Ce(e),t,Ce(t)]}(v)),b=[v].concat(y).reduce((function(e,n){return e.concat(xe(n)===ie?function(e,t){void 0===t&&(t={});var n=t,r=n.placement,i=n.boundary,o=n.rootBoundary,a=n.padding,s=n.flipVariations,c=n.allowedAutoPlacements,u=void 0===c?de:c,p=we(r),f=p?s?le:le.filter((function(e){return we(e)===p})):oe,l=f.filter((function(e){return u.indexOf(e)>=0}));0===l.length&&(l=f);var d=l.reduce((function(t,n){return t[n]=He(e,{placement:n,boundary:i,rootBoundary:o,padding:a})[xe(n)],t}),{});return Object.keys(d).sort((function(e,t){return d[e]-d[t]}))}(t,{placement:n,boundary:p,rootBoundary:f,padding:u,flipVariations:h,allowedAutoPlacements:m}):n)}),[]),x=t.rects.reference,w=t.rects.popper,O=new Map,A=!0,E=b[0],T=0;T<b.length;T++){var _=b[T],k=xe(_),j=we(_)===ae,L=[ee,te].indexOf(k)>=0,D=L?"width":"height",C=He(t,{placement:_,boundary:p,rootBoundary:f,altBoundary:l,padding:u}),S=L?j?ne:re:j?te:ee;x[D]>w[D]&&(S=Le(S));var F=Le(S),P=[];if(o&&P.push(C[k]<=0),s&&P.push(C[S]<=0,C[F]<=0),P.every((function(e){return e}))){E=_,A=!1;break}O.set(_,P)}if(A)for(var I=function(e){var t=b.find((function(t){var n=O.get(t);if(n)return n.slice(0,e).every((function(e){return e}))}));if(t)return E=t,"break"},M=h?3:1;M>0;M--){if("break"===I(M))break}t.placement!==E&&(t.modifiersData[r]._skip=!0,t.placement=E,t.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}},We,Ne,{name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(e){var t=e.state,n=e.name,r=t.rects.reference,i=t.rects.popper,o=t.modifiersData.preventOverflow,a=He(t,{elementContext:"reference"}),s=He(t,{altBoundary:!0}),c=Be(a,r),u=Be(s,i,o),p=ze(c),f=ze(u);t.modifiersData[n]={referenceClippingOffsets:c,popperEscapeOffsets:u,isReferenceHidden:p,hasPopperEscaped:f},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":p,"data-popper-escaped":f})}}]}),Ue="tippy-content",$e="tippy-backdrop",Xe="tippy-arrow",Ze="tippy-svg-arrow",Ke={passive:!0,capture:!0},Ye=function(){return document.body};function Je(e,t,n){if(Array.isArray(e)){var r=e[t];return null==r?Array.isArray(n)?n[t]:n:r}return e}function Ge(e,t){var n={}.toString.call(e);return 0===n.indexOf("[object")&&n.indexOf(t+"]")>-1}function Qe(e,t){return"function"==typeof e?e.apply(void 0,t):e}function et(e,t){return 0===t?e:function(r){clearTimeout(n),n=setTimeout((function(){e(r)}),t)};var n}function tt(e){return[].concat(e)}function nt(e,t){-1===e.indexOf(t)&&e.push(t)}function rt(e){return e.split("-")[0]}function it(e){return[].slice.call(e)}function ot(e){return Object.keys(e).reduce((function(t,n){return void 0!==e[n]&&(t[n]=e[n]),t}),{})}function at(){return document.createElement("div")}function st(e){return["Element","Fragment"].some((function(t){return Ge(e,t)}))}function ct(e){return Ge(e,"MouseEvent")}function ut(e){return!(!e||!e._tippy||e._tippy.reference!==e)}function pt(e){return st(e)?[e]:function(e){return Ge(e,"NodeList")}(e)?it(e):Array.isArray(e)?e:it(document.querySelectorAll(e))}function ft(e,t){e.forEach((function(e){e&&(e.style.transitionDuration=t+"ms")}))}function lt(e,t){e.forEach((function(e){e&&e.setAttribute("data-state",t)}))}function dt(e){var t,n=tt(e)[0];return null!=n&&null!=(t=n.ownerDocument)&&t.body?n.ownerDocument:document}function ht(e,t,n){var r=t+"EventListener";["transitionend","webkitTransitionEnd"].forEach((function(t){e[r](t,n)}))}function mt(e,t){for(var n=t;n;){var r;if(e.contains(n))return!0;n=null==n.getRootNode||null==(r=n.getRootNode())?void 0:r.host}return!1}var vt={isTouch:!1},gt=0;function yt(){vt.isTouch||(vt.isTouch=!0,window.performance&&document.addEventListener("mousemove",bt))}function bt(){var e=performance.now();e-gt<20&&(vt.isTouch=!1,document.removeEventListener("mousemove",bt)),gt=e}function xt(){var e=document.activeElement;if(ut(e)){var t=e._tippy;e.blur&&!t.state.isVisible&&e.blur()}}var wt=!!("undefined"!=typeof window&&"undefined"!=typeof document)&&!!window.msCrypto;var Ot={animateFill:!1,followCursor:!1,inlinePositioning:!1,sticky:!1},At=Object.assign({appendTo:Ye,aria:{content:"auto",expanded:"auto"},delay:0,duration:[300,250],getReferenceClientRect:null,hideOnClick:!0,ignoreAttributes:!1,interactive:!1,interactiveBorder:2,interactiveDebounce:0,moveTransition:"",offset:[0,10],onAfterUpdate:function(){},onBeforeUpdate:function(){},onCreate:function(){},onDestroy:function(){},onHidden:function(){},onHide:function(){},onMount:function(){},onShow:function(){},onShown:function(){},onTrigger:function(){},onUntrigger:function(){},onClickOutside:function(){},placement:"top",plugins:[],popperOptions:{},render:null,showOnCreate:!1,touch:!0,trigger:"mouseenter focus",triggerTarget:null},Ot,{allowHTML:!1,animation:"fade",arrow:!0,content:"",inertia:!1,maxWidth:350,role:"tooltip",theme:"",zIndex:9999}),Et=Object.keys(At);function Tt(e){var t=(e.plugins||[]).reduce((function(t,n){var r,i=n.name,o=n.defaultValue;i&&(t[i]=void 0!==e[i]?e[i]:null!=(r=At[i])?r:o);return t}),{});return Object.assign({},e,t)}function _t(e,t){var n=Object.assign({},t,{content:Qe(t.content,[e])},t.ignoreAttributes?{}:function(e,t){return(t?Object.keys(Tt(Object.assign({},At,{plugins:t}))):Et).reduce((function(t,n){var r=(e.getAttribute("data-tippy-"+n)||"").trim();if(!r)return t;if("content"===n)t[n]=r;else try{t[n]=JSON.parse(r)}catch(e){t[n]=r}return t}),{})}(e,t.plugins));return n.aria=Object.assign({},At.aria,n.aria),n.aria={expanded:"auto"===n.aria.expanded?t.interactive:n.aria.expanded,content:"auto"===n.aria.content?t.interactive?null:"describedby":n.aria.content},n}var kt=function(){return"innerHTML"};function jt(e,t){e[kt()]=t}function Lt(e){var t=at();return!0===e?t.className=Xe:(t.className=Ze,st(e)?t.appendChild(e):jt(t,e)),t}function Dt(e,t){st(t.content)?(jt(e,""),e.appendChild(t.content)):"function"!=typeof t.content&&(t.allowHTML?jt(e,t.content):e.textContent=t.content)}function Ct(e){var t=e.firstElementChild,n=it(t.children);return{box:t,content:n.find((function(e){return e.classList.contains(Ue)})),arrow:n.find((function(e){return e.classList.contains(Xe)||e.classList.contains(Ze)})),backdrop:n.find((function(e){return e.classList.contains($e)}))}}function St(e){var t=at(),n=at();n.className="tippy-box",n.setAttribute("data-state","hidden"),n.setAttribute("tabindex","-1");var r=at();function i(n,r){var i=Ct(t),o=i.box,a=i.content,s=i.arrow;r.theme?o.setAttribute("data-theme",r.theme):o.removeAttribute("data-theme"),"string"==typeof r.animation?o.setAttribute("data-animation",r.animation):o.removeAttribute("data-animation"),r.inertia?o.setAttribute("data-inertia",""):o.removeAttribute("data-inertia"),o.style.maxWidth="number"==typeof r.maxWidth?r.maxWidth+"px":r.maxWidth,r.role?o.setAttribute("role",r.role):o.removeAttribute("role"),n.content===r.content&&n.allowHTML===r.allowHTML||Dt(a,e.props),r.arrow?s?n.arrow!==r.arrow&&(o.removeChild(s),o.appendChild(Lt(r.arrow))):o.appendChild(Lt(r.arrow)):s&&o.removeChild(s)}return r.className=Ue,r.setAttribute("data-state","hidden"),Dt(r,e.props),t.appendChild(n),n.appendChild(r),i(e.props,e.props),{popper:t,onUpdate:i}}St.$$tippy=!0;var Ft=1,Pt=[],It=[];function Mt(e,t){var n,r,i,o,a,s,c,u,p=_t(e,Object.assign({},At,Tt(ot(t)))),f=!1,l=!1,d=!1,h=!1,m=[],v=et(X,p.interactiveDebounce),g=Ft++,y=(u=p.plugins).filter((function(e,t){return u.indexOf(e)===t})),b={id:g,reference:e,popper:at(),popperInstance:null,props:p,state:{isEnabled:!0,isVisible:!1,isDestroyed:!1,isMounted:!1,isShown:!1},plugins:y,clearDelayTimeouts:function(){clearTimeout(n),clearTimeout(r),cancelAnimationFrame(i)},setProps:function(t){0;if(b.state.isDestroyed)return;F("onBeforeUpdate",[b,t]),U();var n=b.props,r=_t(e,Object.assign({},n,ot(t),{ignoreAttributes:!0}));b.props=r,q(),n.interactiveDebounce!==r.interactiveDebounce&&(M(),v=et(X,r.interactiveDebounce));n.triggerTarget&&!r.triggerTarget?tt(n.triggerTarget).forEach((function(e){e.removeAttribute("aria-expanded")})):r.triggerTarget&&e.removeAttribute("aria-expanded");I(),S(),O&&O(n,r);b.popperInstance&&(J(),Q().forEach((function(e){requestAnimationFrame(e._tippy.popperInstance.forceUpdate)})));F("onAfterUpdate",[b,t])},setContent:function(e){b.setProps({content:e})},show:function(){0;var e=b.state.isVisible,t=b.state.isDestroyed,n=!b.state.isEnabled,r=vt.isTouch&&!b.props.touch,i=Je(b.props.duration,0,At.duration);if(e||t||n||r)return;if(j().hasAttribute("disabled"))return;if(F("onShow",[b],!1),!1===b.props.onShow(b))return;b.state.isVisible=!0,k()&&(w.style.visibility="visible");S(),W(),b.state.isMounted||(w.style.transition="none");if(k()){var o=D();ft([o.box,o.content],0)}s=function(){var e;if(b.state.isVisible&&!h){if(h=!0,w.offsetHeight,w.style.transition=b.props.moveTransition,k()&&b.props.animation){var t=D(),n=t.box,r=t.content;ft([n,r],i),lt([n,r],"visible")}P(),I(),nt(It,b),null==(e=b.popperInstance)||e.forceUpdate(),F("onMount",[b]),b.props.animation&&k()&&function(e,t){B(e,t)}(i,(function(){b.state.isShown=!0,F("onShown",[b])}))}},function(){var e,t=b.props.appendTo,n=j();e=b.props.interactive&&t===Ye||"parent"===t?n.parentNode:Qe(t,[n]);e.contains(w)||e.appendChild(w);b.state.isMounted=!0,J(),!1}()},hide:function(){0;var e=!b.state.isVisible,t=b.state.isDestroyed,n=!b.state.isEnabled,r=Je(b.props.duration,1,At.duration);if(e||t||n)return;if(F("onHide",[b],!1),!1===b.props.onHide(b))return;b.state.isVisible=!1,b.state.isShown=!1,h=!1,f=!1,k()&&(w.style.visibility="hidden");if(M(),N(),S(!0),k()){var i=D(),o=i.box,a=i.content;b.props.animation&&(ft([o,a],r),lt([o,a],"hidden"))}P(),I(),b.props.animation?k()&&function(e,t){B(e,(function(){!b.state.isVisible&&w.parentNode&&w.parentNode.contains(w)&&t()}))}(r,b.unmount):b.unmount()},hideWithInteractivity:function(e){0;L().addEventListener("mousemove",v),nt(Pt,v),v(e)},enable:function(){b.state.isEnabled=!0},disable:function(){b.hide(),b.state.isEnabled=!1},unmount:function(){0;b.state.isVisible&&b.hide();if(!b.state.isMounted)return;G(),Q().forEach((function(e){e._tippy.unmount()})),w.parentNode&&w.parentNode.removeChild(w);It=It.filter((function(e){return e!==b})),b.state.isMounted=!1,F("onHidden",[b])},destroy:function(){0;if(b.state.isDestroyed)return;b.clearDelayTimeouts(),b.unmount(),U(),delete e._tippy,b.state.isDestroyed=!0,F("onDestroy",[b])}};if(!p.render)return b;var x=p.render(b),w=x.popper,O=x.onUpdate;w.setAttribute("data-tippy-root",""),w.id="tippy-"+b.id,b.popper=w,e._tippy=b,w._tippy=b;var A=y.map((function(e){return e.fn(b)})),E=e.hasAttribute("aria-expanded");return q(),I(),S(),F("onCreate",[b]),p.showOnCreate&&ee(),w.addEventListener("mouseenter",(function(){b.props.interactive&&b.state.isVisible&&b.clearDelayTimeouts()})),w.addEventListener("mouseleave",(function(){b.props.interactive&&b.props.trigger.indexOf("mouseenter")>=0&&L().addEventListener("mousemove",v)})),b;function T(){var e=b.props.touch;return Array.isArray(e)?e:[e,0]}function _(){return"hold"===T()[0]}function k(){var e;return!(null==(e=b.props.render)||!e.$$tippy)}function j(){return c||e}function L(){var e=j().parentNode;return e?dt(e):document}function D(){return Ct(w)}function C(e){return b.state.isMounted&&!b.state.isVisible||vt.isTouch||o&&"focus"===o.type?0:Je(b.props.delay,e?0:1,At.delay)}function S(e){void 0===e&&(e=!1),w.style.pointerEvents=b.props.interactive&&!e?"":"none",w.style.zIndex=""+b.props.zIndex}function F(e,t,n){var r;(void 0===n&&(n=!0),A.forEach((function(n){n[e]&&n[e].apply(n,t)})),n)&&(r=b.props)[e].apply(r,t)}function P(){var t=b.props.aria;if(t.content){var n="aria-"+t.content,r=w.id;tt(b.props.triggerTarget||e).forEach((function(e){var t=e.getAttribute(n);if(b.state.isVisible)e.setAttribute(n,t?t+" "+r:r);else{var i=t&&t.replace(r,"").trim();i?e.setAttribute(n,i):e.removeAttribute(n)}}))}}function I(){!E&&b.props.aria.expanded&&tt(b.props.triggerTarget||e).forEach((function(e){b.props.interactive?e.setAttribute("aria-expanded",b.state.isVisible&&e===j()?"true":"false"):e.removeAttribute("aria-expanded")}))}function M(){L().removeEventListener("mousemove",v),Pt=Pt.filter((function(e){return e!==v}))}function R(t){if(!vt.isTouch||!d&&"mousedown"!==t.type){var n=t.composedPath&&t.composedPath()[0]||t.target;if(!b.props.interactive||!mt(w,n)){if(tt(b.props.triggerTarget||e).some((function(e){return mt(e,n)}))){if(vt.isTouch)return;if(b.state.isVisible&&b.props.trigger.indexOf("click")>=0)return}else F("onClickOutside",[b,t]);!0===b.props.hideOnClick&&(b.clearDelayTimeouts(),b.hide(),l=!0,setTimeout((function(){l=!1})),b.state.isMounted||N())}}}function H(){d=!0}function V(){d=!1}function W(){var e=L();e.addEventListener("mousedown",R,!0),e.addEventListener("touchend",R,Ke),e.addEventListener("touchstart",V,Ke),e.addEventListener("touchmove",H,Ke)}function N(){var e=L();e.removeEventListener("mousedown",R,!0),e.removeEventListener("touchend",R,Ke),e.removeEventListener("touchstart",V,Ke),e.removeEventListener("touchmove",H,Ke)}function B(e,t){var n=D().box;function r(e){e.target===n&&(ht(n,"remove",r),t())}if(0===e)return t();ht(n,"remove",a),ht(n,"add",r),a=r}function z(t,n,r){void 0===r&&(r=!1),tt(b.props.triggerTarget||e).forEach((function(e){e.addEventListener(t,n,r),m.push({node:e,eventType:t,handler:n,options:r})}))}function q(){var e;_()&&(z("touchstart",$,{passive:!0}),z("touchend",Z,{passive:!0})),(e=b.props.trigger,e.split(/\s+/).filter(Boolean)).forEach((function(e){if("manual"!==e)switch(z(e,$),e){case"mouseenter":z("mouseleave",Z);break;case"focus":z(wt?"focusout":"blur",K);break;case"focusin":z("focusout",K)}}))}function U(){m.forEach((function(e){var t=e.node,n=e.eventType,r=e.handler,i=e.options;t.removeEventListener(n,r,i)})),m=[]}function $(e){var t,n=!1;if(b.state.isEnabled&&!Y(e)&&!l){var r="focus"===(null==(t=o)?void 0:t.type);o=e,c=e.currentTarget,I(),!b.state.isVisible&&ct(e)&&Pt.forEach((function(t){return t(e)})),"click"===e.type&&(b.props.trigger.indexOf("mouseenter")<0||f)&&!1!==b.props.hideOnClick&&b.state.isVisible?n=!0:ee(e),"click"===e.type&&(f=!n),n&&!r&&te(e)}}function X(e){var t=e.target,n=j().contains(t)||w.contains(t);if("mousemove"!==e.type||!n){var r=Q().concat(w).map((function(e){var t,n=null==(t=e._tippy.popperInstance)?void 0:t.state;return n?{popperRect:e.getBoundingClientRect(),popperState:n,props:p}:null})).filter(Boolean);(function(e,t){var n=t.clientX,r=t.clientY;return e.every((function(e){var t=e.popperRect,i=e.popperState,o=e.props.interactiveBorder,a=rt(i.placement),s=i.modifiersData.offset;if(!s)return!0;var c="bottom"===a?s.top.y:0,u="top"===a?s.bottom.y:0,p="right"===a?s.left.x:0,f="left"===a?s.right.x:0,l=t.top-r+c>o,d=r-t.bottom-u>o,h=t.left-n+p>o,m=n-t.right-f>o;return l||d||h||m}))})(r,e)&&(M(),te(e))}}function Z(e){Y(e)||b.props.trigger.indexOf("click")>=0&&f||(b.props.interactive?b.hideWithInteractivity(e):te(e))}function K(e){b.props.trigger.indexOf("focusin")<0&&e.target!==j()||b.props.interactive&&e.relatedTarget&&w.contains(e.relatedTarget)||te(e)}function Y(e){return!!vt.isTouch&&_()!==e.type.indexOf("touch")>=0}function J(){G();var t=b.props,n=t.popperOptions,r=t.placement,i=t.offset,o=t.getReferenceClientRect,a=t.moveTransition,c=k()?Ct(w).arrow:null,u=o?{getBoundingClientRect:o,contextElement:o.contextElement||j()}:e,p={name:"$$tippy",enabled:!0,phase:"beforeWrite",requires:["computeStyles"],fn:function(e){var t=e.state;if(k()){var n=D().box;["placement","reference-hidden","escaped"].forEach((function(e){"placement"===e?n.setAttribute("data-placement",t.placement):t.attributes.popper["data-popper-"+e]?n.setAttribute("data-"+e,""):n.removeAttribute("data-"+e)})),t.attributes.popper={}}}},f=[{name:"offset",options:{offset:i}},{name:"preventOverflow",options:{padding:{top:2,bottom:2,left:5,right:5}}},{name:"flip",options:{padding:5}},{name:"computeStyles",options:{adaptive:!a}},p];k()&&c&&f.push({name:"arrow",options:{element:c,padding:3}}),f.push.apply(f,(null==n?void 0:n.modifiers)||[]),b.popperInstance=qe(u,w,Object.assign({},n,{placement:r,onFirstUpdate:s,modifiers:f}))}function G(){b.popperInstance&&(b.popperInstance.destroy(),b.popperInstance=null)}function Q(){return it(w.querySelectorAll("[data-tippy-root]"))}function ee(e){b.clearDelayTimeouts(),e&&F("onTrigger",[b,e]),W();var t=C(!0),r=T(),i=r[0],o=r[1];vt.isTouch&&"hold"===i&&o&&(t=o),t?n=setTimeout((function(){b.show()}),t):b.show()}function te(e){if(b.clearDelayTimeouts(),F("onUntrigger",[b,e]),b.state.isVisible){if(!(b.props.trigger.indexOf("mouseenter")>=0&&b.props.trigger.indexOf("click")>=0&&["mouseleave","mousemove"].indexOf(e.type)>=0&&f)){var t=C(!1);t?r=setTimeout((function(){b.state.isVisible&&b.hide()}),t):i=requestAnimationFrame((function(){b.hide()}))}}else N()}}function Rt(e,t){void 0===t&&(t={});var n=At.plugins.concat(t.plugins||[]);document.addEventListener("touchstart",yt,Ke),window.addEventListener("blur",xt);var r=Object.assign({},t,{plugins:n}),i=pt(e).reduce((function(e,t){var n=t&&Mt(t,r);return n&&e.push(n),e}),[]);return st(e)?i[0]:i}Rt.defaultProps=At,Rt.setDefaultProps=function(e){Object.keys(e).forEach((function(t){At[t]=e[t]}))},Rt.currentInput=vt;Object.assign({},_e,{effect:function(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow)}});Rt.setDefaultProps({render:St});var Ht=Rt;const Vt={init(){[...document.images].forEach((e=>{const t=e.parentNode;"PICTURE"===t.tagName?(this.processPicture(e,t),e.addEventListener("load",this.load.bind(this,e,t))):this.wrapImage(e)}));const e=document.querySelectorAll(".cld-tag");this.tippy(e)},load(e,t){t.querySelectorAll(".overlay-tag").forEach((e=>e.remove())),this.processPicture(e,t),this.tippy(t.querySelectorAll(".cld-tag"))},processPicture(e,t){const n=t.querySelectorAll("source");n.length>0&&n.forEach((t=>{[t.src,t.srcset].some((t=>t.includes(e.currentSrc)))&&([...e.attributes].forEach((t=>{t.name.startsWith("data-")&&e.removeAttribute(t.name)})),Object.assign(e.dataset,{...t.dataset}))})),this.wrapImage(e)},wrapImage(e){e.dataset.publicId?this.cldTag(e):this.wpTag(e)},createTag(e){const t=document.createElement("span");return t.classList.add("overlay-tag"),e.parentNode.insertBefore(t,e),t},cldTag(e){const t=this.createTag(e);t.template=this.createTemplate(e),t.innerText=L("Cloudinary","cloudinary"),t.classList.add("cld-tag")},wpTag(e){const t=this.createTag(e);t.innerText=L("WordPress","cloudinary"),t.classList.add("wp-tag")},createTemplate(e){const t=document.createElement("div");t.classList.add("cld-tag-info"),t.appendChild(this.makeLine(L("Local size","cloudinary"),e.dataset.filesize)),t.appendChild(this.makeLine(L("Optimized size","cloudinary"),e.dataset.optsize)),t.appendChild(this.makeLine(L("Optimized format","cloudinary"),e.dataset.optformat)),e.dataset.percent&&t.appendChild(this.makeLine(L("Reduction","cloudinary"),e.dataset.percent+"%")),t.appendChild(this.makeLine(L("Transformations","cloudinary"),e.dataset.transformations)),e.dataset.transformationCrop&&t.appendChild(this.makeLine(L("Crop transformations","cloudinary"),e.dataset.transformationCrop));const n=document.createElement("a");return n.classList.add("edit-link"),n.href=e.dataset.permalink,n.innerText=L("Edit asset","cloudinary"),t.appendChild(this.makeLine("","",n)),t},makeLine(e,t,n){const r=document.createElement("div"),i=document.createElement("span"),o=document.createElement("span");return i.innerText=e,i.classList.add("title"),o.innerText=t,n&&o.appendChild(n),r.appendChild(i),r.appendChild(o),r},tippy(e){Ht(e,{placement:"bottom-start",interactive:!0,appendTo:()=>document.body,aria:{content:"auto",expanded:"auto"},content:e=>e.template.innerHTML,allowHTML:!0})},debounce(e,t){let n=null;return(...r)=>{window.clearTimeout(n),n=window.setTimeout((()=>{e(...r)}),t)}}};window.addEventListener("load",(()=>Vt.init()))}()}();