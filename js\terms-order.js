!function(){"use strict";const t={template:"",tags:jQuery("#cld-tax-items"),tagDelimiter:window.tagsSuggestL10n&&window.tagsSuggestL10n.tagDelimiter||",",startId:null,_init(){if(!this.tags.length)return;const t=this;this._sortable(),"undefined"!=typeof wpAjax&&(wpAjax.procesParseAjaxResponse=wpAjax.parseAjaxResponse,wpAjax.parseAjaxResponse=function(e,a,s){const n=wpAjax.procesParseAjaxResponse(e,a,s);if(!n.errors&&n.responses[0]&&jQuery('[data-taxonomy="'+n.responses[0].what+'"]').length){const e=jQuery(n.responses[0].data).find("label").last().text().trim();t._pushItem(n.responses[0].what,e)}return n}),void 0!==window.tagBox&&(window.tagBox.processflushTags=window.tagBox.flushTags,window.tagBox.flushTags=function(e,a,s){if(void 0===s){const s=e.prop("id"),n=jQuery("input.newtag",e),i=(a=a||!1)?jQuery(a).text():n.val(),r=window.tagBox.clean(i).split(t.tagDelimiter);for(const e in r){const a=s+":"+r[e];jQuery('[data-item="'+a+'"]').length||t._pushItem(a,r[e])}}return this.processflushTags(e,a,s)},window.tagBox.processTags=window.tagBox.parseTags,window.tagBox.parseTags=function(e){const a=e.id,s=a.split("-check-num-")[1],n=a.split("-check-num-")[0],i=jQuery(e).closest(".tagsdiv").find(".the-tags"),r=window.tagBox.clean(i.val()).split(t.tagDelimiter)[s];(new wp.api.collections.Tags).fetch({data:{slug:r}}).done((a=>{const s=!!a.length&&jQuery('[data-item="'+n+":"+a[0].id+'"]');s.length?s.remove():(jQuery(`.cld-tax-order-list-item:contains(${r})`).remove(),--t.startId),this.processTags(e)}))}),jQuery("body").on("change",".selectit input",(function(){const e=jQuery(this),a=e.val(),s=e.is(":checked"),n=e.parent().text().trim();!0===s?t.tags.find(`[data-item="category:${a}"]`).length||t._pushItem(`category:${a}`,n):t.tags.find(`[data-item="category:${a}"]`).remove()}))},_createItem(t,e){const a=jQuery("<li/>"),s=jQuery("<span/>"),n=jQuery("<input/>");return a.addClass("cld-tax-order-list-item").attr("data-item",t),n.addClass("cld-tax-order-list-item-input").attr("type","hidden").attr("name","cld_tax_order[]").val(t),s.addClass("dashicons dashicons-menu cld-tax-order-list-item-handle"),a.append(s).append(e).append(n),a},_pushItem(t,e){const a=this._createItem(t,e);this.tags.append(a)},_sortable(){jQuery(".cld-tax-order-list").sortable({connectWith:".cld-tax-order",axis:"y",handle:".cld-tax-order-list-item-handle",placeholder:"cld-tax-order-list-item-placeholder",forcePlaceholderSize:!0,helper:"clone"})}};void 0!==window.CLDN&&(t._init(),jQuery("[data-wp-lists] .selectit input[checked]").each(((t,e)=>{jQuery(e).trigger("change")})))}();