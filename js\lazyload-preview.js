!function(){var e={616:function(e){e.exports=function(e,t){var r,n,i=0;function o(){var o,s,a=r,l=arguments.length;e:for(;a;){if(a.args.length===arguments.length){for(s=0;s<l;s++)if(a.args[s]!==arguments[s]){a=a.next;continue e}return a!==r&&(a===n&&(n=a.prev),a.prev.next=a.next,a.next&&(a.next.prev=a.prev),a.next=r,a.prev=null,r.prev=a,r=a),a.val}a=a.next}for(o=new Array(l),s=0;s<l;s++)o[s]=arguments[s];return a={args:o,val:e.apply(null,o)},r?(r.prev=a,a.next=r):n=a,i===t.maxSize?(n=n.prev).next=null:i++,r=a,a.val}return t=t||{},o.clear=function(){r=null,n=null,i=0},o}},604:function(e,t,r){var n;!function(){"use strict";var i={not_string:/[^s]/,not_bool:/[^t]/,not_type:/[^T]/,not_primitive:/[^v]/,number:/[diefg]/,numeric_arg:/[bcdiefguxX]/,json:/[j]/,not_json:/[^j]/,text:/^[^\x25]+/,modulo:/^\x25{2}/,placeholder:/^\x25(?:([1-9]\d*)\$|\(([^)]+)\))?(\+)?(0|'[^$])?(-)?(\d+)?(?:\.(\d+))?([b-gijostTuvxX])/,key:/^([a-z_][a-z_\d]*)/i,key_access:/^\.([a-z_][a-z_\d]*)/i,index_access:/^\[(\d+)\]/,sign:/^[+-]/};function o(e){return function(e,t){var r,n,s,a,l,c,u,h,d,p=1,f=e.length,g="";for(n=0;n<f;n++)if("string"==typeof e[n])g+=e[n];else if("object"==typeof e[n]){if((a=e[n]).keys)for(r=t[p],s=0;s<a.keys.length;s++){if(null==r)throw new Error(o('[sprintf] Cannot access property "%s" of undefined value "%s"',a.keys[s],a.keys[s-1]));r=r[a.keys[s]]}else r=a.param_no?t[a.param_no]:t[p++];if(i.not_type.test(a.type)&&i.not_primitive.test(a.type)&&r instanceof Function&&(r=r()),i.numeric_arg.test(a.type)&&"number"!=typeof r&&isNaN(r))throw new TypeError(o("[sprintf] expecting number but found %T",r));switch(i.number.test(a.type)&&(h=r>=0),a.type){case"b":r=parseInt(r,10).toString(2);break;case"c":r=String.fromCharCode(parseInt(r,10));break;case"d":case"i":r=parseInt(r,10);break;case"j":r=JSON.stringify(r,null,a.width?parseInt(a.width):0);break;case"e":r=a.precision?parseFloat(r).toExponential(a.precision):parseFloat(r).toExponential();break;case"f":r=a.precision?parseFloat(r).toFixed(a.precision):parseFloat(r);break;case"g":r=a.precision?String(Number(r.toPrecision(a.precision))):parseFloat(r);break;case"o":r=(parseInt(r,10)>>>0).toString(8);break;case"s":r=String(r),r=a.precision?r.substring(0,a.precision):r;break;case"t":r=String(!!r),r=a.precision?r.substring(0,a.precision):r;break;case"T":r=Object.prototype.toString.call(r).slice(8,-1).toLowerCase(),r=a.precision?r.substring(0,a.precision):r;break;case"u":r=parseInt(r,10)>>>0;break;case"v":r=r.valueOf(),r=a.precision?r.substring(0,a.precision):r;break;case"x":r=(parseInt(r,10)>>>0).toString(16);break;case"X":r=(parseInt(r,10)>>>0).toString(16).toUpperCase()}i.json.test(a.type)?g+=r:(!i.number.test(a.type)||h&&!a.sign?d="":(d=h?"+":"-",r=r.toString().replace(i.sign,"")),c=a.pad_char?"0"===a.pad_char?"0":a.pad_char.charAt(1):" ",u=a.width-(d+r).length,l=a.width&&u>0?c.repeat(u):"",g+=a.align?d+r+l:"0"===c?d+l+r:l+d+r)}return g}(function(e){if(a[e])return a[e];var t,r=e,n=[],o=0;for(;r;){if(null!==(t=i.text.exec(r)))n.push(t[0]);else if(null!==(t=i.modulo.exec(r)))n.push("%");else{if(null===(t=i.placeholder.exec(r)))throw new SyntaxError("[sprintf] unexpected placeholder");if(t[2]){o|=1;var s=[],l=t[2],c=[];if(null===(c=i.key.exec(l)))throw new SyntaxError("[sprintf] failed to parse named argument key");for(s.push(c[1]);""!==(l=l.substring(c[0].length));)if(null!==(c=i.key_access.exec(l)))s.push(c[1]);else{if(null===(c=i.index_access.exec(l)))throw new SyntaxError("[sprintf] failed to parse named argument key");s.push(c[1])}t[2]=s}else o|=2;if(3===o)throw new Error("[sprintf] mixing positional and named placeholders is not (yet) supported");n.push({placeholder:t[0],param_no:t[1],keys:t[2],sign:t[3],pad_char:t[4],align:t[5],width:t[6],precision:t[7],type:t[8]})}r=r.substring(t[0].length)}return a[e]=n}(e),arguments)}function s(e,t){return o.apply(null,[e].concat(t||[]))}var a=Object.create(null);o,s,"undefined"!=typeof window&&(window.sprintf=o,window.vsprintf=s,void 0===(n=function(){return{sprintf:o,vsprintf:s}}.call(t,r,t,e))||(e.exports=n))}()}},t={};function r(n){var i=t[n];if(void 0!==i)return i.exports;var o=t[n]={exports:{}};return e[n](o,o.exports,r),o.exports}r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,{a:t}),t},r.d=function(e,t){for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},function(){"use strict";var e,t,n,i,o=r(616),s=r.n(o);r(604),s()(console.error);function a(e){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},a(e)}function l(e){var t=function(e,t){if("object"!=a(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=a(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==a(t)?t:t+""}e={"(":9,"!":8,"*":7,"/":7,"%":7,"+":6,"-":6,"<":5,"<=":5,">":5,">=":5,"==":4,"!=":4,"&&":3,"||":2,"?":1,"?:":1},t=["(","?"],n={")":["("],":":["?","?:"]},i=/<=|>=|==|!=|&&|\|\||\?:|\(|!|\*|\/|%|\+|-|<|>|\?|\)|:/;var c={"!":function(e){return!e},"*":function(e,t){return e*t},"/":function(e,t){return e/t},"%":function(e,t){return e%t},"+":function(e,t){return e+t},"-":function(e,t){return e-t},"<":function(e,t){return e<t},"<=":function(e,t){return e<=t},">":function(e,t){return e>t},">=":function(e,t){return e>=t},"==":function(e,t){return e===t},"!=":function(e,t){return e!==t},"&&":function(e,t){return e&&t},"||":function(e,t){return e||t},"?:":function(e,t,r){if(e)throw t;return r}};function u(r){var o=function(r){for(var o,s,a,l,c=[],u=[];o=r.match(i);){for(s=o[0],(a=r.substr(0,o.index).trim())&&c.push(a);l=u.pop();){if(n[s]){if(n[s][0]===l){s=n[s][1]||s;break}}else if(t.indexOf(l)>=0||e[l]<e[s]){u.push(l);break}c.push(l)}n[s]||u.push(s),r=r.substr(o.index+s.length)}return(r=r.trim())&&c.push(r),c.concat(u.reverse())}(r);return function(e){return function(e,t){var r,n,i,o,s,a,l=[];for(r=0;r<e.length;r++){if(s=e[r],o=c[s]){for(n=o.length,i=Array(n);n--;)i[n]=l.pop();try{a=o.apply(null,i)}catch(e){return e}}else a=t.hasOwnProperty(s)?t[s]:+s;l.push(a)}return l[0]}(o,e)}}var h={contextDelimiter:"",onMissingKey:null};function d(e,t){var r;for(r in this.data=e,this.pluralForms={},this.options={},h)this.options[r]=void 0!==t&&r in t?t[r]:h[r]}function p(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function f(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?p(Object(r),!0).forEach((function(t){var n,i,o;n=e,i=t,o=r[t],(i=l(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):p(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}d.prototype.getPluralForm=function(e,t){var r,n,i,o=this.pluralForms[e];return o||("function"!=typeof(i=(r=this.data[e][""])["Plural-Forms"]||r["plural-forms"]||r.plural_forms)&&(n=function(e){var t,r,n;for(t=e.split(";"),r=0;r<t.length;r++)if(0===(n=t[r].trim()).indexOf("plural="))return n.substr(7)}(r["Plural-Forms"]||r["plural-forms"]||r.plural_forms),i=function(e){var t=u(e);return function(e){return+t({n:e})}}(n)),o=this.pluralForms[e]=i),o(t)},d.prototype.dcnpgettext=function(e,t,r,n,i){var o,s,a;return o=void 0===i?0:this.getPluralForm(e,i),s=r,t&&(s=t+this.options.contextDelimiter+r),(a=this.data[e][s])&&a[o]?a[o]:(this.options.onMissingKey&&this.options.onMissingKey(r,e),0===o?r:n)};var g={"":{plural_forms:function(e){return 1===e?0:1}}},y=/^i18n\.(n?gettext|has_translation)(_|$)/;var m=function(e){return"string"!=typeof e||""===e?(console.error("The namespace must be a non-empty string."),!1):!!/^[a-zA-Z][a-zA-Z0-9_.\-\/]*$/.test(e)||(console.error("The namespace can only contain numbers, letters, dashes, periods, underscores and slashes."),!1)};var v=function(e){return"string"!=typeof e||""===e?(console.error("The hook name must be a non-empty string."),!1):/^__/.test(e)?(console.error("The hook name cannot begin with `__`."),!1):!!/^[a-zA-Z][a-zA-Z0-9_.-]*$/.test(e)||(console.error("The hook name can only contain numbers, letters, dashes, periods and underscores."),!1)};var b=function(e,t){return function(r,n,i){var o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:10,s=e[t];if(v(r)&&m(n))if("function"==typeof i)if("number"==typeof o){var a={callback:i,priority:o,namespace:n};if(s[r]){var l,c=s[r].handlers;for(l=c.length;l>0&&!(o>=c[l-1].priority);l--);l===c.length?c[l]=a:c.splice(l,0,a),s.__current.forEach((function(e){e.name===r&&e.currentIndex>=l&&e.currentIndex++}))}else s[r]={handlers:[a],runs:0};"hookAdded"!==r&&e.doAction("hookAdded",r,n,i,o)}else console.error("If specified, the hook priority must be a number.");else console.error("The hook callback must be a function.")}};var _=function(e,t){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return function(n,i){var o=e[t];if(v(n)&&(r||m(i))){if(!o[n])return 0;var s=0;if(r)s=o[n].handlers.length,o[n]={runs:o[n].runs,handlers:[]};else for(var a=o[n].handlers,l=function(e){a[e].namespace===i&&(a.splice(e,1),s++,o.__current.forEach((function(t){t.name===n&&t.currentIndex>=e&&t.currentIndex--})))},c=a.length-1;c>=0;c--)l(c);return"hookRemoved"!==n&&e.doAction("hookRemoved",n,i),s}}};var x=function(e,t){return function(r,n){var i=e[t];return void 0!==n?r in i&&i[r].handlers.some((function(e){return e.namespace===n})):r in i}};var w=function(e,t){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return function(n){var i=e[t];i[n]||(i[n]={handlers:[],runs:0}),i[n].runs++;var o=i[n].handlers;for(var s=arguments.length,a=new Array(s>1?s-1:0),l=1;l<s;l++)a[l-1]=arguments[l];if(!o||!o.length)return r?a[0]:void 0;var c={name:n,currentIndex:0};for(i.__current.push(c);c.currentIndex<o.length;){var u=o[c.currentIndex].callback.apply(null,a);r&&(a[0]=u),c.currentIndex++}return i.__current.pop(),r?a[0]:void 0}};var P=function(e,t){return function(){var r,n,i=e[t];return null!==(r=null===(n=i.__current[i.__current.length-1])||void 0===n?void 0:n.name)&&void 0!==r?r:null}};var k=function(e,t){return function(r){var n=e[t];return void 0===r?void 0!==n.__current[0]:!!n.__current[0]&&r===n.__current[0].name}};var F=function(e,t){return function(r){var n=e[t];if(v(r))return n[r]&&n[r].runs?n[r].runs:0}},T=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.actions=Object.create(null),this.actions.__current=[],this.filters=Object.create(null),this.filters.__current=[],this.addAction=b(this,"actions"),this.addFilter=b(this,"filters"),this.removeAction=_(this,"actions"),this.removeFilter=_(this,"filters"),this.hasAction=x(this,"actions"),this.hasFilter=x(this,"filters"),this.removeAllActions=_(this,"actions",!0),this.removeAllFilters=_(this,"filters",!0),this.doAction=w(this,"actions"),this.applyFilters=w(this,"filters",!0),this.currentAction=P(this,"actions"),this.currentFilter=P(this,"filters"),this.doingAction=k(this,"actions"),this.doingFilter=k(this,"filters"),this.didAction=F(this,"actions"),this.didFilter=F(this,"filters")};var A=function(){return new T}(),S=(A.addAction,A.addFilter,A.removeAction,A.removeFilter,A.hasAction,A.hasFilter,A.removeAllActions,A.removeAllFilters,A.doAction,A.applyFilters,A.currentAction,A.currentFilter,A.doingAction,A.doingFilter,A.didAction,A.didFilter,A.actions,A.filters,function(e,t,r){var n=new d({}),i=new Set,o=function(){i.forEach((function(e){return e()}))},s=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"default";n.data[t]=f(f(f({},g),n.data[t]),e),n.data[t][""]=f(f({},g[""]),n.data[t][""])},a=function(e,t){s(e,t),o()},l=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"default",t=arguments.length>1?arguments[1]:void 0,r=arguments.length>2?arguments[2]:void 0,i=arguments.length>3?arguments[3]:void 0,o=arguments.length>4?arguments[4]:void 0;return n.data[e]||s(void 0,e),n.dcnpgettext(e,t,r,i,o)},c=function(){return arguments.length>0&&void 0!==arguments[0]?arguments[0]:"default"},u=function(e,t,n){var i=l(n,t,e);return r?(i=r.applyFilters("i18n.gettext_with_context",i,e,t,n),r.applyFilters("i18n.gettext_with_context_"+c(n),i,e,t,n)):i};if(e&&a(e,t),r){var h=function(e){y.test(e)&&o()};r.addAction("hookAdded","core/i18n",h),r.addAction("hookRemoved","core/i18n",h)}return{getLocaleData:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"default";return n.data[e]},setLocaleData:a,resetLocaleData:function(e,t){n.data={},n.pluralForms={},a(e,t)},subscribe:function(e){return i.add(e),function(){return i.delete(e)}},__:function(e,t){var n=l(t,void 0,e);return r?(n=r.applyFilters("i18n.gettext",n,e,t),r.applyFilters("i18n.gettext_"+c(t),n,e,t)):n},_x:u,_n:function(e,t,n,i){var o=l(i,void 0,e,t,n);return r?(o=r.applyFilters("i18n.ngettext",o,e,t,n,i),r.applyFilters("i18n.ngettext_"+c(i),o,e,t,n,i)):o},_nx:function(e,t,n,i,o){var s=l(o,i,e,t,n);return r?(s=r.applyFilters("i18n.ngettext_with_context",s,e,t,n,i,o),r.applyFilters("i18n.ngettext_with_context_"+c(o),s,e,t,n,i,o)):s},isRTL:function(){return"rtl"===u("ltr","text direction")},hasTranslation:function(e,t,i){var o,s,a=t?t+""+e:e,l=!(null===(o=n.data)||void 0===o||null===(s=o[null!=i?i:"default"])||void 0===s||!s[a]);return r&&(l=r.applyFilters("i18n.has_translation",l,e,t,i),l=r.applyFilters("i18n.has_translation_"+c(i),l,e,t,i)),l}}}(void 0,void 0,A));S.getLocaleData.bind(S),S.setLocaleData.bind(S),S.resetLocaleData.bind(S),S.subscribe.bind(S),S.__.bind(S),S._x.bind(S),S._n.bind(S),S._nx.bind(S),S.isRTL.bind(S),S.hasTranslation.bind(S);const E={cycleTime:2e3,animate:document.getElementById("lazy_loading.lazy_animate"),image:document.getElementById("lazyload-image"),placeHolders:document.querySelectorAll('[name="lazy_loading[lazy_placeholder]"]'),preloader:document.getElementById("preloader-image"),color:document.getElementById("lazy_loading.lazy_custom_color"),previewCycle:document.getElementById("preview-cycle"),progress:document.getElementById("progress-bar"),threshold:document.getElementById("lazy_loading.lazy_threshold"),currentPlaceholder:null,svg:null,running:!1,init(){this.svg=this.image.dataset.svg,this.currentPlaceholder=document.getElementById("placeholder-"+this.getPlaceholder()),[...this.placeHolders].forEach((e=>{e.addEventListener("change",(()=>this.changePlaceholder(e.value)))})),this.color.addEventListener("input",(()=>this.changePreloader())),this.animate.addEventListener("change",(()=>this.changePreloader())),this.previewCycle.addEventListener("click",(()=>this.startCycle()))},getPlaceholder:()=>document.querySelector('[name="lazy_loading[lazy_placeholder]"]:checked').value,changePreloader(){this.preloader.src=this.getSVG()},changePlaceholder(e){const t=document.getElementById("placeholder-"+e);this.currentPlaceholder&&(this.currentPlaceholder.style.display="none",this.currentPlaceholder.style.width="85%",this.currentPlaceholder.style.boxShadow="",this.currentPlaceholder.style.bottom="0"),t&&(t.style.display=""),this.currentPlaceholder=t},getThreshold(){return parseInt(this.threshold.value)+this.image.parentNode.parentNode.offsetHeight},startCycle(){this.running?this.endCycle():(this.changePlaceholder("none"),this.image.parentNode.parentNode.style.overflowY="scroll",this.image.parentNode.style.visibility="hidden",this.image.parentNode.style.width="100%",this.image.parentNode.style.boxShadow="none",this.progress.style.width="100%",this.preloader.parentNode.style.visibility="hidden",this.running=setTimeout((()=>{this.progress.style.visibility="hidden",this.progress.style.width="0%",this.preloader.parentNode.style.visibility="",setTimeout((()=>{const e=this.getThreshold();this.image.parentNode.style.visibility="",this.preloader.parentNode.style.bottom="-"+e+"px",setTimeout((()=>{setTimeout((()=>{this.image.parentNode.parentNode.scrollTo({top:e,behavior:"smooth"}),this.showPlaceholder()}),this.cycleTime/3)}),this.cycleTime/2)}),this.cycleTime/2)}),this.cycleTime/2))},showPlaceholder(){const e=this.getPlaceholder(),t=this.getThreshold();"off"!==e&&(this.changePlaceholder(e),this.currentPlaceholder&&(this.currentPlaceholder.style.width="100%",this.currentPlaceholder.style.boxShadow="none",this.currentPlaceholder.style.bottom="-"+t+"px")),setTimeout((()=>{this.showImage()}),this.cycleTime/2)},showImage(){const e=this.getThreshold();this.changePlaceholder("none"),this.image.parentNode.style.bottom="-"+e+"px",this.image.parentNode.style.visibility="",setTimeout((()=>{this.endCycle()}),this.cycleTime)},endCycle(){clearTimeout(this.running),this.running=!1,this.changePlaceholder(this.getPlaceholder()),this.image.parentNode.style.visibility="",this.image.parentNode.style.bottom="0",this.image.parentNode.style.width="65%",this.image.parentNode.style.boxShadow="",this.preloader.parentNode.style.bottom="0",this.image.parentNode.parentNode.style.overflowY="",this.progress.style.visibility=""},getSVG(){let e=this.color.value;const t=[e];if(this.animate.checked){const r=[...e.matchAll(new RegExp(/[\d+\.*]+/g))];r[3]=.1,t.push("rgba("+r.join(",")+")"),t.push(e)}return this.svg.replace("-color-",t.join(";"))},showLoader(){this.image.parentNode.style.opacity=1,this.image.parentNode.src=this.getSVG(),setTimeout((()=>{this.showPlaceholder(this.image.parentNode.dataset.placeholder)}),this.cycleTime)}};window.addEventListener("load",(()=>E.init()))}()}();