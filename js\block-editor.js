!function(){"use strict";var e={n:function(t){var r=t&&t.__esModule?function(){return t.default}:function(){return t};return e.d(r,{a:r}),r},d:function(t,r){for(var o in r)e.o(r,o)&&!e.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:r[o]})},o:function(e,t){return Object.prototype.hasOwnProperty.call(e,t)}},t=window.wp.apiFetch,r=e.n(t),o=window.React,i=window.wp.i18n,a=window.wp.data,n=window.wp.components;const l={_init(){"undefined"!=typeof CLD_VIDEO_PLAYER&&wp.hooks.addFilter("blocks.registerBlockType","Cloudinary/Media/Video",(function(e,t){return"core/video"===t&&("off"!==CLD_VIDEO_PLAYER.video_autoplay_mode&&(e.attributes.autoplay.default=!0),"on"===CLD_VIDEO_PLAYER.video_loop&&(e.attributes.loop.default=!0),"off"===CLD_VIDEO_PLAYER.video_controls&&(e.attributes.controls.default=!1)),e}))}};l._init();wp.hooks.addFilter("blocks.registerBlockType","cloudinary/addAttributes",(function(e,t){return"core/image"!==t&&"core/video"!==t||(e.attributes||(e.attributes={}),e.attributes.overwrite_transformations={type:"boolean"},e.attributes.transformations={type:"boolean"}),e}));const s=e=>{const{attributes:{overwrite_transformations:t},setAttributes:r}=e;return(0,o.createElement)(n.PanelBody,{title:(0,i.__)("Transformations","cloudinary")},(0,o.createElement)(n.ToggleControl,{label:(0,i.__)("Overwrite Global Transformations","cloudinary"),checked:t,onChange:e=>{r({overwrite_transformations:e})}}))};let d=e=>{const{setAttributes:t,media:r}=e,{InspectorControls:i}=wp.editor;return r&&r.transformations&&t({transformations:!0}),(0,o.createElement)(i,null,(0,o.createElement)(s,{...e}))};d=(0,a.withSelect)(((e,t)=>({...t,media:t.attributes.id?e("core").getMedia(t.attributes.id):null})))(d);wp.hooks.addFilter("editor.BlockEdit","cloudinary/filterEdit",(e=>t=>{const{name:r}=t,i="core/image"===r||"core/video"===r;return(0,o.createElement)(o.Fragment,null,i?(0,o.createElement)(d,{...t}):null,(0,o.createElement)(e,{...t}))}),20);let c=e=>(0,o.createElement)(o.Fragment,null,e.modalClass&&(0,o.createElement)(n.ToggleControl,{label:(0,i.__)("Overwrite Cloudinary Global Transformations","cloudinary"),checked:e.overwrite_featured_transformations,onChange:t=>e.setOverwrite(t),className:"cloudinary-overwrite-transformations"}));c=(0,a.withSelect)((e=>{var t;return{overwrite_featured_transformations:null!==(t=e("core/editor")?.getEditedPostAttribute("meta")._cloudinary_featured_overwrite)&&void 0!==t&&t}}))(c),c=(0,a.withDispatch)((e=>({setOverwrite:t=>{e("core/editor").editPost({meta:{_cloudinary_featured_overwrite:t}})}})))(c);const u=e=>class extends e{render(){return(0,o.createElement)(o.Fragment,null,super.render(),!!this.props.value&&(0,o.createElement)(c,{...this.props}))}},m={_init(){wp.hooks.addFilter("editor.MediaUpload","cloudinary/filter-featured-image",u)}};m._init();const p={wrapper:null,query:{per_page:-1,orderby:"name",order:"asc",_fields:"id,name,parent",context:"view"},available:{},taxonomies:null,fetchWait:null,_init(){if(this.wrapper=document.getElementById("cld-tax-items"),!this.wrapper)return;const{getTaxonomies:e}=(0,a.select)("core");this.fetchWait=setInterval((()=>{this.taxonomies=e(),this.taxonomies&&(clearInterval(this.fetchWait),this._init_listeners())}),1e3)},_init_listeners(){this.taxonomies.forEach((e=>{e.rest_base&&e.visibility.public&&(0,a.subscribe)((()=>{const t=e.slug,r=e.hierarchical,{isResolving:o}=(0,a.select)("core/data"),i=["taxonomy",t,this.query];this.available[t]=null,r&&(this.available[t]=(0,a.select)("core").getEntityRecords("taxonomy",t,this.query)),o("core","getEntityRecords",i)||this.event(e)}))}))},event(e){const t=(0,a.select)("core/editor").getEditedPostAttribute(e.rest_base);if(!t)return;const r=[...t],o=Array.from(this.wrapper.querySelectorAll(`[data-item*="${e.slug}"]`));[...r].forEach((t=>{const r=this.wrapper.querySelector(`[data-item="${e.slug}:${t}"]`);o.splice(o.indexOf(r),1),null===r&&this.createItem(this.getItem(e,t))})),o.forEach((e=>{e.parentNode.removeChild(e)}))},createItem(e){if(!e||!e.id)return;const t=document.createElement("li"),r=document.createElement("span"),o=document.createElement("input"),i=document.createTextNode(e.name);t.classList.add("cld-tax-order-list-item"),t.dataset.item=`${e.taxonomy}:${e.id}`,o.classList.add("cld-tax-order-list-item-input"),o.type="hidden",o.name="cld_tax_order[]",o.value=`${e.taxonomy}:${e.id}`,r.className="dashicons dashicons-menu cld-tax-order-list-item-handle",t.appendChild(r),t.appendChild(o),t.appendChild(i),this.wrapper.appendChild(t)},getItem(e,t){let r={};if(null===this.available[e.slug])r=(0,a.select)("core").getEntityRecord("taxonomy",e.slug,t);else for(const o of this.available[e.slug])if(o.id===t){r=o,r.taxonomy=e.slug;break}return r}};window.addEventListener("load",(()=>p._init()));window.$=window.jQuery,r().use(((e,t)=>{if("cors"===e.mode)return t(e);if(e.url)try{const r=new URL(e.url,location.href);if(r.protocol!==location.protocol||r.hostname!==location.hostname||r.port!==location.port)return t(e)}catch{return t(e)}return e.headers||(e.headers={}),e.headers["x-cld-fetch-from-editor"]="true",t(e)}))}();