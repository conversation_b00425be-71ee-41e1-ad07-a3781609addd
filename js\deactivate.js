!function(){"use strict";const t={modal:document.getElementById("cloudinary-deactivation"),modalBody:document.getElementById("modal-body"),modalFooter:document.getElementById("modal-footer"),modalUninstall:document.getElementById("modal-uninstall"),modalClose:document.querySelectorAll('button[data-action="cancel"], button[data-action="close"]'),pluginListLinks:document.querySelectorAll(".cld-deactivate-link, .cld-deactivate"),triggers:document.getElementsByClassName("cld-deactivate"),options:document.querySelectorAll('.cloudinary-deactivation .reasons input[type="radio"]'),report:document.getElementById("cld-report"),contact:document.getElementById("cld-contact"),submitButton:document.querySelectorAll('.cloudinary-deactivation button[data-action="submit"]'),contactButton:document.querySelectorAll('.cloudinary-deactivation button[data-action="contact"]'),deactivateButton:document.querySelectorAll('.cloudinary-deactivation button[data-action="deactivate"]'),emailField:document.getElementById("email"),reason:"",more:null,deactivationUrl:"",email:"",isCloudinaryOnly:!1,addEvents(){const t=this;if([...t.modalClose].forEach((e=>{e.addEventListener("click",(e=>{t.closeModal()}))})),window.addEventListener("keyup",(e=>{"visible"===t.modal.style.visibility&&"Escape"===e.key&&(t.modal.style.visibility="hidden",t.modal.style.opacity="0")})),t.modal.addEventListener("click",(e=>{e.stopPropagation(),e.target===t.modal&&t.closeModal()})),[...t.pluginListLinks].forEach((e=>{e.addEventListener("click",(function(e){e.preventDefault(),t.deactivationUrl=e.target.getAttribute("href"),t.openModal()}))})),[...t.contactButton].forEach((e=>{e.addEventListener("click",(function(){t.emailField&&(t.email=t.emailField.value),t.submit()}))})),[...t.deactivateButton].forEach((e=>{e.addEventListener("click",(function(){window.location.href=t.deactivationUrl}))})),[...t.options].forEach((e=>{e.addEventListener("change",(function(e){t.reason=e.target.value,t.more=e.target.parentNode.querySelector("textarea")}))})),t.contact&&t.report.addEventListener("change",(function(){t.report.checked?t.contact.parentNode.removeAttribute("style"):t.contact.parentNode.style.display="none"})),[...t.submitButton].forEach((e=>{e.addEventListener("click",(function(){const e=document.querySelector('.cloudinary-deactivation .data input[name="option"]:checked');let o="";e&&(o=e.value),"uninstall"===o&&(t.modalBody.style.display="none",t.modalFooter.style.display="none",t.modalUninstall.style.display="block"),t.submit(o)}))})),this.isCloudinaryOnly){const t=document.getElementById("cld-bypass-cloudinary-only");t.addEventListener("change",function(e){this.modal.dataset.cloudinaryOnly=!t.checked}.bind(this))}},closeModal(){document.body.style.removeProperty("overflow"),this.modal.style.visibility="hidden",this.modal.style.opacity="0"},openModal(){document.body.style.overflow="hidden",this.modal.style.visibility="visible",this.modal.style.opacity="1"},submit(t=""){wp.ajax.send({url:CLD_Deactivate.endpoint,data:{reason:this.reason,more:this.more?.value,report:this.report?.checked,contact:this.contact?.checked,email:this.email,dataHandling:t},beforeSend(t){t.setRequestHeader("X-WP-Nonce",CLD_Deactivate.nonce)}}).always((function(){window.location.reload()}))},init(){this.isCloudinaryOnly=!!this.modal.dataset.cloudinaryOnly,this.addEvents()}};t.init()}();