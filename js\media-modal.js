!function(){if(wp.media){wp.media.events.on("editor:image-edit",(function(e){e.metadata.cldoverwrite=null;e.image.className.split(" ").indexOf("cld-overwrite")>=0&&(e.metadata.cldoverwrite="true")})),wp.media.events.on("editor:image-update",(function(e){const t=e.image.className.split(" ");e.metadata.cldoverwrite&&-1===t.indexOf("cld-overwrite")?t.push("cld-overwrite"):!e.metadata.cldoverwrite&&t.indexOf("cld-overwrite")>=0&&delete t[t.indexOf("cld-overwrite")],e.image.className=t.join(" ")}));let e=null;const t=wp.media.string.props;wp.media.string.props=function(i,a){i.cldoverwrite&&(i.classes=["cld-overwrite"],e=!0);return t(i,a)},wp.media.post=function(t,i){if("send-attachment-to-editor"===t){const t=wp?.media?.editor?.get()?.state(),a=t?.get("selection")?.get(i.attachment);a?.attributes?.transformations&&(i.attachment.transformations=a.attributes.transformations),(i?.html.indexOf("cld-overwrite")>-1||!0===e)&&(i.attachment.cldoverwrite=!0,e=null)}return wp.ajax.post(t,i)};const i=wp.media.view.MediaFrame.Select,a=wp.media.view.MediaFrame.Post,n=wp.media.view.MediaFrame.ImageDetails,o=wp.media.view.MediaFrame.VideoDetails,r=wp.media.view.Settings.AttachmentDisplay,s=wp.media.View.extend({tagName:"div",className:"cloudinary-widget",template:wp.template("cloudinary-dam"),active:!1,toolbar:null,frame:null,ready(){const e=this.controller,t=this.model.get("selection"),i=this.model.get("library"),a=wp.media.model.Attachment;if(CLDN.mloptions.multiple=e.options.multiple,this.cid!==this.active){if(CLDN.mloptions.inline_container="#cloudinary-dam-"+e.cid,1===t.length){const e=a.get(t.models[0].id);void 0!==e.attributes.public_id&&(CLDN.mloptions.asset={resource_id:e.attributes.public_id})}else CLDN.mloptions.asset=null;try{CLDN.mloptions.folder||(CLDN.mloptions.folder={path:""});const e=t.props.attributes.type;CLDN.mloptions.folder.resource_type=Array.isArray(e)?e[0]:e}catch(e){}window.ml=cloudinary.openMediaLibrary(CLDN.mloptions,{insertHandler(n){for(let o=0;o<n.assets.length;o++){const r=n.assets[o];wp.media.post("cloudinary-down-sync",{nonce:CLDN.nonce,asset:r}).done((function(n){const o=function(e,t){e.uploading=!1,t.set(e),wp.Uploader.queue.remove(t),0===wp.Uploader.queue.length&&wp.Uploader.queue.reset()};if(void 0!==n.resync&&n.resync.forEach((function(e){a.get(e.id).set(e)})),void 0!==n.fetch){const e=a.get(n.attachment_id);e.set(n),i.add(e),wp.Uploader.queue.add(e),wp.ajax.send({url:n.fetch,beforeSend(e){e.setRequestHeader("X-WP-Nonce",CLDN.nonce)},data:{src:n.url,filename:n.filename,attachment_id:n.attachment_id,transformations:n.transformations}}).done((function(e){const t=a.get(e.id);o(e,t)})).fail((function(a){o(n,e),i.remove(e),t.remove(e),"string"==typeof a?alert(a):500===a.status&&alert("HTTP error.")}))}else{const e=a.get(n.id);e.set(n),t.add(e)}0===wp.Uploader.queue.length&&wp.Uploader.queue.reset(),e.content.mode("browse")}))}}},document.querySelectorAll(".dam-cloudinary")[0])}return this.active=this.cid,this}}),d=function(e){const t={bindHandlers(){e.prototype.bindHandlers.apply(this,arguments),this.on("content:render:cloudinary",this.cloudinaryContent,this)},browseRouter(t){e.prototype.browseRouter.apply(this,arguments),t.set({cloudinary:{text:"Cloudinary",priority:60}})},cloudinaryContent(){this.$el.addClass("hide-toolbar");const e=this.state(),t=new s({controller:this,model:e}).render();this.content.set(t)}};return t},l=function(e){return{initialize(){e.prototype.initialize.apply(this,arguments),this.listenTo(this.model,"change:cldoverwrite",this.handleOverwrite)},handleOverwrite(e){const t=this.options.attachment.attributes.sizes;for(const i in t){const a=new URL(t[i].url);e.attributes.cldoverwrite?a.searchParams.set("cld_overwrite",!0):a.searchParams.delete("cld_overwrite"),this.options.attachment.attributes.sizes[i].url=a.href}}}};wp.media.view.MediaFrame.Select=i.extend(d(i)),wp.media.view.MediaFrame.Post=a.extend(d(a)),wp.media.view.MediaFrame.ImageDetails=n.extend(d(n)),wp.media.view.MediaFrame.VideoDetails=o.extend(d(o)),wp.media.view.Settings.AttachmentDisplay=r.extend(l(r))}}();